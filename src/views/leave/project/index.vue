<template>
  <ele-page hide-footer flex-table>
    <ele-card
      flex-table
      :body-style="{ padding: '0 8px 10px 8px!important', overflow: 'hidden' }"
    >
      <!-- 表格 -->
      <ele-pro-table
        ref="tableRef"
        row-key="id"
        :columns="columns"
        :datasource="datasource"
        :border="true"
        :show-overflow-tooltip="true"
        v-model:selections="selections"
        tooltip-effect="light"
        highlight-current-row
        :footer-style="{ paddingBottom: '3px' }"
        style="padding-bottom: 0"
      >
        <template #toolbar>
          <el-button
            size="small"
            plain
            class="ele-btn-icon"
            @click="openEdit()"
          >
            新建
          </el-button>
          <el-button size="small" plain class="ele-btn-icon" @click="remove()">
            删除
          </el-button>
        </template>
        <template #action="{ row }">
          <template
            v-for="(t, tIndex) in requestType"
            :key="'formButton' + t.id"
          >
            <el-link
              type="primary"
              underline="never"
              @click="openFormField(row, t)"
            >
              {{ t.text }}表单
            </el-link>
            <el-divider direction="vertical" />
          </template>
          <template
            v-for="(t, tIndex) in requestType"
            :key="'processButton' + t.id"
          >
            <el-link
              type="primary"
              underline="never"
              @click="openProcess(row, t)"
            >
              {{ t.text }}流程
            </el-link>
            <el-divider direction="vertical" />
          </template>
          <template
            v-for="(t, tIndex) in requestType"
            :key="'processButton' + t.id"
          >
            <el-link
              v-if="t.id === 'CANCELLATION'"
              type="primary"
              underline="never"
              @click="openAddress(row, t)"
            >
              {{ t.text }}地理范围
            </el-link>
            <!-- <el-divider
              v-if="tIndex < requestType.length - 1"
              direction="vertical"
            /> -->
          </template>
        </template>
        <template #cancellationMode="{ row }">
          {{ row?.cancellationModeObj?.text }}
        </template>
        <template #allowExtension="{ row }">
          {{ row?.allowExtensionObj?.text }}
        </template>
        <template #enableNotification="{ row }">
          {{ row?.enableNotificationObj?.text }}
        </template>
        <template #durationMode="{ row }">
          {{ row?.durationModeObj?.text }}
        </template>
        <template #adminName="{ row }">
          <ele-tooltip content="编辑" placement="left" effect="light">
            <el-link type="primary" underline="never" @click="openEdit(row)">
              {{ row.adminName }}
            </el-link>
          </ele-tooltip>
        </template>
      </ele-pro-table>
    </ele-card>
    <edit v-model="showEdit" :data="editData" @done="reload" />
    <FormFieldIndex
      v-model="showFormField"
      :currentProjectId="currentProjectId"
      :requestTypeId="requestTypeId"
      :requestTypeText="requestTypeText"
      @done="reload"
    />
    <ApprovalRuleNodeIndex
      v-model="showProcess"
      :currentProjectId="currentProjectId"
      :requestTypeId="requestTypeId"
      :requestTypeText="requestTypeText"
      @done="reload"
    />
    <AutoCancelLocation
      v-model="showAddress"
      :currentProjectId="currentProjectId"
      :requestTypeId="requestTypeId"
      :requestTypeText="requestTypeText"
      @done="reload"
    />
  </ele-page>
</template>

<script setup>
  import { reactive, ref, unref } from 'vue';
  import {
    getLeaveProjectPage,
    removes,
    optionalRequestType
  } from './api/index.js';
  import { useRouter } from 'vue-router';
  import { usePageTab } from '@/utils/use-page-tab.js';
  import {
    ElMessageBox,
    ElMessage as EleMessage,
    ElLoading
  } from 'element-plus';
  import Edit from './components/edit.vue';
  import FormFieldIndex from '../form-field/index.vue';
  import ApprovalRuleNodeIndex from '../approval-rule-node/index.vue';
  import AutoCancelLocation from '../auto-cancel-location/index.vue';
  import { useUserStore } from '@/store/modules/user.js';
  import { storeToRefs } from 'pinia';
  import { comColumns } from './utils/index.js';

  const { removePageTab, getRouteTabKey, setPageTab } = usePageTab();
  const userStore = useUserStore();
  const { pageHeight } = storeToRefs(userStore);

  const { currentRoute, push } = useRouter();
  const { path } = unref(currentRoute);

  /** 表格实例 */
  const tableRef = ref(null);

  /** 加载状态 */
  const loading = ref(false);

  /** 是否显示表单弹窗 */
  const showEdit = ref(false);

  /** 编辑回显数据 */
  const editData = ref(null);
  /** 打开编辑弹窗 */
  const openEdit = (row) => {
    editData.value = row ?? null;
    showEdit.value = true;
  };

  const showFormField = ref(false);
  const currentProjectId = ref(null);
  const requestTypeText = ref(null);
  const requestTypeId = ref(null);
  /** 打开表单设置弹窗 */
  const openFormField = (row, requestTypeObj) => {
    requestTypeText.value = requestTypeObj.text;
    requestTypeId.value = requestTypeObj.id;
    currentProjectId.value = row.id;
    showFormField.value = true;
  };
  const showProcess = ref(false);
  const openProcess = (row, requestTypeObj) => {
    requestTypeText.value = requestTypeObj.text;
    requestTypeId.value = requestTypeObj.id;
    currentProjectId.value = row.id;
    showProcess.value = true;
  };
  const showAddress = ref(false);
  const openAddress = (row, requestTypeObj) => {
    requestTypeText.value = requestTypeObj.text;
    requestTypeId.value = requestTypeObj.id;
    currentProjectId.value = row.id;
    showAddress.value = true;
  };

  /** 删除单个 */
  const remove = (row) => {
    const rows = row == null ? selections.value : [row];
    if (!rows.length) {
      EleMessage.error('请至少选择一条数据');
      return;
    }
    ElMessageBox.confirm(
      '确定要删除“' + rows.map((d) => d.name).join(', ') + '”吗?',
      '系统提示',
      { type: 'warning', draggable: true }
    )
      .then(() => {
        const loading = ElLoading.service({
          lock: true,
          text: '请求中...',
          background: 'rgba(0, 0, 0, 0.1)'
        });
        removes(rows.map((d) => d.id))
          .then((msg) => {
            loading.close();
            EleMessage.success('删除成功');
            reload();
          })
          .catch((e) => {
            loading.close();
            EleMessage.error(e.message);
          });
      })
      .catch(() => {});
  };

  /** 是否显示导入弹窗 */
  const showImport = ref(false);
  /** 表格列配置 */
  const columns = ref([]);
  columns.value = comColumns().filter((item) => item.prop !== 'conditions');

  columns.value.push({
    columnKey: 'action',
    label: '操作',
    slot: 'action',
    minWidth: 380,
    fixed: 'right'
  });

  /** 列表选中数据 */
  const selections = ref([]);
  /** 用户名筛选值 */
  const nameFilterValue = ref('');

  /** 用户名筛选事件 */
  const onNameFilter = (name) => {
    nameFilterValue.value = name;
    doReload();
  };
  const requestType = ref([]);
  const queryOptionalRequestType = () => {
    optionalRequestType()
      .then((resData) => {
        if (resData) {
          requestType.value = resData;
        }
      })
      .catch((e) => {
        EleMessage.error(e.message);
      });
  };
  queryOptionalRequestType();
  /** 表格搜索参数 */
  const lastWhere = reactive({});
  /** 表格搜索 */
  const doReload = () => {
    if (nameFilterValue.value) {
      reload({
        ...lastWhere,
        name: nameFilterValue.value
      });
    } else {
      reload(lastWhere);
    }
  };
  /** 表格数据源 */
  const datasource = async ({ page, limit, where, orders, filters }) => {
    const res = await getLeaveProjectPage({
      ...where,
      ...orders,
      ...filters,
      page,
      limit
    });
    res.list.map((item) => {
      item.cancellationMode = item.cancellationModeObj?.id;
      item.allowExtension = item.allowExtensionObj?.id;
      item.enableNotification = item.enableNotificationObj?.id;
      item.durationMode = item.durationModeObj?.id;
    });
    console.log('datasource :>> ', res);
    return res;
  };

  /** 搜索 */
  const reload = (where) => {
    selections.value = [];
    if (where) {
      tableRef['value']?.reload?.({ page: 1, where });
    } else {
      //编辑提交table不全局刷新
      tableRef['value']?.reload?.();
    }
  };
</script>

<script>
  export default {
    name: 'LEAVEPROJECTINDEX'
  };
</script>
