[2m2025-08-04 16:59:02.125[0;39m [32m INFO[0;39m [35m99538[0;39m [2m---[0;39m [2m[kground-preinit][0;39m [36mo.h.validator.internal.util.Version     [0;39m [2m:[0;39m HV000001: Hibernate Validator 8.0.2.Final
[2m2025-08-04 16:59:02.411[0;39m [32m INFO[0;39m [35m99538[0;39m [2m---[0;39m [2m[           main][0;39m [36mcom.sanythadmin.AdminApplication        [0;39m [2m:[0;39m Starting AdminApplication using Java 17.0.15 with PID 99538 (/Users/<USER>/WorkSpace/sanyth-xuegong-5/sanyth-xuegong-5-server/xggl-api/target/classes started by paynexc in /Users/<USER>/WorkSpace/sanyth-xuegong-5)
[2m2025-08-04 16:59:02.412[0;39m [32mDEBUG[0;39m [35m99538[0;39m [2m---[0;39m [2m[           main][0;39m [36mcom.sanythadmin.AdminApplication        [0;39m [2m:[0;39m Running with Spring Boot v3.5.3, Spring v6.2.8
[2m2025-08-04 16:59:02.412[0;39m [32m INFO[0;39m [35m99538[0;39m [2m---[0;39m [2m[           main][0;39m [36mcom.sanythadmin.AdminApplication        [0;39m [2m:[0;39m The following 1 profile is active: "dev"
[2m2025-08-04 16:59:04.247[0;39m [32m INFO[0;39m [35m99538[0;39m [2m---[0;39m [2m[           main][0;39m [36m.s.d.r.c.RepositoryConfigurationDelegate[0;39m [2m:[0;39m Multiple Spring Data modules found, entering strict repository configuration mode
[2m2025-08-04 16:59:04.250[0;39m [32m INFO[0;39m [35m99538[0;39m [2m---[0;39m [2m[           main][0;39m [36m.s.d.r.c.RepositoryConfigurationDelegate[0;39m [2m:[0;39m Bootstrapping Spring Data JPA repositories in DEFAULT mode.
[2m2025-08-04 16:59:04.621[0;39m [32m INFO[0;39m [35m99538[0;39m [2m---[0;39m [2m[           main][0;39m [36m.RepositoryConfigurationExtensionSupport[0;39m [2m:[0;39m Spring Data JPA - Could not safely identify store assignment for repository candidate interface com.sanythadmin.project.export.repository.ExportTaskRepository; If you want this repository to be a JPA repository, consider annotating your entities with one of these annotations: jakarta.persistence.Entity, jakarta.persistence.MappedSuperclass (preferred), or consider extending one of the following types with your repository: org.springframework.data.jpa.repository.JpaRepository
[2m2025-08-04 16:59:04.757[0;39m [32m INFO[0;39m [35m99538[0;39m [2m---[0;39m [2m[           main][0;39m [36m.s.d.r.c.RepositoryConfigurationDelegate[0;39m [2m:[0;39m Finished Spring Data repository scanning in 492 ms. Found 23 JPA repository interfaces.
[2m2025-08-04 16:59:04.811[0;39m [32m INFO[0;39m [35m99538[0;39m [2m---[0;39m [2m[           main][0;39m [36m.s.d.r.c.RepositoryConfigurationDelegate[0;39m [2m:[0;39m Multiple Spring Data modules found, entering strict repository configuration mode
[2m2025-08-04 16:59:04.812[0;39m [32m INFO[0;39m [35m99538[0;39m [2m---[0;39m [2m[           main][0;39m [36m.s.d.r.c.RepositoryConfigurationDelegate[0;39m [2m:[0;39m Bootstrapping Spring Data MongoDB repositories in DEFAULT mode.
[2m2025-08-04 16:59:04.847[0;39m [32m INFO[0;39m [35m99538[0;39m [2m---[0;39m [2m[           main][0;39m [36m.RepositoryConfigurationExtensionSupport[0;39m [2m:[0;39m Spring Data MongoDB - Could not safely identify store assignment for repository candidate interface com.sanythadmin.common.integration.authcenter.repository.AuthCenterParamRepository; If you want this repository to be a MongoDB repository, consider annotating your entities with one of these annotations: org.springframework.data.mongodb.core.mapping.Document (preferred), or consider extending one of the following types with your repository: org.springframework.data.mongodb.repository.MongoRepository
[2m2025-08-04 16:59:04.847[0;39m [32m INFO[0;39m [35m99538[0;39m [2m---[0;39m [2m[           main][0;39m [36m.RepositoryConfigurationExtensionSupport[0;39m [2m:[0;39m Spring Data MongoDB - Could not safely identify store assignment for repository candidate interface com.sanythadmin.platform.dingtalk.repository.DingtalkAppRepository; If you want this repository to be a MongoDB repository, consider annotating your entities with one of these annotations: org.springframework.data.mongodb.core.mapping.Document (preferred), or consider extending one of the following types with your repository: org.springframework.data.mongodb.repository.MongoRepository
[2m2025-08-04 16:59:04.847[0;39m [32m INFO[0;39m [35m99538[0;39m [2m---[0;39m [2m[           main][0;39m [36m.RepositoryConfigurationExtensionSupport[0;39m [2m:[0;39m Spring Data MongoDB - Could not safely identify store assignment for repository candidate interface com.sanythadmin.platform.dingtalk.repository.DingtalkParamRepository; If you want this repository to be a MongoDB repository, consider annotating your entities with one of these annotations: org.springframework.data.mongodb.core.mapping.Document (preferred), or consider extending one of the following types with your repository: org.springframework.data.mongodb.repository.MongoRepository
[2m2025-08-04 16:59:04.847[0;39m [32m INFO[0;39m [35m99538[0;39m [2m---[0;39m [2m[           main][0;39m [36m.RepositoryConfigurationExtensionSupport[0;39m [2m:[0;39m Spring Data MongoDB - Could not safely identify store assignment for repository candidate interface com.sanythadmin.platform.dingtalk.repository.DingtalkSysAccountRepository; If you want this repository to be a MongoDB repository, consider annotating your entities with one of these annotations: org.springframework.data.mongodb.core.mapping.Document (preferred), or consider extending one of the following types with your repository: org.springframework.data.mongodb.repository.MongoRepository
[2m2025-08-04 16:59:04.848[0;39m [32m INFO[0;39m [35m99538[0;39m [2m---[0;39m [2m[           main][0;39m [36m.RepositoryConfigurationExtensionSupport[0;39m [2m:[0;39m Spring Data MongoDB - Could not safely identify store assignment for repository candidate interface com.sanythadmin.platform.dingtalk.repository.DingtalkSysAccountRoleRepository; If you want this repository to be a MongoDB repository, consider annotating your entities with one of these annotations: org.springframework.data.mongodb.core.mapping.Document (preferred), or consider extending one of the following types with your repository: org.springframework.data.mongodb.repository.MongoRepository
[2m2025-08-04 16:59:04.848[0;39m [32m INFO[0;39m [35m99538[0;39m [2m---[0;39m [2m[           main][0;39m [36m.RepositoryConfigurationExtensionSupport[0;39m [2m:[0;39m Spring Data MongoDB - Could not safely identify store assignment for repository candidate interface com.sanythadmin.platform.dingtalk.repository.DingtalkUserRepository; If you want this repository to be a MongoDB repository, consider annotating your entities with one of these annotations: org.springframework.data.mongodb.core.mapping.Document (preferred), or consider extending one of the following types with your repository: org.springframework.data.mongodb.repository.MongoRepository
[2m2025-08-04 16:59:04.850[0;39m [32m INFO[0;39m [35m99538[0;39m [2m---[0;39m [2m[           main][0;39m [36m.RepositoryConfigurationExtensionSupport[0;39m [2m:[0;39m Spring Data MongoDB - Could not safely identify store assignment for repository candidate interface com.sanythadmin.project.leave.repository.LeaveApplicationFieldFileRepository; If you want this repository to be a MongoDB repository, consider annotating your entities with one of these annotations: org.springframework.data.mongodb.core.mapping.Document (preferred), or consider extending one of the following types with your repository: org.springframework.data.mongodb.repository.MongoRepository
[2m2025-08-04 16:59:04.850[0;39m [32m INFO[0;39m [35m99538[0;39m [2m---[0;39m [2m[           main][0;39m [36m.RepositoryConfigurationExtensionSupport[0;39m [2m:[0;39m Spring Data MongoDB - Could not safely identify store assignment for repository candidate interface com.sanythadmin.project.leave.repository.LeaveApplicationProcessNodeAssigneeRepository; If you want this repository to be a MongoDB repository, consider annotating your entities with one of these annotations: org.springframework.data.mongodb.core.mapping.Document (preferred), or consider extending one of the following types with your repository: org.springframework.data.mongodb.repository.MongoRepository
[2m2025-08-04 16:59:04.850[0;39m [32m INFO[0;39m [35m99538[0;39m [2m---[0;39m [2m[           main][0;39m [36m.RepositoryConfigurationExtensionSupport[0;39m [2m:[0;39m Spring Data MongoDB - Could not safely identify store assignment for repository candidate interface com.sanythadmin.project.leave.repository.LeaveApplicationProcessNodeRepository; If you want this repository to be a MongoDB repository, consider annotating your entities with one of these annotations: org.springframework.data.mongodb.core.mapping.Document (preferred), or consider extending one of the following types with your repository: org.springframework.data.mongodb.repository.MongoRepository
[2m2025-08-04 16:59:04.850[0;39m [32m INFO[0;39m [35m99538[0;39m [2m---[0;39m [2m[           main][0;39m [36m.RepositoryConfigurationExtensionSupport[0;39m [2m:[0;39m Spring Data MongoDB - Could not safely identify store assignment for repository candidate interface com.sanythadmin.project.leave.repository.LeaveApplicationProcessRecordRepository; If you want this repository to be a MongoDB repository, consider annotating your entities with one of these annotations: org.springframework.data.mongodb.core.mapping.Document (preferred), or consider extending one of the following types with your repository: org.springframework.data.mongodb.repository.MongoRepository
[2m2025-08-04 16:59:04.851[0;39m [32m INFO[0;39m [35m99538[0;39m [2m---[0;39m [2m[           main][0;39m [36m.RepositoryConfigurationExtensionSupport[0;39m [2m:[0;39m Spring Data MongoDB - Could not safely identify store assignment for repository candidate interface com.sanythadmin.project.leave.repository.LeaveApplicationRepository; If you want this repository to be a MongoDB repository, consider annotating your entities with one of these annotations: org.springframework.data.mongodb.core.mapping.Document (preferred), or consider extending one of the following types with your repository: org.springframework.data.mongodb.repository.MongoRepository
[2m2025-08-04 16:59:04.851[0;39m [32m INFO[0;39m [35m99538[0;39m [2m---[0;39m [2m[           main][0;39m [36m.RepositoryConfigurationExtensionSupport[0;39m [2m:[0;39m Spring Data MongoDB - Could not safely identify store assignment for repository candidate interface com.sanythadmin.project.leave.repository.LeaveBasicFieldRepository; If you want this repository to be a MongoDB repository, consider annotating your entities with one of these annotations: org.springframework.data.mongodb.core.mapping.Document (preferred), or consider extending one of the following types with your repository: org.springframework.data.mongodb.repository.MongoRepository
[2m2025-08-04 16:59:04.851[0;39m [32m INFO[0;39m [35m99538[0;39m [2m---[0;39m [2m[           main][0;39m [36m.RepositoryConfigurationExtensionSupport[0;39m [2m:[0;39m Spring Data MongoDB - Could not safely identify store assignment for repository candidate interface com.sanythadmin.project.leave.repository.LeaveProjectApprovalRuleNodeRepository; If you want this repository to be a MongoDB repository, consider annotating your entities with one of these annotations: org.springframework.data.mongodb.core.mapping.Document (preferred), or consider extending one of the following types with your repository: org.springframework.data.mongodb.repository.MongoRepository
[2m2025-08-04 16:59:04.851[0;39m [32m INFO[0;39m [35m99538[0;39m [2m---[0;39m [2m[           main][0;39m [36m.RepositoryConfigurationExtensionSupport[0;39m [2m:[0;39m Spring Data MongoDB - Could not safely identify store assignment for repository candidate interface com.sanythadmin.project.leave.repository.LeaveProjectAreaRepository; If you want this repository to be a MongoDB repository, consider annotating your entities with one of these annotations: org.springframework.data.mongodb.core.mapping.Document (preferred), or consider extending one of the following types with your repository: org.springframework.data.mongodb.repository.MongoRepository
[2m2025-08-04 16:59:04.851[0;39m [32m INFO[0;39m [35m99538[0;39m [2m---[0;39m [2m[           main][0;39m [36m.RepositoryConfigurationExtensionSupport[0;39m [2m:[0;39m Spring Data MongoDB - Could not safely identify store assignment for repository candidate interface com.sanythadmin.project.leave.repository.LeaveProjectFieldRepository; If you want this repository to be a MongoDB repository, consider annotating your entities with one of these annotations: org.springframework.data.mongodb.core.mapping.Document (preferred), or consider extending one of the following types with your repository: org.springframework.data.mongodb.repository.MongoRepository
[2m2025-08-04 16:59:04.851[0;39m [32m INFO[0;39m [35m99538[0;39m [2m---[0;39m [2m[           main][0;39m [36m.RepositoryConfigurationExtensionSupport[0;39m [2m:[0;39m Spring Data MongoDB - Could not safely identify store assignment for repository candidate interface com.sanythadmin.project.leave.repository.LeaveProjectRepository; If you want this repository to be a MongoDB repository, consider annotating your entities with one of these annotations: org.springframework.data.mongodb.core.mapping.Document (preferred), or consider extending one of the following types with your repository: org.springframework.data.mongodb.repository.MongoRepository
[2m2025-08-04 16:59:04.851[0;39m [32m INFO[0;39m [35m99538[0;39m [2m---[0;39m [2m[           main][0;39m [36m.RepositoryConfigurationExtensionSupport[0;39m [2m:[0;39m Spring Data MongoDB - Could not safely identify store assignment for repository candidate interface com.sanythadmin.project.mobile.repository.MobileAppRepository; If you want this repository to be a MongoDB repository, consider annotating your entities with one of these annotations: org.springframework.data.mongodb.core.mapping.Document (preferred), or consider extending one of the following types with your repository: org.springframework.data.mongodb.repository.MongoRepository
[2m2025-08-04 16:59:04.851[0;39m [32m INFO[0;39m [35m99538[0;39m [2m---[0;39m [2m[           main][0;39m [36m.RepositoryConfigurationExtensionSupport[0;39m [2m:[0;39m Spring Data MongoDB - Could not safely identify store assignment for repository candidate interface com.sanythadmin.project.mobile.repository.MobileAppRoleRepository; If you want this repository to be a MongoDB repository, consider annotating your entities with one of these annotations: org.springframework.data.mongodb.core.mapping.Document (preferred), or consider extending one of the following types with your repository: org.springframework.data.mongodb.repository.MongoRepository
[2m2025-08-04 16:59:04.851[0;39m [32m INFO[0;39m [35m99538[0;39m [2m---[0;39m [2m[           main][0;39m [36m.RepositoryConfigurationExtensionSupport[0;39m [2m:[0;39m Spring Data MongoDB - Could not safely identify store assignment for repository candidate interface com.sanythadmin.project.mobile.repository.MobileGroupRepository; If you want this repository to be a MongoDB repository, consider annotating your entities with one of these annotations: org.springframework.data.mongodb.core.mapping.Document (preferred), or consider extending one of the following types with your repository: org.springframework.data.mongodb.repository.MongoRepository
[2m2025-08-04 16:59:04.851[0;39m [32m INFO[0;39m [35m99538[0;39m [2m---[0;39m [2m[           main][0;39m [36m.RepositoryConfigurationExtensionSupport[0;39m [2m:[0;39m Spring Data MongoDB - Could not safely identify store assignment for repository candidate interface com.sanythadmin.project.mobile.repository.MobileGroupRoleRepository; If you want this repository to be a MongoDB repository, consider annotating your entities with one of these annotations: org.springframework.data.mongodb.core.mapping.Document (preferred), or consider extending one of the following types with your repository: org.springframework.data.mongodb.repository.MongoRepository
[2m2025-08-04 16:59:04.852[0;39m [32m INFO[0;39m [35m99538[0;39m [2m---[0;39m [2m[           main][0;39m [36m.RepositoryConfigurationExtensionSupport[0;39m [2m:[0;39m Spring Data MongoDB - Could not safely identify store assignment for repository candidate interface com.sanythadmin.project.mobile.repository.MobileHomeComponentItemRepository; If you want this repository to be a MongoDB repository, consider annotating your entities with one of these annotations: org.springframework.data.mongodb.core.mapping.Document (preferred), or consider extending one of the following types with your repository: org.springframework.data.mongodb.repository.MongoRepository
[2m2025-08-04 16:59:04.852[0;39m [32m INFO[0;39m [35m99538[0;39m [2m---[0;39m [2m[           main][0;39m [36m.RepositoryConfigurationExtensionSupport[0;39m [2m:[0;39m Spring Data MongoDB - Could not safely identify store assignment for repository candidate interface com.sanythadmin.project.mobile.repository.MobileHomeComponentRepository; If you want this repository to be a MongoDB repository, consider annotating your entities with one of these annotations: org.springframework.data.mongodb.core.mapping.Document (preferred), or consider extending one of the following types with your repository: org.springframework.data.mongodb.repository.MongoRepository
[2m2025-08-04 16:59:04.852[0;39m [32m INFO[0;39m [35m99538[0;39m [2m---[0;39m [2m[           main][0;39m [36m.RepositoryConfigurationExtensionSupport[0;39m [2m:[0;39m Spring Data MongoDB - Could not safely identify store assignment for repository candidate interface com.sanythadmin.project.mobile.repository.MobileSysRoleRepository; If you want this repository to be a MongoDB repository, consider annotating your entities with one of these annotations: org.springframework.data.mongodb.core.mapping.Document (preferred), or consider extending one of the following types with your repository: org.springframework.data.mongodb.repository.MongoRepository
[2m2025-08-04 16:59:04.879[0;39m [32m INFO[0;39m [35m99538[0;39m [2m---[0;39m [2m[           main][0;39m [36m.s.d.r.c.RepositoryConfigurationDelegate[0;39m [2m:[0;39m Finished Spring Data repository scanning in 66 ms. Found 1 MongoDB repository interface.
[2m2025-08-04 16:59:04.894[0;39m [32m INFO[0;39m [35m99538[0;39m [2m---[0;39m [2m[           main][0;39m [36m.s.d.r.c.RepositoryConfigurationDelegate[0;39m [2m:[0;39m Multiple Spring Data modules found, entering strict repository configuration mode
[2m2025-08-04 16:59:04.900[0;39m [32m INFO[0;39m [35m99538[0;39m [2m---[0;39m [2m[           main][0;39m [36m.s.d.r.c.RepositoryConfigurationDelegate[0;39m [2m:[0;39m Bootstrapping Spring Data Redis repositories in DEFAULT mode.
[2m2025-08-04 16:59:04.949[0;39m [32m INFO[0;39m [35m99538[0;39m [2m---[0;39m [2m[           main][0;39m [36m.RepositoryConfigurationExtensionSupport[0;39m [2m:[0;39m Spring Data Redis - Could not safely identify store assignment for repository candidate interface com.sanythadmin.common.integration.authcenter.repository.AuthCenterParamRepository; If you want this repository to be a Redis repository, consider annotating your entities with one of these annotations: org.springframework.data.redis.core.RedisHash (preferred), or consider extending one of the following types with your repository: org.springframework.data.keyvalue.repository.KeyValueRepository
[2m2025-08-04 16:59:04.949[0;39m [32m INFO[0;39m [35m99538[0;39m [2m---[0;39m [2m[           main][0;39m [36m.RepositoryConfigurationExtensionSupport[0;39m [2m:[0;39m Spring Data Redis - Could not safely identify store assignment for repository candidate interface com.sanythadmin.platform.dingtalk.repository.DingtalkAppRepository; If you want this repository to be a Redis repository, consider annotating your entities with one of these annotations: org.springframework.data.redis.core.RedisHash (preferred), or consider extending one of the following types with your repository: org.springframework.data.keyvalue.repository.KeyValueRepository
[2m2025-08-04 16:59:04.949[0;39m [32m INFO[0;39m [35m99538[0;39m [2m---[0;39m [2m[           main][0;39m [36m.RepositoryConfigurationExtensionSupport[0;39m [2m:[0;39m Spring Data Redis - Could not safely identify store assignment for repository candidate interface com.sanythadmin.platform.dingtalk.repository.DingtalkParamRepository; If you want this repository to be a Redis repository, consider annotating your entities with one of these annotations: org.springframework.data.redis.core.RedisHash (preferred), or consider extending one of the following types with your repository: org.springframework.data.keyvalue.repository.KeyValueRepository
[2m2025-08-04 16:59:04.949[0;39m [32m INFO[0;39m [35m99538[0;39m [2m---[0;39m [2m[           main][0;39m [36m.RepositoryConfigurationExtensionSupport[0;39m [2m:[0;39m Spring Data Redis - Could not safely identify store assignment for repository candidate interface com.sanythadmin.platform.dingtalk.repository.DingtalkSysAccountRepository; If you want this repository to be a Redis repository, consider annotating your entities with one of these annotations: org.springframework.data.redis.core.RedisHash (preferred), or consider extending one of the following types with your repository: org.springframework.data.keyvalue.repository.KeyValueRepository
[2m2025-08-04 16:59:04.949[0;39m [32m INFO[0;39m [35m99538[0;39m [2m---[0;39m [2m[           main][0;39m [36m.RepositoryConfigurationExtensionSupport[0;39m [2m:[0;39m Spring Data Redis - Could not safely identify store assignment for repository candidate interface com.sanythadmin.platform.dingtalk.repository.DingtalkSysAccountRoleRepository; If you want this repository to be a Redis repository, consider annotating your entities with one of these annotations: org.springframework.data.redis.core.RedisHash (preferred), or consider extending one of the following types with your repository: org.springframework.data.keyvalue.repository.KeyValueRepository
[2m2025-08-04 16:59:04.949[0;39m [32m INFO[0;39m [35m99538[0;39m [2m---[0;39m [2m[           main][0;39m [36m.RepositoryConfigurationExtensionSupport[0;39m [2m:[0;39m Spring Data Redis - Could not safely identify store assignment for repository candidate interface com.sanythadmin.platform.dingtalk.repository.DingtalkUserRepository; If you want this repository to be a Redis repository, consider annotating your entities with one of these annotations: org.springframework.data.redis.core.RedisHash (preferred), or consider extending one of the following types with your repository: org.springframework.data.keyvalue.repository.KeyValueRepository
[2m2025-08-04 16:59:04.949[0;39m [32m INFO[0;39m [35m99538[0;39m [2m---[0;39m [2m[           main][0;39m [36m.RepositoryConfigurationExtensionSupport[0;39m [2m:[0;39m Spring Data Redis - Could not safely identify store assignment for repository candidate interface com.sanythadmin.project.export.repository.ExportTaskRepository; If you want this repository to be a Redis repository, consider annotating your entities with one of these annotations: org.springframework.data.redis.core.RedisHash (preferred), or consider extending one of the following types with your repository: org.springframework.data.keyvalue.repository.KeyValueRepository
[2m2025-08-04 16:59:04.949[0;39m [32m INFO[0;39m [35m99538[0;39m [2m---[0;39m [2m[           main][0;39m [36m.RepositoryConfigurationExtensionSupport[0;39m [2m:[0;39m Spring Data Redis - Could not safely identify store assignment for repository candidate interface com.sanythadmin.project.leave.repository.LeaveApplicationFieldFileRepository; If you want this repository to be a Redis repository, consider annotating your entities with one of these annotations: org.springframework.data.redis.core.RedisHash (preferred), or consider extending one of the following types with your repository: org.springframework.data.keyvalue.repository.KeyValueRepository
[2m2025-08-04 16:59:04.949[0;39m [32m INFO[0;39m [35m99538[0;39m [2m---[0;39m [2m[           main][0;39m [36m.RepositoryConfigurationExtensionSupport[0;39m [2m:[0;39m Spring Data Redis - Could not safely identify store assignment for repository candidate interface com.sanythadmin.project.leave.repository.LeaveApplicationProcessNodeAssigneeRepository; If you want this repository to be a Redis repository, consider annotating your entities with one of these annotations: org.springframework.data.redis.core.RedisHash (preferred), or consider extending one of the following types with your repository: org.springframework.data.keyvalue.repository.KeyValueRepository
[2m2025-08-04 16:59:04.950[0;39m [32m INFO[0;39m [35m99538[0;39m [2m---[0;39m [2m[           main][0;39m [36m.RepositoryConfigurationExtensionSupport[0;39m [2m:[0;39m Spring Data Redis - Could not safely identify store assignment for repository candidate interface com.sanythadmin.project.leave.repository.LeaveApplicationProcessNodeRepository; If you want this repository to be a Redis repository, consider annotating your entities with one of these annotations: org.springframework.data.redis.core.RedisHash (preferred), or consider extending one of the following types with your repository: org.springframework.data.keyvalue.repository.KeyValueRepository
[2m2025-08-04 16:59:04.950[0;39m [32m INFO[0;39m [35m99538[0;39m [2m---[0;39m [2m[           main][0;39m [36m.RepositoryConfigurationExtensionSupport[0;39m [2m:[0;39m Spring Data Redis - Could not safely identify store assignment for repository candidate interface com.sanythadmin.project.leave.repository.LeaveApplicationProcessRecordRepository; If you want this repository to be a Redis repository, consider annotating your entities with one of these annotations: org.springframework.data.redis.core.RedisHash (preferred), or consider extending one of the following types with your repository: org.springframework.data.keyvalue.repository.KeyValueRepository
[2m2025-08-04 16:59:04.950[0;39m [32m INFO[0;39m [35m99538[0;39m [2m---[0;39m [2m[           main][0;39m [36m.RepositoryConfigurationExtensionSupport[0;39m [2m:[0;39m Spring Data Redis - Could not safely identify store assignment for repository candidate interface com.sanythadmin.project.leave.repository.LeaveApplicationRepository; If you want this repository to be a Redis repository, consider annotating your entities with one of these annotations: org.springframework.data.redis.core.RedisHash (preferred), or consider extending one of the following types with your repository: org.springframework.data.keyvalue.repository.KeyValueRepository
[2m2025-08-04 16:59:04.950[0;39m [32m INFO[0;39m [35m99538[0;39m [2m---[0;39m [2m[           main][0;39m [36m.RepositoryConfigurationExtensionSupport[0;39m [2m:[0;39m Spring Data Redis - Could not safely identify store assignment for repository candidate interface com.sanythadmin.project.leave.repository.LeaveBasicFieldRepository; If you want this repository to be a Redis repository, consider annotating your entities with one of these annotations: org.springframework.data.redis.core.RedisHash (preferred), or consider extending one of the following types with your repository: org.springframework.data.keyvalue.repository.KeyValueRepository
[2m2025-08-04 16:59:04.950[0;39m [32m INFO[0;39m [35m99538[0;39m [2m---[0;39m [2m[           main][0;39m [36m.RepositoryConfigurationExtensionSupport[0;39m [2m:[0;39m Spring Data Redis - Could not safely identify store assignment for repository candidate interface com.sanythadmin.project.leave.repository.LeaveProjectApprovalRuleNodeRepository; If you want this repository to be a Redis repository, consider annotating your entities with one of these annotations: org.springframework.data.redis.core.RedisHash (preferred), or consider extending one of the following types with your repository: org.springframework.data.keyvalue.repository.KeyValueRepository
[2m2025-08-04 16:59:04.950[0;39m [32m INFO[0;39m [35m99538[0;39m [2m---[0;39m [2m[           main][0;39m [36m.RepositoryConfigurationExtensionSupport[0;39m [2m:[0;39m Spring Data Redis - Could not safely identify store assignment for repository candidate interface com.sanythadmin.project.leave.repository.LeaveProjectAreaRepository; If you want this repository to be a Redis repository, consider annotating your entities with one of these annotations: org.springframework.data.redis.core.RedisHash (preferred), or consider extending one of the following types with your repository: org.springframework.data.keyvalue.repository.KeyValueRepository
[2m2025-08-04 16:59:04.950[0;39m [32m INFO[0;39m [35m99538[0;39m [2m---[0;39m [2m[           main][0;39m [36m.RepositoryConfigurationExtensionSupport[0;39m [2m:[0;39m Spring Data Redis - Could not safely identify store assignment for repository candidate interface com.sanythadmin.project.leave.repository.LeaveProjectFieldRepository; If you want this repository to be a Redis repository, consider annotating your entities with one of these annotations: org.springframework.data.redis.core.RedisHash (preferred), or consider extending one of the following types with your repository: org.springframework.data.keyvalue.repository.KeyValueRepository
[2m2025-08-04 16:59:04.950[0;39m [32m INFO[0;39m [35m99538[0;39m [2m---[0;39m [2m[           main][0;39m [36m.RepositoryConfigurationExtensionSupport[0;39m [2m:[0;39m Spring Data Redis - Could not safely identify store assignment for repository candidate interface com.sanythadmin.project.leave.repository.LeaveProjectRepository; If you want this repository to be a Redis repository, consider annotating your entities with one of these annotations: org.springframework.data.redis.core.RedisHash (preferred), or consider extending one of the following types with your repository: org.springframework.data.keyvalue.repository.KeyValueRepository
[2m2025-08-04 16:59:04.950[0;39m [32m INFO[0;39m [35m99538[0;39m [2m---[0;39m [2m[           main][0;39m [36m.RepositoryConfigurationExtensionSupport[0;39m [2m:[0;39m Spring Data Redis - Could not safely identify store assignment for repository candidate interface com.sanythadmin.project.mobile.repository.MobileAppRepository; If you want this repository to be a Redis repository, consider annotating your entities with one of these annotations: org.springframework.data.redis.core.RedisHash (preferred), or consider extending one of the following types with your repository: org.springframework.data.keyvalue.repository.KeyValueRepository
[2m2025-08-04 16:59:04.950[0;39m [32m INFO[0;39m [35m99538[0;39m [2m---[0;39m [2m[           main][0;39m [36m.RepositoryConfigurationExtensionSupport[0;39m [2m:[0;39m Spring Data Redis - Could not safely identify store assignment for repository candidate interface com.sanythadmin.project.mobile.repository.MobileAppRoleRepository; If you want this repository to be a Redis repository, consider annotating your entities with one of these annotations: org.springframework.data.redis.core.RedisHash (preferred), or consider extending one of the following types with your repository: org.springframework.data.keyvalue.repository.KeyValueRepository
[2m2025-08-04 16:59:04.950[0;39m [32m INFO[0;39m [35m99538[0;39m [2m---[0;39m [2m[           main][0;39m [36m.RepositoryConfigurationExtensionSupport[0;39m [2m:[0;39m Spring Data Redis - Could not safely identify store assignment for repository candidate interface com.sanythadmin.project.mobile.repository.MobileGroupRepository; If you want this repository to be a Redis repository, consider annotating your entities with one of these annotations: org.springframework.data.redis.core.RedisHash (preferred), or consider extending one of the following types with your repository: org.springframework.data.keyvalue.repository.KeyValueRepository
[2m2025-08-04 16:59:04.951[0;39m [32m INFO[0;39m [35m99538[0;39m [2m---[0;39m [2m[           main][0;39m [36m.RepositoryConfigurationExtensionSupport[0;39m [2m:[0;39m Spring Data Redis - Could not safely identify store assignment for repository candidate interface com.sanythadmin.project.mobile.repository.MobileGroupRoleRepository; If you want this repository to be a Redis repository, consider annotating your entities with one of these annotations: org.springframework.data.redis.core.RedisHash (preferred), or consider extending one of the following types with your repository: org.springframework.data.keyvalue.repository.KeyValueRepository
[2m2025-08-04 16:59:04.951[0;39m [32m INFO[0;39m [35m99538[0;39m [2m---[0;39m [2m[           main][0;39m [36m.RepositoryConfigurationExtensionSupport[0;39m [2m:[0;39m Spring Data Redis - Could not safely identify store assignment for repository candidate interface com.sanythadmin.project.mobile.repository.MobileHomeComponentItemRepository; If you want this repository to be a Redis repository, consider annotating your entities with one of these annotations: org.springframework.data.redis.core.RedisHash (preferred), or consider extending one of the following types with your repository: org.springframework.data.keyvalue.repository.KeyValueRepository
[2m2025-08-04 16:59:04.951[0;39m [32m INFO[0;39m [35m99538[0;39m [2m---[0;39m [2m[           main][0;39m [36m.RepositoryConfigurationExtensionSupport[0;39m [2m:[0;39m Spring Data Redis - Could not safely identify store assignment for repository candidate interface com.sanythadmin.project.mobile.repository.MobileHomeComponentRepository; If you want this repository to be a Redis repository, consider annotating your entities with one of these annotations: org.springframework.data.redis.core.RedisHash (preferred), or consider extending one of the following types with your repository: org.springframework.data.keyvalue.repository.KeyValueRepository
[2m2025-08-04 16:59:04.951[0;39m [32m INFO[0;39m [35m99538[0;39m [2m---[0;39m [2m[           main][0;39m [36m.RepositoryConfigurationExtensionSupport[0;39m [2m:[0;39m Spring Data Redis - Could not safely identify store assignment for repository candidate interface com.sanythadmin.project.mobile.repository.MobileSysRoleRepository; If you want this repository to be a Redis repository, consider annotating your entities with one of these annotations: org.springframework.data.redis.core.RedisHash (preferred), or consider extending one of the following types with your repository: org.springframework.data.keyvalue.repository.KeyValueRepository
[2m2025-08-04 16:59:04.951[0;39m [32m INFO[0;39m [35m99538[0;39m [2m---[0;39m [2m[           main][0;39m [36m.s.d.r.c.RepositoryConfigurationDelegate[0;39m [2m:[0;39m Finished Spring Data repository scanning in 38 ms. Found 0 Redis repository interfaces.
[2m2025-08-04 16:59:05.410[0;39m [32m INFO[0;39m [35m99538[0;39m [2m---[0;39m [2m[           main][0;39m [36mc.g.y.a.MybatisPlusJoinAutoConfiguration[0;39m [2m:[0;39m MPJ SqlSessionFactory bean definition: sqlSessionFactory factoryBeanName: com.baomidou.mybatisplus.autoconfigure.MybatisPlusAutoConfiguration factoryMethodName: sqlSessionFactory source: com.baomidou.mybatisplus.autoconfigure.MybatisPlusAutoConfiguration.sqlSessionFactory(javax.sql.DataSource)
[2m2025-08-04 16:59:06.416[0;39m [32m INFO[0;39m [35m99538[0;39m [2m---[0;39m [2m[           main][0;39m [36mo.s.b.w.embedded.tomcat.TomcatWebServer [0;39m [2m:[0;39m Tomcat initialized with port 8082 (http)
[2m2025-08-04 16:59:06.446[0;39m [32m INFO[0;39m [35m99538[0;39m [2m---[0;39m [2m[           main][0;39m [36mo.a.coyote.http11.Http11NioProtocol     [0;39m [2m:[0;39m Initializing ProtocolHandler ["http-nio-8082"]
[2m2025-08-04 16:59:06.448[0;39m [32m INFO[0;39m [35m99538[0;39m [2m---[0;39m [2m[           main][0;39m [36mo.apache.catalina.core.StandardService  [0;39m [2m:[0;39m Starting service [Tomcat]
[2m2025-08-04 16:59:06.448[0;39m [32m INFO[0;39m [35m99538[0;39m [2m---[0;39m [2m[           main][0;39m [36mo.apache.catalina.core.StandardEngine   [0;39m [2m:[0;39m Starting Servlet engine: [Apache Tomcat/10.1.42]
[2m2025-08-04 16:59:06.530[0;39m [32m INFO[0;39m [35m99538[0;39m [2m---[0;39m [2m[           main][0;39m [36mo.a.c.c.C.[Tomcat].[localhost].[/]      [0;39m [2m:[0;39m Initializing Spring embedded WebApplicationContext
[2m2025-08-04 16:59:06.531[0;39m [32m INFO[0;39m [35m99538[0;39m [2m---[0;39m [2m[           main][0;39m [36mw.s.c.ServletWebServerApplicationContext[0;39m [2m:[0;39m Root WebApplicationContext: initialization completed in 4063 ms
[2m2025-08-04 16:59:06.754[0;39m [32m INFO[0;39m [35m99538[0;39m [2m---[0;39m [2m[           main][0;39m [36morg.mongodb.driver.client               [0;39m [2m:[0;39m MongoClient with metadata {"driver": {"name": "mongo-java-driver|sync|spring-boot", "version": "5.5.1"}, "os": {"type": "Darwin", "name": "Mac OS X", "architecture": "aarch64", "version": "15.5"}, "platform": "Java/Azul Systems, Inc./17.0.15+6-LTS"} created with settings MongoClientSettings{readPreference=primary, writeConcern=WriteConcern{w=null, wTimeout=null ms, journal=null}, retryWrites=true, retryReads=true, readConcern=ReadConcern{level=null}, credential=MongoCredential{mechanism=null, userName='admin', source='admin', password=<hidden>, mechanismProperties=<hidden>}, transportSettings=null, commandListeners=[], codecRegistry=ProvidersCodecRegistry{codecProviders=[ValueCodecProvider{}, BsonValueCodecProvider{}, DBRefCodecProvider{}, DBObjectCodecProvider{}, DocumentCodecProvider{}, CollectionCodecProvider{}, IterableCodecProvider{}, MapCodecProvider{}, GeoJsonCodecProvider{}, GridFSFileCodecProvider{}, Jsr310CodecProvider{}, JsonObjectCodecProvider{}, BsonCodecProvider{}, EnumCodecProvider{}, com.mongodb.client.model.mql.ExpressionCodecProvider@3aa950b1, com.mongodb.Jep395RecordCodecProvider@6a3cc3a4, com.mongodb.KotlinCodecProvider@50e1ead9]}, loggerSettings=LoggerSettings{maxDocumentLength=1000}, clusterSettings={hosts=[*************:37017], srvServiceName=mongodb, mode=SINGLE, requiredClusterType=UNKNOWN, requiredReplicaSetName='null', serverSelector='null', clusterListeners='[]', serverSelectionTimeout='30000 ms', localThreshold='15 ms'}, socketSettings=SocketSettings{connectTimeoutMS=10000, readTimeoutMS=0, receiveBufferSize=0, proxySettings=ProxySettings{host=null, port=null, username=null, password=null}}, heartbeatSocketSettings=SocketSettings{connectTimeoutMS=10000, readTimeoutMS=10000, receiveBufferSize=0, proxySettings=ProxySettings{host=null, port=null, username=null, password=null}}, connectionPoolSettings=ConnectionPoolSettings{maxSize=100, minSize=0, maxWaitTimeMS=120000, maxConnectionLifeTimeMS=0, maxConnectionIdleTimeMS=0, maintenanceInitialDelayMS=0, maintenanceFrequencyMS=60000, connectionPoolListeners=[], maxConnecting=2}, serverSettings=ServerSettings{heartbeatFrequencyMS=10000, minHeartbeatFrequencyMS=500, serverMonitoringMode=AUTO, serverListeners='[]', serverMonitorListeners='[]'}, sslSettings=SslSettings{enabled=false, invalidHostNameAllowed=false, context=null}, applicationName='null', compressorList=[], uuidRepresentation=JAVA_LEGACY, serverApi=null, autoEncryptionSettings=null, dnsClient=null, inetAddressResolver=null, contextProvider=null, timeoutMS=null}
[2m2025-08-04 16:59:06.925[0;39m [32m INFO[0;39m [35m99538[0;39m [2m---[0;39m [2m[168.9.254:37017][0;39m [36morg.mongodb.driver.cluster              [0;39m [2m:[0;39m Monitor thread successfully connected to server with description ServerDescription{address=*************:37017, type=STANDALONE, cryptd=false, state=CONNECTED, ok=true, minWireVersion=0, maxWireVersion=13, maxDocumentSize=16777216, logicalSessionTimeoutMinutes=30, roundTripTimeNanos=96535542, minRoundTripTimeNanos=0}
[2m2025-08-04 16:59:07.201[0;39m [32m INFO[0;39m [35m99538[0;39m [2m---[0;39m [2m[           main][0;39m [36mc.g.y.a.MybatisPlusJoinAutoConfiguration[0;39m [2m:[0;39m mybatis plus join properties config complete
[2m2025-08-04 16:59:07.456[0;39m [32m INFO[0;39m [35m99538[0;39m [2m---[0;39m [2m[           main][0;39m [36mc.g.y.a.MybatisPlusJoinAutoConfiguration[0;39m [2m:[0;39m mybatis plus join SqlInjector init
[2m2025-08-04 16:59:07.462[0;39m [32mDEBUG[0;39m [35m99538[0;39m [2m---[0;39m [2m[           main][0;39m [36mc.b.m.e.s.MybatisSqlSessionFactoryBean  [0;39m [2m:[0;39m Registered plugin: 'com.sanythadmin.common.core.mybatisplus.interceptor.DecryptInterceptor@287d7790'
[2m2025-08-04 16:59:07.462[0;39m [32mDEBUG[0;39m [35m99538[0;39m [2m---[0;39m [2m[           main][0;39m [36mc.b.m.e.s.MybatisSqlSessionFactoryBean  [0;39m [2m:[0;39m Registered plugin: 'com.baomidou.mybatisplus.extension.plugins.MybatisPlusInterceptor@2acdaa25'
[2m2025-08-04 16:59:07.462[0;39m [32mDEBUG[0;39m [35m99538[0;39m [2m---[0;39m [2m[           main][0;39m [36mc.b.m.e.s.MybatisSqlSessionFactoryBean  [0;39m [2m:[0;39m Registered plugin: 'com.github.yulichang.interceptor.MPJInterceptor@b1e798'
[2m2025-08-04 16:59:07.711[0;39m [32mDEBUG[0;39m [35m99538[0;39m [2m---[0;39m [2m[           main][0;39m [36mc.b.m.e.s.MybatisSqlSessionFactoryBean  [0;39m [2m:[0;39m Parsed mapper file: 'file [/Users/<USER>/WorkSpace/sanyth-xuegong-5/sanyth-xuegong-5-server/xggl-api/target/classes/com/sanythadmin/common/system/mapper/xml/EmailRecordMapper.xml]'
[2m2025-08-04 16:59:07.758[0;39m [32mDEBUG[0;39m [35m99538[0;39m [2m---[0;39m [2m[           main][0;39m [36mc.b.m.e.s.MybatisSqlSessionFactoryBean  [0;39m [2m:[0;39m Parsed mapper file: 'file [/Users/<USER>/WorkSpace/sanyth-xuegong-5/sanyth-xuegong-5-server/xggl-api/target/classes/com/sanythadmin/common/system/mapper/xml/LoginRecordMapper.xml]'
[2m2025-08-04 16:59:07.787[0;39m [32mDEBUG[0;39m [35m99538[0;39m [2m---[0;39m [2m[           main][0;39m [36mc.b.m.e.s.MybatisSqlSessionFactoryBean  [0;39m [2m:[0;39m Parsed mapper file: 'file [/Users/<USER>/WorkSpace/sanyth-xuegong-5/sanyth-xuegong-5-server/xggl-api/target/classes/com/sanythadmin/common/system/mapper/xml/RoleMenuMapper.xml]'
[2m2025-08-04 16:59:07.825[0;39m [32mDEBUG[0;39m [35m99538[0;39m [2m---[0;39m [2m[           main][0;39m [36mc.b.m.e.s.MybatisSqlSessionFactoryBean  [0;39m [2m:[0;39m Parsed mapper file: 'file [/Users/<USER>/WorkSpace/sanyth-xuegong-5/sanyth-xuegong-5-server/xggl-api/target/classes/com/sanythadmin/common/system/mapper/xml/SysAccountMapper.xml]'
[2m2025-08-04 16:59:07.848[0;39m [32mDEBUG[0;39m [35m99538[0;39m [2m---[0;39m [2m[           main][0;39m [36mc.b.m.e.s.MybatisSqlSessionFactoryBean  [0;39m [2m:[0;39m Parsed mapper file: 'file [/Users/<USER>/WorkSpace/sanyth-xuegong-5/sanyth-xuegong-5-server/xggl-api/target/classes/com/sanythadmin/common/system/mapper/xml/SysAccountRoleMapper.xml]'
[2m2025-08-04 16:59:07.875[0;39m [32mDEBUG[0;39m [35m99538[0;39m [2m---[0;39m [2m[           main][0;39m [36mc.b.m.e.s.MybatisSqlSessionFactoryBean  [0;39m [2m:[0;39m Parsed mapper file: 'file [/Users/<USER>/WorkSpace/sanyth-xuegong-5/sanyth-xuegong-5-server/xggl-api/target/classes/com/sanythadmin/common/system/mapper/xml/SysJwtMapper.xml]'
[2m2025-08-04 16:59:07.953[0;39m [32mDEBUG[0;39m [35m99538[0;39m [2m---[0;39m [2m[           main][0;39m [36mc.b.m.e.s.MybatisSqlSessionFactoryBean  [0;39m [2m:[0;39m Parsed mapper file: 'file [/Users/<USER>/WorkSpace/sanyth-xuegong-5/sanyth-xuegong-5-server/xggl-api/target/classes/com/sanythadmin/common/system/mapper/xml/SysMenuMapper.xml]'
[2m2025-08-04 16:59:07.977[0;39m [32mDEBUG[0;39m [35m99538[0;39m [2m---[0;39m [2m[           main][0;39m [36mc.b.m.e.s.MybatisSqlSessionFactoryBean  [0;39m [2m:[0;39m Parsed mapper file: 'file [/Users/<USER>/WorkSpace/sanyth-xuegong-5/sanyth-xuegong-5-server/xggl-api/target/classes/com/sanythadmin/common/system/mapper/xml/SysModuleSetupMapper.xml]'
[2m2025-08-04 16:59:07.998[0;39m [32mDEBUG[0;39m [35m99538[0;39m [2m---[0;39m [2m[           main][0;39m [36mc.b.m.e.s.MybatisSqlSessionFactoryBean  [0;39m [2m:[0;39m Parsed mapper file: 'file [/Users/<USER>/WorkSpace/sanyth-xuegong-5/sanyth-xuegong-5-server/xggl-api/target/classes/com/sanythadmin/common/system/mapper/xml/SysParamMapper.xml]'
[2m2025-08-04 16:59:08.017[0;39m [32mDEBUG[0;39m [35m99538[0;39m [2m---[0;39m [2m[           main][0;39m [36mc.b.m.e.s.MybatisSqlSessionFactoryBean  [0;39m [2m:[0;39m Parsed mapper file: 'file [/Users/<USER>/WorkSpace/sanyth-xuegong-5/sanyth-xuegong-5-server/xggl-api/target/classes/com/sanythadmin/common/system/mapper/xml/SysRoleMapper.xml]'
[2m2025-08-04 16:59:08.036[0;39m [32mDEBUG[0;39m [35m99538[0;39m [2m---[0;39m [2m[           main][0;39m [36mc.b.m.e.s.MybatisSqlSessionFactoryBean  [0;39m [2m:[0;39m Parsed mapper file: 'file [/Users/<USER>/WorkSpace/sanyth-xuegong-5/sanyth-xuegong-5-server/xggl-api/target/classes/com/sanythadmin/common/system/mapper/xml/SysRoleScopeMapper.xml]'
[2m2025-08-04 16:59:08.097[0;39m [32mDEBUG[0;39m [35m99538[0;39m [2m---[0;39m [2m[           main][0;39m [36mc.b.m.e.s.MybatisSqlSessionFactoryBean  [0;39m [2m:[0;39m Parsed mapper file: 'file [/Users/<USER>/WorkSpace/sanyth-xuegong-5/sanyth-xuegong-5-server/xggl-api/target/classes/com/sanythadmin/common/system/mapper/xml/UserInfoMapper.xml]'
[2m2025-08-04 16:59:08.116[0;39m [32mDEBUG[0;39m [35m99538[0;39m [2m---[0;39m [2m[           main][0;39m [36mc.b.m.e.s.MybatisSqlSessionFactoryBean  [0;39m [2m:[0;39m Parsed mapper file: 'file [/Users/<USER>/WorkSpace/sanyth-xuegong-5/sanyth-xuegong-5-server/xggl-api/target/classes/com/sanythadmin/common/system/mapper/xml/UserOrgMapMapper.xml]'
[2m2025-08-04 16:59:08.137[0;39m [32mDEBUG[0;39m [35m99538[0;39m [2m---[0;39m [2m[           main][0;39m [36mc.b.m.e.s.MybatisSqlSessionFactoryBean  [0;39m [2m:[0;39m Parsed mapper file: 'file [/Users/<USER>/WorkSpace/sanyth-xuegong-5/sanyth-xuegong-5-server/xggl-api/target/classes/com/sanythadmin/project/checkin/mapper/xml/CheckinAddressMapper.xml]'
[2m2025-08-04 16:59:08.156[0;39m [32mDEBUG[0;39m [35m99538[0;39m [2m---[0;39m [2m[           main][0;39m [36mc.b.m.e.s.MybatisSqlSessionFactoryBean  [0;39m [2m:[0;39m Parsed mapper file: 'file [/Users/<USER>/WorkSpace/sanyth-xuegong-5/sanyth-xuegong-5-server/xggl-api/target/classes/com/sanythadmin/project/checkin/mapper/xml/CheckinItemAddressMapper.xml]'
[2m2025-08-04 16:59:08.186[0;39m [32mDEBUG[0;39m [35m99538[0;39m [2m---[0;39m [2m[           main][0;39m [36mc.b.m.e.s.MybatisSqlSessionFactoryBean  [0;39m [2m:[0;39m Parsed mapper file: 'file [/Users/<USER>/WorkSpace/sanyth-xuegong-5/sanyth-xuegong-5-server/xggl-api/target/classes/com/sanythadmin/project/checkin/mapper/xml/CheckinItemMapper.xml]'
[2m2025-08-04 16:59:08.221[0;39m [32mDEBUG[0;39m [35m99538[0;39m [2m---[0;39m [2m[           main][0;39m [36mc.b.m.e.s.MybatisSqlSessionFactoryBean  [0;39m [2m:[0;39m Parsed mapper file: 'file [/Users/<USER>/WorkSpace/sanyth-xuegong-5/sanyth-xuegong-5-server/xggl-api/target/classes/com/sanythadmin/project/checkin/mapper/xml/CheckinRecordMapper.xml]'
[2m2025-08-04 16:59:08.246[0;39m [32mDEBUG[0;39m [35m99538[0;39m [2m---[0;39m [2m[           main][0;39m [36mc.b.m.e.s.MybatisSqlSessionFactoryBean  [0;39m [2m:[0;39m Parsed mapper file: 'file [/Users/<USER>/WorkSpace/sanyth-xuegong-5/sanyth-xuegong-5-server/xggl-api/target/classes/com/sanythadmin/project/code/mapper/xml/CodeBjbMapper.xml]'
[2m2025-08-04 16:59:08.267[0;39m [32mDEBUG[0;39m [35m99538[0;39m [2m---[0;39m [2m[           main][0;39m [36mc.b.m.e.s.MybatisSqlSessionFactoryBean  [0;39m [2m:[0;39m Parsed mapper file: 'file [/Users/<USER>/WorkSpace/sanyth-xuegong-5/sanyth-xuegong-5-server/xggl-api/target/classes/com/sanythadmin/project/code/mapper/xml/CodeCommonMapper.xml]'
[2m2025-08-04 16:59:08.287[0;39m [32mDEBUG[0;39m [35m99538[0;39m [2m---[0;39m [2m[           main][0;39m [36mc.b.m.e.s.MybatisSqlSessionFactoryBean  [0;39m [2m:[0;39m Parsed mapper file: 'file [/Users/<USER>/WorkSpace/sanyth-xuegong-5/sanyth-xuegong-5-server/xggl-api/target/classes/com/sanythadmin/project/code/mapper/xml/CodeDwbMapper.xml]'
[2m2025-08-04 16:59:08.304[0;39m [32mDEBUG[0;39m [35m99538[0;39m [2m---[0;39m [2m[           main][0;39m [36mc.b.m.e.s.MybatisSqlSessionFactoryBean  [0;39m [2m:[0;39m Parsed mapper file: 'file [/Users/<USER>/WorkSpace/sanyth-xuegong-5/sanyth-xuegong-5-server/xggl-api/target/classes/com/sanythadmin/project/code/mapper/xml/CodeMzbMapper.xml]'
[2m2025-08-04 16:59:08.324[0;39m [32mDEBUG[0;39m [35m99538[0;39m [2m---[0;39m [2m[           main][0;39m [36mc.b.m.e.s.MybatisSqlSessionFactoryBean  [0;39m [2m:[0;39m Parsed mapper file: 'file [/Users/<USER>/WorkSpace/sanyth-xuegong-5/sanyth-xuegong-5-server/xggl-api/target/classes/com/sanythadmin/project/code/mapper/xml/CodeNjbMapper.xml]'
[2m2025-08-04 16:59:08.340[0;39m [32mDEBUG[0;39m [35m99538[0;39m [2m---[0;39m [2m[           main][0;39m [36mc.b.m.e.s.MybatisSqlSessionFactoryBean  [0;39m [2m:[0;39m Parsed mapper file: 'file [/Users/<USER>/WorkSpace/sanyth-xuegong-5/sanyth-xuegong-5-server/xggl-api/target/classes/com/sanythadmin/project/code/mapper/xml/CodePyccbMapper.xml]'
[2m2025-08-04 16:59:08.357[0;39m [32mDEBUG[0;39m [35m99538[0;39m [2m---[0;39m [2m[           main][0;39m [36mc.b.m.e.s.MybatisSqlSessionFactoryBean  [0;39m [2m:[0;39m Parsed mapper file: 'file [/Users/<USER>/WorkSpace/sanyth-xuegong-5/sanyth-xuegong-5-server/xggl-api/target/classes/com/sanythadmin/project/code/mapper/xml/CodeTypeMapper.xml]'
[2m2025-08-04 16:59:08.380[0;39m [32mDEBUG[0;39m [35m99538[0;39m [2m---[0;39m [2m[           main][0;39m [36mc.b.m.e.s.MybatisSqlSessionFactoryBean  [0;39m [2m:[0;39m Parsed mapper file: 'file [/Users/<USER>/WorkSpace/sanyth-xuegong-5/sanyth-xuegong-5-server/xggl-api/target/classes/com/sanythadmin/project/code/mapper/xml/CodeXqbMapper.xml]'
[2m2025-08-04 16:59:08.402[0;39m [32mDEBUG[0;39m [35m99538[0;39m [2m---[0;39m [2m[           main][0;39m [36mc.b.m.e.s.MybatisSqlSessionFactoryBean  [0;39m [2m:[0;39m Parsed mapper file: 'file [/Users/<USER>/WorkSpace/sanyth-xuegong-5/sanyth-xuegong-5-server/xggl-api/target/classes/com/sanythadmin/project/code/mapper/xml/CodeXsztMapper.xml]'
[2m2025-08-04 16:59:08.421[0;39m [32mDEBUG[0;39m [35m99538[0;39m [2m---[0;39m [2m[           main][0;39m [36mc.b.m.e.s.MybatisSqlSessionFactoryBean  [0;39m [2m:[0;39m Parsed mapper file: 'file [/Users/<USER>/WorkSpace/sanyth-xuegong-5/sanyth-xuegong-5-server/xggl-api/target/classes/com/sanythadmin/project/code/mapper/xml/CodeZybMapper.xml]'
[2m2025-08-04 16:59:08.436[0;39m [32mDEBUG[0;39m [35m99538[0;39m [2m---[0;39m [2m[           main][0;39m [36mc.b.m.e.s.MybatisSqlSessionFactoryBean  [0;39m [2m:[0;39m Parsed mapper file: 'file [/Users/<USER>/WorkSpace/sanyth-xuegong-5/sanyth-xuegong-5-server/xggl-api/target/classes/com/sanythadmin/project/code/mapper/xml/CodeZzmmbMapper.xml]'
[2m2025-08-04 16:59:08.452[0;39m [32mDEBUG[0;39m [35m99538[0;39m [2m---[0;39m [2m[           main][0;39m [36mc.b.m.e.s.MybatisSqlSessionFactoryBean  [0;39m [2m:[0;39m Parsed mapper file: 'file [/Users/<USER>/WorkSpace/sanyth-xuegong-5/sanyth-xuegong-5-server/xggl-api/target/classes/com/sanythadmin/project/code/mapper/xml/ScoreSourceConfigMapper.xml]'
[2m2025-08-04 16:59:08.470[0;39m [32mDEBUG[0;39m [35m99538[0;39m [2m---[0;39m [2m[           main][0;39m [36mc.b.m.e.s.MybatisSqlSessionFactoryBean  [0;39m [2m:[0;39m Parsed mapper file: 'file [/Users/<USER>/WorkSpace/sanyth-xuegong-5/sanyth-xuegong-5-server/xggl-api/target/classes/com/sanythadmin/project/condition/mapper/xml/TempConditionFieldMapper.xml]'
[2m2025-08-04 16:59:08.486[0;39m [32mDEBUG[0;39m [35m99538[0;39m [2m---[0;39m [2m[           main][0;39m [36mc.b.m.e.s.MybatisSqlSessionFactoryBean  [0;39m [2m:[0;39m Parsed mapper file: 'file [/Users/<USER>/WorkSpace/sanyth-xuegong-5/sanyth-xuegong-5-server/xggl-api/target/classes/com/sanythadmin/project/condition/mapper/xml/TempConditionMapper.xml]'
[2m2025-08-04 16:59:08.503[0;39m [32mDEBUG[0;39m [35m99538[0;39m [2m---[0;39m [2m[           main][0;39m [36mc.b.m.e.s.MybatisSqlSessionFactoryBean  [0;39m [2m:[0;39m Parsed mapper file: 'file [/Users/<USER>/WorkSpace/sanyth-xuegong-5/sanyth-xuegong-5-server/xggl-api/target/classes/com/sanythadmin/project/dictionary/mapper/xml/DictionaryFieldEduLevelMapper.xml]'
[2m2025-08-04 16:59:08.518[0;39m [32mDEBUG[0;39m [35m99538[0;39m [2m---[0;39m [2m[           main][0;39m [36mc.b.m.e.s.MybatisSqlSessionFactoryBean  [0;39m [2m:[0;39m Parsed mapper file: 'file [/Users/<USER>/WorkSpace/sanyth-xuegong-5/sanyth-xuegong-5-server/xggl-api/target/classes/com/sanythadmin/project/dictionary/mapper/xml/DictionaryFieldHideRoleMapper.xml]'
[2m2025-08-04 16:59:08.539[0;39m [32mDEBUG[0;39m [35m99538[0;39m [2m---[0;39m [2m[           main][0;39m [36mc.b.m.e.s.MybatisSqlSessionFactoryBean  [0;39m [2m:[0;39m Parsed mapper file: 'file [/Users/<USER>/WorkSpace/sanyth-xuegong-5/sanyth-xuegong-5-server/xggl-api/target/classes/com/sanythadmin/project/dictionary/mapper/xml/DictionaryFieldLinkMapper.xml]'
[2m2025-08-04 16:59:08.562[0;39m [32mDEBUG[0;39m [35m99538[0;39m [2m---[0;39m [2m[           main][0;39m [36mc.b.m.e.s.MybatisSqlSessionFactoryBean  [0;39m [2m:[0;39m Parsed mapper file: 'file [/Users/<USER>/WorkSpace/sanyth-xuegong-5/sanyth-xuegong-5-server/xggl-api/target/classes/com/sanythadmin/project/dictionary/mapper/xml/DictionaryFieldMapper.xml]'
[2m2025-08-04 16:59:08.576[0;39m [32mDEBUG[0;39m [35m99538[0;39m [2m---[0;39m [2m[           main][0;39m [36mc.b.m.e.s.MybatisSqlSessionFactoryBean  [0;39m [2m:[0;39m Parsed mapper file: 'file [/Users/<USER>/WorkSpace/sanyth-xuegong-5/sanyth-xuegong-5-server/xggl-api/target/classes/com/sanythadmin/project/dictionary/mapper/xml/DictionaryGroupEduLevelMapper.xml]'
[2m2025-08-04 16:59:08.591[0;39m [32mDEBUG[0;39m [35m99538[0;39m [2m---[0;39m [2m[           main][0;39m [36mc.b.m.e.s.MybatisSqlSessionFactoryBean  [0;39m [2m:[0;39m Parsed mapper file: 'file [/Users/<USER>/WorkSpace/sanyth-xuegong-5/sanyth-xuegong-5-server/xggl-api/target/classes/com/sanythadmin/project/dictionary/mapper/xml/DictionaryGroupHideRoleMapper.xml]'
[2m2025-08-04 16:59:08.610[0;39m [32mDEBUG[0;39m [35m99538[0;39m [2m---[0;39m [2m[           main][0;39m [36mc.b.m.e.s.MybatisSqlSessionFactoryBean  [0;39m [2m:[0;39m Parsed mapper file: 'file [/Users/<USER>/WorkSpace/sanyth-xuegong-5/sanyth-xuegong-5-server/xggl-api/target/classes/com/sanythadmin/project/dictionary/mapper/xml/DictionaryGroupMapper.xml]'
[2m2025-08-04 16:59:08.625[0;39m [32mDEBUG[0;39m [35m99538[0;39m [2m---[0;39m [2m[           main][0;39m [36mc.b.m.e.s.MybatisSqlSessionFactoryBean  [0;39m [2m:[0;39m Parsed mapper file: 'file [/Users/<USER>/WorkSpace/sanyth-xuegong-5/sanyth-xuegong-5-server/xggl-api/target/classes/com/sanythadmin/project/dictionary/mapper/xml/ListGroupConfigMapper.xml]'
[2m2025-08-04 16:59:08.640[0;39m [32mDEBUG[0;39m [35m99538[0;39m [2m---[0;39m [2m[           main][0;39m [36mc.b.m.e.s.MybatisSqlSessionFactoryBean  [0;39m [2m:[0;39m Parsed mapper file: 'file [/Users/<USER>/WorkSpace/sanyth-xuegong-5/sanyth-xuegong-5-server/xggl-api/target/classes/com/sanythadmin/project/dictionary/mapper/xml/SelectControlApiMapper.xml]'
[2m2025-08-04 16:59:08.673[0;39m [32mDEBUG[0;39m [35m99538[0;39m [2m---[0;39m [2m[           main][0;39m [36mc.b.m.e.s.MybatisSqlSessionFactoryBean  [0;39m [2m:[0;39m Parsed mapper file: 'file [/Users/<USER>/WorkSpace/sanyth-xuegong-5/sanyth-xuegong-5-server/xggl-api/target/classes/com/sanythadmin/project/evaluate/mapper/xml/EvaluateApplicationInfoMapper.xml]'
[2m2025-08-04 16:59:08.690[0;39m [32mDEBUG[0;39m [35m99538[0;39m [2m---[0;39m [2m[           main][0;39m [36mc.b.m.e.s.MybatisSqlSessionFactoryBean  [0;39m [2m:[0;39m Parsed mapper file: 'file [/Users/<USER>/WorkSpace/sanyth-xuegong-5/sanyth-xuegong-5-server/xggl-api/target/classes/com/sanythadmin/project/evaluate/mapper/xml/EvaluateApplicationReferenceInfoMapper.xml]'
[2m2025-08-04 16:59:08.709[0;39m [32mDEBUG[0;39m [35m99538[0;39m [2m---[0;39m [2m[           main][0;39m [36mc.b.m.e.s.MybatisSqlSessionFactoryBean  [0;39m [2m:[0;39m Parsed mapper file: 'file [/Users/<USER>/WorkSpace/sanyth-xuegong-5/sanyth-xuegong-5-server/xggl-api/target/classes/com/sanythadmin/project/evaluate/mapper/xml/EvaluateApprovalNodeMapper.xml]'
[2m2025-08-04 16:59:08.728[0;39m [32mDEBUG[0;39m [35m99538[0;39m [2m---[0;39m [2m[           main][0;39m [36mc.b.m.e.s.MybatisSqlSessionFactoryBean  [0;39m [2m:[0;39m Parsed mapper file: 'file [/Users/<USER>/WorkSpace/sanyth-xuegong-5/sanyth-xuegong-5-server/xggl-api/target/classes/com/sanythadmin/project/evaluate/mapper/xml/EvaluateApprovalNodeRecordMapper.xml]'
[2m2025-08-04 16:59:08.743[0;39m [32mDEBUG[0;39m [35m99538[0;39m [2m---[0;39m [2m[           main][0;39m [36mc.b.m.e.s.MybatisSqlSessionFactoryBean  [0;39m [2m:[0;39m Parsed mapper file: 'file [/Users/<USER>/WorkSpace/sanyth-xuegong-5/sanyth-xuegong-5-server/xggl-api/target/classes/com/sanythadmin/project/evaluate/mapper/xml/EvaluateConfigMapper.xml]'
[2m2025-08-04 16:59:08.758[0;39m [32mDEBUG[0;39m [35m99538[0;39m [2m---[0;39m [2m[           main][0;39m [36mc.b.m.e.s.MybatisSqlSessionFactoryBean  [0;39m [2m:[0;39m Parsed mapper file: 'file [/Users/<USER>/WorkSpace/sanyth-xuegong-5/sanyth-xuegong-5-server/xggl-api/target/classes/com/sanythadmin/project/evaluate/mapper/xml/EvaluateConfigScopeMapper.xml]'
[2m2025-08-04 16:59:08.774[0;39m [32mDEBUG[0;39m [35m99538[0;39m [2m---[0;39m [2m[           main][0;39m [36mc.b.m.e.s.MybatisSqlSessionFactoryBean  [0;39m [2m:[0;39m Parsed mapper file: 'file [/Users/<USER>/WorkSpace/sanyth-xuegong-5/sanyth-xuegong-5-server/xggl-api/target/classes/com/sanythadmin/project/evaluate/mapper/xml/EvaluateItemDetailMapper.xml]'
[2m2025-08-04 16:59:08.789[0;39m [32mDEBUG[0;39m [35m99538[0;39m [2m---[0;39m [2m[           main][0;39m [36mc.b.m.e.s.MybatisSqlSessionFactoryBean  [0;39m [2m:[0;39m Parsed mapper file: 'file [/Users/<USER>/WorkSpace/sanyth-xuegong-5/sanyth-xuegong-5-server/xggl-api/target/classes/com/sanythadmin/project/evaluate/mapper/xml/EvaluateItemDetailScopeMapper.xml]'
[2m2025-08-04 16:59:08.812[0;39m [32mDEBUG[0;39m [35m99538[0;39m [2m---[0;39m [2m[           main][0;39m [36mc.b.m.e.s.MybatisSqlSessionFactoryBean  [0;39m [2m:[0;39m Parsed mapper file: 'file [/Users/<USER>/WorkSpace/sanyth-xuegong-5/sanyth-xuegong-5-server/xggl-api/target/classes/com/sanythadmin/project/evaluate/mapper/xml/EvaluateItemMapper.xml]'
[2m2025-08-04 16:59:08.834[0;39m [32mDEBUG[0;39m [35m99538[0;39m [2m---[0;39m [2m[           main][0;39m [36mc.b.m.e.s.MybatisSqlSessionFactoryBean  [0;39m [2m:[0;39m Parsed mapper file: 'file [/Users/<USER>/WorkSpace/sanyth-xuegong-5/sanyth-xuegong-5-server/xggl-api/target/classes/com/sanythadmin/project/evaluate/mapper/xml/EvaluatePeerReviewRecordMapper.xml]'
[2m2025-08-04 16:59:08.854[0;39m [32mDEBUG[0;39m [35m99538[0;39m [2m---[0;39m [2m[           main][0;39m [36mc.b.m.e.s.MybatisSqlSessionFactoryBean  [0;39m [2m:[0;39m Parsed mapper file: 'file [/Users/<USER>/WorkSpace/sanyth-xuegong-5/sanyth-xuegong-5-server/xggl-api/target/classes/com/sanythadmin/project/evaluate/mapper/xml/EvaluateReviewScoreMapper.xml]'
[2m2025-08-04 16:59:08.872[0;39m [32mDEBUG[0;39m [35m99538[0;39m [2m---[0;39m [2m[           main][0;39m [36mc.b.m.e.s.MybatisSqlSessionFactoryBean  [0;39m [2m:[0;39m Parsed mapper file: 'file [/Users/<USER>/WorkSpace/sanyth-xuegong-5/sanyth-xuegong-5-server/xggl-api/target/classes/com/sanythadmin/project/evaluate/mapper/xml/EvaluateScoreConfirmRecordMapper.xml]'
[2m2025-08-04 16:59:08.900[0;39m [32mDEBUG[0;39m [35m99538[0;39m [2m---[0;39m [2m[           main][0;39m [36mc.b.m.e.s.MybatisSqlSessionFactoryBean  [0;39m [2m:[0;39m Parsed mapper file: 'file [/Users/<USER>/WorkSpace/sanyth-xuegong-5/sanyth-xuegong-5-server/xggl-api/target/classes/com/sanythadmin/project/evaluate/mapper/xml/EvaluateScoreMapper.xml]'
[2m2025-08-04 16:59:08.926[0;39m [32mDEBUG[0;39m [35m99538[0;39m [2m---[0;39m [2m[           main][0;39m [36mc.b.m.e.s.MybatisSqlSessionFactoryBean  [0;39m [2m:[0;39m Parsed mapper file: 'file [/Users/<USER>/WorkSpace/sanyth-xuegong-5/sanyth-xuegong-5-server/xggl-api/target/classes/com/sanythadmin/project/exam/mapper/xml/ExamAnswerInfoMapper.xml]'
[2m2025-08-04 16:59:08.943[0;39m [32mDEBUG[0;39m [35m99538[0;39m [2m---[0;39m [2m[           main][0;39m [36mc.b.m.e.s.MybatisSqlSessionFactoryBean  [0;39m [2m:[0;39m Parsed mapper file: 'file [/Users/<USER>/WorkSpace/sanyth-xuegong-5/sanyth-xuegong-5-server/xggl-api/target/classes/com/sanythadmin/project/exam/mapper/xml/ExamAnswerMapper.xml]'
[2m2025-08-04 16:59:08.958[0;39m [32mDEBUG[0;39m [35m99538[0;39m [2m---[0;39m [2m[           main][0;39m [36mc.b.m.e.s.MybatisSqlSessionFactoryBean  [0;39m [2m:[0;39m Parsed mapper file: 'file [/Users/<USER>/WorkSpace/sanyth-xuegong-5/sanyth-xuegong-5-server/xggl-api/target/classes/com/sanythadmin/project/exam/mapper/xml/ExamClassMapper.xml]'
[2m2025-08-04 16:59:08.977[0;39m [32mDEBUG[0;39m [35m99538[0;39m [2m---[0;39m [2m[           main][0;39m [36mc.b.m.e.s.MybatisSqlSessionFactoryBean  [0;39m [2m:[0;39m Parsed mapper file: 'file [/Users/<USER>/WorkSpace/sanyth-xuegong-5/sanyth-xuegong-5-server/xggl-api/target/classes/com/sanythadmin/project/exam/mapper/xml/ExamMapper.xml]'
[2m2025-08-04 16:59:08.991[0;39m [32mDEBUG[0;39m [35m99538[0;39m [2m---[0;39m [2m[           main][0;39m [36mc.b.m.e.s.MybatisSqlSessionFactoryBean  [0;39m [2m:[0;39m Parsed mapper file: 'file [/Users/<USER>/WorkSpace/sanyth-xuegong-5/sanyth-xuegong-5-server/xggl-api/target/classes/com/sanythadmin/project/exam/mapper/xml/ExamPaperClassMapper.xml]'
[2m2025-08-04 16:59:08.993[0;39m [32mDEBUG[0;39m [35m99538[0;39m [2m---[0;39m [2m[           main][0;39m [36mc.b.m.e.s.MybatisSqlSessionFactoryBean  [0;39m [2m:[0;39m Parsed mapper file: 'file [/Users/<USER>/WorkSpace/sanyth-xuegong-5/sanyth-xuegong-5-server/xggl-api/target/classes/com/sanythadmin/project/exam/mapper/xml/ExamPaperContentMapper.xml]'
[2m2025-08-04 16:59:09.016[0;39m [32mDEBUG[0;39m [35m99538[0;39m [2m---[0;39m [2m[           main][0;39m [36mc.b.m.e.s.MybatisSqlSessionFactoryBean  [0;39m [2m:[0;39m Parsed mapper file: 'file [/Users/<USER>/WorkSpace/sanyth-xuegong-5/sanyth-xuegong-5-server/xggl-api/target/classes/com/sanythadmin/project/exam/mapper/xml/ExamPaperMapper.xml]'
[2m2025-08-04 16:59:09.033[0;39m [32mDEBUG[0;39m [35m99538[0;39m [2m---[0;39m [2m[           main][0;39m [36mc.b.m.e.s.MybatisSqlSessionFactoryBean  [0;39m [2m:[0;39m Parsed mapper file: 'file [/Users/<USER>/WorkSpace/sanyth-xuegong-5/sanyth-xuegong-5-server/xggl-api/target/classes/com/sanythadmin/project/exam/mapper/xml/ExamPaperQgroupMapper.xml]'
[2m2025-08-04 16:59:09.051[0;39m [32mDEBUG[0;39m [35m99538[0;39m [2m---[0;39m [2m[           main][0;39m [36mc.b.m.e.s.MybatisSqlSessionFactoryBean  [0;39m [2m:[0;39m Parsed mapper file: 'file [/Users/<USER>/WorkSpace/sanyth-xuegong-5/sanyth-xuegong-5-server/xggl-api/target/classes/com/sanythadmin/project/exam/mapper/xml/ExamPaperQuestionsMapper.xml]'
[2m2025-08-04 16:59:09.068[0;39m [32mDEBUG[0;39m [35m99538[0;39m [2m---[0;39m [2m[           main][0;39m [36mc.b.m.e.s.MybatisSqlSessionFactoryBean  [0;39m [2m:[0;39m Parsed mapper file: 'file [/Users/<USER>/WorkSpace/sanyth-xuegong-5/sanyth-xuegong-5-server/xggl-api/target/classes/com/sanythadmin/project/exam/mapper/xml/ExamPaperQuestionsOptionsMapper.xml]'
[2m2025-08-04 16:59:09.083[0;39m [32mDEBUG[0;39m [35m99538[0;39m [2m---[0;39m [2m[           main][0;39m [36mc.b.m.e.s.MybatisSqlSessionFactoryBean  [0;39m [2m:[0;39m Parsed mapper file: 'file [/Users/<USER>/WorkSpace/sanyth-xuegong-5/sanyth-xuegong-5-server/xggl-api/target/classes/com/sanythadmin/project/exam/mapper/xml/ExamPaperQuestionsRuleMapper.xml]'
[2m2025-08-04 16:59:09.097[0;39m [32mDEBUG[0;39m [35m99538[0;39m [2m---[0;39m [2m[           main][0;39m [36mc.b.m.e.s.MybatisSqlSessionFactoryBean  [0;39m [2m:[0;39m Parsed mapper file: 'file [/Users/<USER>/WorkSpace/sanyth-xuegong-5/sanyth-xuegong-5-server/xggl-api/target/classes/com/sanythadmin/project/exam/mapper/xml/ExamQuestionsClassMapper.xml]'
[2m2025-08-04 16:59:09.112[0;39m [32mDEBUG[0;39m [35m99538[0;39m [2m---[0;39m [2m[           main][0;39m [36mc.b.m.e.s.MybatisSqlSessionFactoryBean  [0;39m [2m:[0;39m Parsed mapper file: 'file [/Users/<USER>/WorkSpace/sanyth-xuegong-5/sanyth-xuegong-5-server/xggl-api/target/classes/com/sanythadmin/project/exam/mapper/xml/ExamQuestionsMapper.xml]'
[2m2025-08-04 16:59:09.127[0;39m [32mDEBUG[0;39m [35m99538[0;39m [2m---[0;39m [2m[           main][0;39m [36mc.b.m.e.s.MybatisSqlSessionFactoryBean  [0;39m [2m:[0;39m Parsed mapper file: 'file [/Users/<USER>/WorkSpace/sanyth-xuegong-5/sanyth-xuegong-5-server/xggl-api/target/classes/com/sanythadmin/project/exam/mapper/xml/ExamQuestionsOptionsMapper.xml]'
[2m2025-08-04 16:59:09.141[0;39m [32mDEBUG[0;39m [35m99538[0;39m [2m---[0;39m [2m[           main][0;39m [36mc.b.m.e.s.MybatisSqlSessionFactoryBean  [0;39m [2m:[0;39m Parsed mapper file: 'file [/Users/<USER>/WorkSpace/sanyth-xuegong-5/sanyth-xuegong-5-server/xggl-api/target/classes/com/sanythadmin/project/exam/mapper/xml/ExamRecordMapper.xml]'
[2m2025-08-04 16:59:09.164[0;39m [32mDEBUG[0;39m [35m99538[0;39m [2m---[0;39m [2m[           main][0;39m [36mc.b.m.e.s.MybatisSqlSessionFactoryBean  [0;39m [2m:[0;39m Parsed mapper file: 'file [/Users/<USER>/WorkSpace/sanyth-xuegong-5/sanyth-xuegong-5-server/xggl-api/target/classes/com/sanythadmin/project/form/mapper/xml/FormApplicationInfoMapper.xml]'
[2m2025-08-04 16:59:09.181[0;39m [32mDEBUG[0;39m [35m99538[0;39m [2m---[0;39m [2m[           main][0;39m [36mc.b.m.e.s.MybatisSqlSessionFactoryBean  [0;39m [2m:[0;39m Parsed mapper file: 'file [/Users/<USER>/WorkSpace/sanyth-xuegong-5/sanyth-xuegong-5-server/xggl-api/target/classes/com/sanythadmin/project/form/mapper/xml/FormApplicationListInfoMapper.xml]'
[2m2025-08-04 16:59:09.204[0;39m [32mDEBUG[0;39m [35m99538[0;39m [2m---[0;39m [2m[           main][0;39m [36mc.b.m.e.s.MybatisSqlSessionFactoryBean  [0;39m [2m:[0;39m Parsed mapper file: 'file [/Users/<USER>/WorkSpace/sanyth-xuegong-5/sanyth-xuegong-5-server/xggl-api/target/classes/com/sanythadmin/project/form/mapper/xml/FormApprovalNodeMapper.xml]'
[2m2025-08-04 16:59:09.220[0;39m [32mDEBUG[0;39m [35m99538[0;39m [2m---[0;39m [2m[           main][0;39m [36mc.b.m.e.s.MybatisSqlSessionFactoryBean  [0;39m [2m:[0;39m Parsed mapper file: 'file [/Users/<USER>/WorkSpace/sanyth-xuegong-5/sanyth-xuegong-5-server/xggl-api/target/classes/com/sanythadmin/project/form/mapper/xml/FormApprovalNodeRecordMapper.xml]'
[2m2025-08-04 16:59:09.239[0;39m [32mDEBUG[0;39m [35m99538[0;39m [2m---[0;39m [2m[           main][0;39m [36mc.b.m.e.s.MybatisSqlSessionFactoryBean  [0;39m [2m:[0;39m Parsed mapper file: 'file [/Users/<USER>/WorkSpace/sanyth-xuegong-5/sanyth-xuegong-5-server/xggl-api/target/classes/com/sanythadmin/project/form/mapper/xml/FormApprovalResultMapper.xml]'
[2m2025-08-04 16:59:09.256[0;39m [32mDEBUG[0;39m [35m99538[0;39m [2m---[0;39m [2m[           main][0;39m [36mc.b.m.e.s.MybatisSqlSessionFactoryBean  [0;39m [2m:[0;39m Parsed mapper file: 'file [/Users/<USER>/WorkSpace/sanyth-xuegong-5/sanyth-xuegong-5-server/xggl-api/target/classes/com/sanythadmin/project/form/mapper/xml/FormCustomFieldMapper.xml]'
[2m2025-08-04 16:59:09.273[0;39m [32mDEBUG[0;39m [35m99538[0;39m [2m---[0;39m [2m[           main][0;39m [36mc.b.m.e.s.MybatisSqlSessionFactoryBean  [0;39m [2m:[0;39m Parsed mapper file: 'file [/Users/<USER>/WorkSpace/sanyth-xuegong-5/sanyth-xuegong-5-server/xggl-api/target/classes/com/sanythadmin/project/form/mapper/xml/FormGroupMapper.xml]'
[2m2025-08-04 16:59:09.288[0;39m [32mDEBUG[0;39m [35m99538[0;39m [2m---[0;39m [2m[           main][0;39m [36mc.b.m.e.s.MybatisSqlSessionFactoryBean  [0;39m [2m:[0;39m Parsed mapper file: 'file [/Users/<USER>/WorkSpace/sanyth-xuegong-5/sanyth-xuegong-5-server/xggl-api/target/classes/com/sanythadmin/project/form/mapper/xml/FormLimitQuotaMapper.xml]'
[2m2025-08-04 16:59:09.310[0;39m [32mDEBUG[0;39m [35m99538[0;39m [2m---[0;39m [2m[           main][0;39m [36mc.b.m.e.s.MybatisSqlSessionFactoryBean  [0;39m [2m:[0;39m Parsed mapper file: 'file [/Users/<USER>/WorkSpace/sanyth-xuegong-5/sanyth-xuegong-5-server/xggl-api/target/classes/com/sanythadmin/project/form/mapper/xml/FormProjectMapper.xml]'
[2m2025-08-04 16:59:09.325[0;39m [32mDEBUG[0;39m [35m99538[0;39m [2m---[0;39m [2m[           main][0;39m [36mc.b.m.e.s.MybatisSqlSessionFactoryBean  [0;39m [2m:[0;39m Parsed mapper file: 'file [/Users/<USER>/WorkSpace/sanyth-xuegong-5/sanyth-xuegong-5-server/xggl-api/target/classes/com/sanythadmin/project/form/mapper/xml/FormProjectSpecialListMapper.xml]'
[2m2025-08-04 16:59:09.341[0;39m [32mDEBUG[0;39m [35m99538[0;39m [2m---[0;39m [2m[           main][0;39m [36mc.b.m.e.s.MybatisSqlSessionFactoryBean  [0;39m [2m:[0;39m Parsed mapper file: 'file [/Users/<USER>/WorkSpace/sanyth-xuegong-5/sanyth-xuegong-5-server/xggl-api/target/classes/com/sanythadmin/project/form/mapper/xml/FormProjectTemplateFieldMapper.xml]'
[2m2025-08-04 16:59:09.363[0;39m [32mDEBUG[0;39m [35m99538[0;39m [2m---[0;39m [2m[           main][0;39m [36mc.b.m.e.s.MybatisSqlSessionFactoryBean  [0;39m [2m:[0;39m Parsed mapper file: 'file [/Users/<USER>/WorkSpace/sanyth-xuegong-5/sanyth-xuegong-5-server/xggl-api/target/classes/com/sanythadmin/project/form/mapper/xml/FormPublicityContentMapper.xml]'
[2m2025-08-04 16:59:09.380[0;39m [32mDEBUG[0;39m [35m99538[0;39m [2m---[0;39m [2m[           main][0;39m [36mc.b.m.e.s.MybatisSqlSessionFactoryBean  [0;39m [2m:[0;39m Parsed mapper file: 'file [/Users/<USER>/WorkSpace/sanyth-xuegong-5/sanyth-xuegong-5-server/xggl-api/target/classes/com/sanythadmin/project/form/mapper/xml/FormPublicityFeedbackMapper.xml]'
[2m2025-08-04 16:59:09.396[0;39m [32mDEBUG[0;39m [35m99538[0;39m [2m---[0;39m [2m[           main][0;39m [36mc.b.m.e.s.MybatisSqlSessionFactoryBean  [0;39m [2m:[0;39m Parsed mapper file: 'file [/Users/<USER>/WorkSpace/sanyth-xuegong-5/sanyth-xuegong-5-server/xggl-api/target/classes/com/sanythadmin/project/form/mapper/xml/FormPublicityItemMapper.xml]'
[2m2025-08-04 16:59:09.412[0;39m [32mDEBUG[0;39m [35m99538[0;39m [2m---[0;39m [2m[           main][0;39m [36mc.b.m.e.s.MybatisSqlSessionFactoryBean  [0;39m [2m:[0;39m Parsed mapper file: 'file [/Users/<USER>/WorkSpace/sanyth-xuegong-5/sanyth-xuegong-5-server/xggl-api/target/classes/com/sanythadmin/project/form/mapper/xml/FormRestrictMapper.xml]'
[2m2025-08-04 16:59:09.428[0;39m [32mDEBUG[0;39m [35m99538[0;39m [2m---[0;39m [2m[           main][0;39m [36mc.b.m.e.s.MybatisSqlSessionFactoryBean  [0;39m [2m:[0;39m Parsed mapper file: 'file [/Users/<USER>/WorkSpace/sanyth-xuegong-5/sanyth-xuegong-5-server/xggl-api/target/classes/com/sanythadmin/project/form/mapper/xml/FormTemplateFieldLinkMapper.xml]'
[2m2025-08-04 16:59:09.445[0;39m [32mDEBUG[0;39m [35m99538[0;39m [2m---[0;39m [2m[           main][0;39m [36mc.b.m.e.s.MybatisSqlSessionFactoryBean  [0;39m [2m:[0;39m Parsed mapper file: 'file [/Users/<USER>/WorkSpace/sanyth-xuegong-5/sanyth-xuegong-5-server/xggl-api/target/classes/com/sanythadmin/project/form/mapper/xml/FormTemplateFieldMapper.xml]'
[2m2025-08-04 16:59:09.459[0;39m [32mDEBUG[0;39m [35m99538[0;39m [2m---[0;39m [2m[           main][0;39m [36mc.b.m.e.s.MybatisSqlSessionFactoryBean  [0;39m [2m:[0;39m Parsed mapper file: 'file [/Users/<USER>/WorkSpace/sanyth-xuegong-5/sanyth-xuegong-5-server/xggl-api/target/classes/com/sanythadmin/project/form/mapper/xml/FormTypeMapper.xml]'
[2m2025-08-04 16:59:09.478[0;39m [32mDEBUG[0;39m [35m99538[0;39m [2m---[0;39m [2m[           main][0;39m [36mc.b.m.e.s.MybatisSqlSessionFactoryBean  [0;39m [2m:[0;39m Parsed mapper file: 'file [/Users/<USER>/WorkSpace/sanyth-xuegong-5/sanyth-xuegong-5-server/xggl-api/target/classes/com/sanythadmin/project/mentalHealth/mapper/xml/XljkAppointmentInfoLogMapper.xml]'
[2m2025-08-04 16:59:09.496[0;39m [32mDEBUG[0;39m [35m99538[0;39m [2m---[0;39m [2m[           main][0;39m [36mc.b.m.e.s.MybatisSqlSessionFactoryBean  [0;39m [2m:[0;39m Parsed mapper file: 'file [/Users/<USER>/WorkSpace/sanyth-xuegong-5/sanyth-xuegong-5-server/xggl-api/target/classes/com/sanythadmin/project/mentalHealth/mapper/xml/XljkAppointmentInfoMapper.xml]'
[2m2025-08-04 16:59:09.511[0;39m [32mDEBUG[0;39m [35m99538[0;39m [2m---[0;39m [2m[           main][0;39m [36mc.b.m.e.s.MybatisSqlSessionFactoryBean  [0;39m [2m:[0;39m Parsed mapper file: 'file [/Users/<USER>/WorkSpace/sanyth-xuegong-5/sanyth-xuegong-5-server/xggl-api/target/classes/com/sanythadmin/project/mentalHealth/mapper/xml/XljkConsultantMapper.xml]'
[2m2025-08-04 16:59:09.528[0;39m [32mDEBUG[0;39m [35m99538[0;39m [2m---[0;39m [2m[           main][0;39m [36mc.b.m.e.s.MybatisSqlSessionFactoryBean  [0;39m [2m:[0;39m Parsed mapper file: 'file [/Users/<USER>/WorkSpace/sanyth-xuegong-5/sanyth-xuegong-5-server/xggl-api/target/classes/com/sanythadmin/project/mentalHealth/mapper/xml/XljkCounselingRecordMapper.xml]'
[2m2025-08-04 16:59:09.554[0;39m [32mDEBUG[0;39m [35m99538[0;39m [2m---[0;39m [2m[           main][0;39m [36mc.b.m.e.s.MybatisSqlSessionFactoryBean  [0;39m [2m:[0;39m Parsed mapper file: 'file [/Users/<USER>/WorkSpace/sanyth-xuegong-5/sanyth-xuegong-5-server/xggl-api/target/classes/com/sanythadmin/project/mentalHealth/mapper/xml/XljkCounselorScheduleMapper.xml]'
[2m2025-08-04 16:59:09.573[0;39m [32mDEBUG[0;39m [35m99538[0;39m [2m---[0;39m [2m[           main][0;39m [36mc.b.m.e.s.MybatisSqlSessionFactoryBean  [0;39m [2m:[0;39m Parsed mapper file: 'file [/Users/<USER>/WorkSpace/sanyth-xuegong-5/sanyth-xuegong-5-server/xggl-api/target/classes/com/sanythadmin/project/mentalHealth/mapper/xml/XljkCrisisReportApprovalNodeMapper.xml]'
[2m2025-08-04 16:59:09.598[0;39m [32mDEBUG[0;39m [35m99538[0;39m [2m---[0;39m [2m[           main][0;39m [36mc.b.m.e.s.MybatisSqlSessionFactoryBean  [0;39m [2m:[0;39m Parsed mapper file: 'file [/Users/<USER>/WorkSpace/sanyth-xuegong-5/sanyth-xuegong-5-server/xggl-api/target/classes/com/sanythadmin/project/mentalHealth/mapper/xml/XljkCrisisReportMapper.xml]'
[2m2025-08-04 16:59:09.612[0;39m [32mDEBUG[0;39m [35m99538[0;39m [2m---[0;39m [2m[           main][0;39m [36mc.b.m.e.s.MybatisSqlSessionFactoryBean  [0;39m [2m:[0;39m Parsed mapper file: 'file [/Users/<USER>/WorkSpace/sanyth-xuegong-5/sanyth-xuegong-5-server/xggl-api/target/classes/com/sanythadmin/project/mentalHealth/mapper/xml/XljkCrisisReportNodeMapper.xml]'
[2m2025-08-04 16:59:09.634[0;39m [32mDEBUG[0;39m [35m99538[0;39m [2m---[0;39m [2m[           main][0;39m [36mc.b.m.e.s.MybatisSqlSessionFactoryBean  [0;39m [2m:[0;39m Parsed mapper file: 'file [/Users/<USER>/WorkSpace/sanyth-xuegong-5/sanyth-xuegong-5-server/xggl-api/target/classes/com/sanythadmin/project/mentalHealth/mapper/xml/XljkCrisisRepositoryMapper.xml]'
[2m2025-08-04 16:59:09.653[0;39m [32mDEBUG[0;39m [35m99538[0;39m [2m---[0;39m [2m[           main][0;39m [36mc.b.m.e.s.MybatisSqlSessionFactoryBean  [0;39m [2m:[0;39m Parsed mapper file: 'file [/Users/<USER>/WorkSpace/sanyth-xuegong-5/sanyth-xuegong-5-server/xggl-api/target/classes/com/sanythadmin/project/mentalHealth/mapper/xml/XljkFollowupRecordMapper.xml]'
[2m2025-08-04 16:59:09.668[0;39m [32mDEBUG[0;39m [35m99538[0;39m [2m---[0;39m [2m[           main][0;39m [36mc.b.m.e.s.MybatisSqlSessionFactoryBean  [0;39m [2m:[0;39m Parsed mapper file: 'file [/Users/<USER>/WorkSpace/sanyth-xuegong-5/sanyth-xuegong-5-server/xggl-api/target/classes/com/sanythadmin/project/mentalHealth/mapper/xml/XljkInterpretationMapper.xml]'
[2m2025-08-04 16:59:09.682[0;39m [32mDEBUG[0;39m [35m99538[0;39m [2m---[0;39m [2m[           main][0;39m [36mc.b.m.e.s.MybatisSqlSessionFactoryBean  [0;39m [2m:[0;39m Parsed mapper file: 'file [/Users/<USER>/WorkSpace/sanyth-xuegong-5/sanyth-xuegong-5-server/xggl-api/target/classes/com/sanythadmin/project/mentalHealth/mapper/xml/XljkParamMapper.xml]'
[2m2025-08-04 16:59:09.699[0;39m [32mDEBUG[0;39m [35m99538[0;39m [2m---[0;39m [2m[           main][0;39m [36mc.b.m.e.s.MybatisSqlSessionFactoryBean  [0;39m [2m:[0;39m Parsed mapper file: 'file [/Users/<USER>/WorkSpace/sanyth-xuegong-5/sanyth-xuegong-5-server/xggl-api/target/classes/com/sanythadmin/project/mentalHealth/mapper/xml/XljkSurveyAnswerInfoMapper.xml]'
[2m2025-08-04 16:59:09.718[0;39m [32mDEBUG[0;39m [35m99538[0;39m [2m---[0;39m [2m[           main][0;39m [36mc.b.m.e.s.MybatisSqlSessionFactoryBean  [0;39m [2m:[0;39m Parsed mapper file: 'file [/Users/<USER>/WorkSpace/sanyth-xuegong-5/sanyth-xuegong-5-server/xggl-api/target/classes/com/sanythadmin/project/mentalHealth/mapper/xml/XljkSurveyAnswerMapper.xml]'
[2m2025-08-04 16:59:09.733[0;39m [32mDEBUG[0;39m [35m99538[0;39m [2m---[0;39m [2m[           main][0;39m [36mc.b.m.e.s.MybatisSqlSessionFactoryBean  [0;39m [2m:[0;39m Parsed mapper file: 'file [/Users/<USER>/WorkSpace/sanyth-xuegong-5/sanyth-xuegong-5-server/xggl-api/target/classes/com/sanythadmin/project/mentalHealth/mapper/xml/XljkSurveyInterpretationMapper.xml]'
[2m2025-08-04 16:59:09.747[0;39m [32mDEBUG[0;39m [35m99538[0;39m [2m---[0;39m [2m[           main][0;39m [36mc.b.m.e.s.MybatisSqlSessionFactoryBean  [0;39m [2m:[0;39m Parsed mapper file: 'file [/Users/<USER>/WorkSpace/sanyth-xuegong-5/sanyth-xuegong-5-server/xggl-api/target/classes/com/sanythadmin/project/mentalHealth/mapper/xml/XljkSurveyMapper.xml]'
[2m2025-08-04 16:59:09.767[0;39m [32mDEBUG[0;39m [35m99538[0;39m [2m---[0;39m [2m[           main][0;39m [36mc.b.m.e.s.MybatisSqlSessionFactoryBean  [0;39m [2m:[0;39m Parsed mapper file: 'file [/Users/<USER>/WorkSpace/sanyth-xuegong-5/sanyth-xuegong-5-server/xggl-api/target/classes/com/sanythadmin/project/mentalHealth/mapper/xml/XljkSurveyReportMapper.xml]'
[2m2025-08-04 16:59:09.789[0;39m [32mDEBUG[0;39m [35m99538[0;39m [2m---[0;39m [2m[           main][0;39m [36mc.b.m.e.s.MybatisSqlSessionFactoryBean  [0;39m [2m:[0;39m Parsed mapper file: 'file [/Users/<USER>/WorkSpace/sanyth-xuegong-5/sanyth-xuegong-5-server/xggl-api/target/classes/com/sanythadmin/project/resume/mapper/xml/ResumeDataConfigMapper.xml]'
[2m2025-08-04 16:59:09.807[0;39m [32mDEBUG[0;39m [35m99538[0;39m [2m---[0;39m [2m[           main][0;39m [36mc.b.m.e.s.MybatisSqlSessionFactoryBean  [0;39m [2m:[0;39m Parsed mapper file: 'file [/Users/<USER>/WorkSpace/sanyth-xuegong-5/sanyth-xuegong-5-server/xggl-api/target/classes/com/sanythadmin/project/resume/mapper/xml/ResumeTemplateMapper.xml]'
[2m2025-08-04 16:59:09.824[0;39m [32mDEBUG[0;39m [35m99538[0;39m [2m---[0;39m [2m[           main][0;39m [36mc.b.m.e.s.MybatisSqlSessionFactoryBean  [0;39m [2m:[0;39m Parsed mapper file: 'file [/Users/<USER>/WorkSpace/sanyth-xuegong-5/sanyth-xuegong-5-server/xggl-api/target/classes/com/sanythadmin/project/resume/mapper/xml/ResumeTemplateTypeMapper.xml]'
[2m2025-08-04 16:59:09.842[0;39m [32mDEBUG[0;39m [35m99538[0;39m [2m---[0;39m [2m[           main][0;39m [36mc.b.m.e.s.MybatisSqlSessionFactoryBean  [0;39m [2m:[0;39m Parsed mapper file: 'file [/Users/<USER>/WorkSpace/sanyth-xuegong-5/sanyth-xuegong-5-server/xggl-api/target/classes/com/sanythadmin/project/resume/mapper/xml/UserResumeMapper.xml]'
[2m2025-08-04 16:59:09.861[0;39m [32mDEBUG[0;39m [35m99538[0;39m [2m---[0;39m [2m[           main][0;39m [36mc.b.m.e.s.MybatisSqlSessionFactoryBean  [0;39m [2m:[0;39m Parsed mapper file: 'file [/Users/<USER>/WorkSpace/sanyth-xuegong-5/sanyth-xuegong-5-server/xggl-api/target/classes/com/sanythadmin/project/score/mapper/xml/CourseMarkMapper.xml]'
[2m2025-08-04 16:59:09.875[0;39m [32mDEBUG[0;39m [35m99538[0;39m [2m---[0;39m [2m[           main][0;39m [36mc.b.m.e.s.MybatisSqlSessionFactoryBean  [0;39m [2m:[0;39m Parsed mapper file: 'file [/Users/<USER>/WorkSpace/sanyth-xuegong-5/sanyth-xuegong-5-server/xggl-api/target/classes/com/sanythadmin/project/score/mapper/xml/CoursePropertiesPercentMapper.xml]'
[2m2025-08-04 16:59:09.895[0;39m [32mDEBUG[0;39m [35m99538[0;39m [2m---[0;39m [2m[           main][0;39m [36mc.b.m.e.s.MybatisSqlSessionFactoryBean  [0;39m [2m:[0;39m Parsed mapper file: 'file [/Users/<USER>/WorkSpace/sanyth-xuegong-5/sanyth-xuegong-5-server/xggl-api/target/classes/com/sanythadmin/project/score/mapper/xml/CourseScoreItemMapper.xml]'
[2m2025-08-04 16:59:09.918[0;39m [32mDEBUG[0;39m [35m99538[0;39m [2m---[0;39m [2m[           main][0;39m [36mc.b.m.e.s.MybatisSqlSessionFactoryBean  [0;39m [2m:[0;39m Parsed mapper file: 'file [/Users/<USER>/WorkSpace/sanyth-xuegong-5/sanyth-xuegong-5-server/xggl-api/target/classes/com/sanythadmin/project/score/mapper/xml/CourseScoreResultMapper.xml]'
[2m2025-08-04 16:59:09.936[0;39m [32mDEBUG[0;39m [35m99538[0;39m [2m---[0;39m [2m[           main][0;39m [36mc.b.m.e.s.MybatisSqlSessionFactoryBean  [0;39m [2m:[0;39m Parsed mapper file: 'file [/Users/<USER>/WorkSpace/sanyth-xuegong-5/sanyth-xuegong-5-server/xggl-api/target/classes/com/sanythadmin/project/score/mapper/xml/ScoreLycjMapper.xml]'
[2m2025-08-04 16:59:09.952[0;39m [32mDEBUG[0;39m [35m99538[0;39m [2m---[0;39m [2m[           main][0;39m [36mc.b.m.e.s.MybatisSqlSessionFactoryBean  [0;39m [2m:[0;39m Parsed mapper file: 'file [/Users/<USER>/WorkSpace/sanyth-xuegong-5/sanyth-xuegong-5-server/xggl-api/target/classes/com/sanythadmin/project/score/mapper/xml/ScoreMycjMapper.xml]'
[2m2025-08-04 16:59:09.973[0;39m [32mDEBUG[0;39m [35m99538[0;39m [2m---[0;39m [2m[           main][0;39m [36mc.b.m.e.s.MybatisSqlSessionFactoryBean  [0;39m [2m:[0;39m Parsed mapper file: 'file [/Users/<USER>/WorkSpace/sanyth-xuegong-5/sanyth-xuegong-5-server/xggl-api/target/classes/com/sanythadmin/project/score/mapper/xml/ScoreTycjMapper.xml]'
[2m2025-08-04 16:59:09.990[0;39m [32mDEBUG[0;39m [35m99538[0;39m [2m---[0;39m [2m[           main][0;39m [36mc.b.m.e.s.MybatisSqlSessionFactoryBean  [0;39m [2m:[0;39m Parsed mapper file: 'file [/Users/<USER>/WorkSpace/sanyth-xuegong-5/sanyth-xuegong-5-server/xggl-api/target/classes/com/sanythadmin/project/score/mapper/xml/ScoreTyhdcyjfMapper.xml]'
[2m2025-08-04 16:59:10.007[0;39m [32mDEBUG[0;39m [35m99538[0;39m [2m---[0;39m [2m[           main][0;39m [36mc.b.m.e.s.MybatisSqlSessionFactoryBean  [0;39m [2m:[0;39m Parsed mapper file: 'file [/Users/<USER>/WorkSpace/sanyth-xuegong-5/sanyth-xuegong-5-server/xggl-api/target/classes/com/sanythadmin/project/score/mapper/xml/ScoreTzjkcsMapper.xml]'
[2m2025-08-04 16:59:10.022[0;39m [32mDEBUG[0;39m [35m99538[0;39m [2m---[0;39m [2m[           main][0;39m [36mc.b.m.e.s.MybatisSqlSessionFactoryBean  [0;39m [2m:[0;39m Parsed mapper file: 'file [/Users/<USER>/WorkSpace/sanyth-xuegong-5/sanyth-xuegong-5-server/xggl-api/target/classes/com/sanythadmin/project/selector/mapper/xml/SelectorAccountMapper.xml]'
[2m2025-08-04 16:59:10.039[0;39m [32mDEBUG[0;39m [35m99538[0;39m [2m---[0;39m [2m[           main][0;39m [36mc.b.m.e.s.MybatisSqlSessionFactoryBean  [0;39m [2m:[0;39m Parsed mapper file: 'file [/Users/<USER>/WorkSpace/sanyth-xuegong-5/sanyth-xuegong-5-server/xggl-api/target/classes/com/sanythadmin/project/selector/mapper/xml/SelectorConfigMapper.xml]'
[2m2025-08-04 16:59:10.054[0;39m [32mDEBUG[0;39m [35m99538[0;39m [2m---[0;39m [2m[           main][0;39m [36mc.b.m.e.s.MybatisSqlSessionFactoryBean  [0;39m [2m:[0;39m Parsed mapper file: 'file [/Users/<USER>/WorkSpace/sanyth-xuegong-5/sanyth-xuegong-5-server/xggl-api/target/classes/com/sanythadmin/project/selector/mapper/xml/SelectorDataMapper.xml]'
[2m2025-08-04 16:59:10.073[0;39m [32mDEBUG[0;39m [35m99538[0;39m [2m---[0;39m [2m[           main][0;39m [36mc.b.m.e.s.MybatisSqlSessionFactoryBean  [0;39m [2m:[0;39m Parsed mapper file: 'file [/Users/<USER>/WorkSpace/sanyth-xuegong-5/sanyth-xuegong-5-server/xggl-api/target/classes/com/sanythadmin/project/survey/mapper/xml/SurveyAnswerInfoMapper.xml]'
[2m2025-08-04 16:59:10.089[0;39m [32mDEBUG[0;39m [35m99538[0;39m [2m---[0;39m [2m[           main][0;39m [36mc.b.m.e.s.MybatisSqlSessionFactoryBean  [0;39m [2m:[0;39m Parsed mapper file: 'file [/Users/<USER>/WorkSpace/sanyth-xuegong-5/sanyth-xuegong-5-server/xggl-api/target/classes/com/sanythadmin/project/survey/mapper/xml/SurveyAnswerMapper.xml]'
[2m2025-08-04 16:59:10.104[0;39m [32mDEBUG[0;39m [35m99538[0;39m [2m---[0;39m [2m[           main][0;39m [36mc.b.m.e.s.MybatisSqlSessionFactoryBean  [0;39m [2m:[0;39m Parsed mapper file: 'file [/Users/<USER>/WorkSpace/sanyth-xuegong-5/sanyth-xuegong-5-server/xggl-api/target/classes/com/sanythadmin/project/survey/mapper/xml/SurveyClassMapper.xml]'
[2m2025-08-04 16:59:10.123[0;39m [32mDEBUG[0;39m [35m99538[0;39m [2m---[0;39m [2m[           main][0;39m [36mc.b.m.e.s.MybatisSqlSessionFactoryBean  [0;39m [2m:[0;39m Parsed mapper file: 'file [/Users/<USER>/WorkSpace/sanyth-xuegong-5/sanyth-xuegong-5-server/xggl-api/target/classes/com/sanythadmin/project/survey/mapper/xml/SurveyMapper.xml]'
[2m2025-08-04 16:59:10.142[0;39m [32mDEBUG[0;39m [35m99538[0;39m [2m---[0;39m [2m[           main][0;39m [36mc.b.m.e.s.MybatisSqlSessionFactoryBean  [0;39m [2m:[0;39m Parsed mapper file: 'file [/Users/<USER>/WorkSpace/sanyth-xuegong-5/sanyth-xuegong-5-server/xggl-api/target/classes/com/sanythadmin/project/survey/mapper/xml/SurveyPquestionsMapper.xml]'
[2m2025-08-04 16:59:10.161[0;39m [32mDEBUG[0;39m [35m99538[0;39m [2m---[0;39m [2m[           main][0;39m [36mc.b.m.e.s.MybatisSqlSessionFactoryBean  [0;39m [2m:[0;39m Parsed mapper file: 'file [/Users/<USER>/WorkSpace/sanyth-xuegong-5/sanyth-xuegong-5-server/xggl-api/target/classes/com/sanythadmin/project/survey/mapper/xml/SurveyPquestionsOptionsMapper.xml]'
[2m2025-08-04 16:59:10.177[0;39m [32mDEBUG[0;39m [35m99538[0;39m [2m---[0;39m [2m[           main][0;39m [36mc.b.m.e.s.MybatisSqlSessionFactoryBean  [0;39m [2m:[0;39m Parsed mapper file: 'file [/Users/<USER>/WorkSpace/sanyth-xuegong-5/sanyth-xuegong-5-server/xggl-api/target/classes/com/sanythadmin/project/survey/mapper/xml/SurveyPquestionsRuleMapper.xml]'
[2m2025-08-04 16:59:10.200[0;39m [32mDEBUG[0;39m [35m99538[0;39m [2m---[0;39m [2m[           main][0;39m [36mc.b.m.e.s.MybatisSqlSessionFactoryBean  [0;39m [2m:[0;39m Parsed mapper file: 'file [/Users/<USER>/WorkSpace/sanyth-xuegong-5/sanyth-xuegong-5-server/xggl-api/target/classes/com/sanythadmin/project/survey/mapper/xml/SurveyQgroupMapper.xml]'
[2m2025-08-04 16:59:10.216[0;39m [32mDEBUG[0;39m [35m99538[0;39m [2m---[0;39m [2m[           main][0;39m [36mc.b.m.e.s.MybatisSqlSessionFactoryBean  [0;39m [2m:[0;39m Parsed mapper file: 'file [/Users/<USER>/WorkSpace/sanyth-xuegong-5/sanyth-xuegong-5-server/xggl-api/target/classes/com/sanythadmin/project/survey/mapper/xml/SurveyQuestionsClassMapper.xml]'
[2m2025-08-04 16:59:10.236[0;39m [32mDEBUG[0;39m [35m99538[0;39m [2m---[0;39m [2m[           main][0;39m [36mc.b.m.e.s.MybatisSqlSessionFactoryBean  [0;39m [2m:[0;39m Parsed mapper file: 'file [/Users/<USER>/WorkSpace/sanyth-xuegong-5/sanyth-xuegong-5-server/xggl-api/target/classes/com/sanythadmin/project/survey/mapper/xml/SurveyQuestionsMapper.xml]'
[2m2025-08-04 16:59:10.251[0;39m [32mDEBUG[0;39m [35m99538[0;39m [2m---[0;39m [2m[           main][0;39m [36mc.b.m.e.s.MybatisSqlSessionFactoryBean  [0;39m [2m:[0;39m Parsed mapper file: 'file [/Users/<USER>/WorkSpace/sanyth-xuegong-5/sanyth-xuegong-5-server/xggl-api/target/classes/com/sanythadmin/project/survey/mapper/xml/SurveyQuestionsOptionsMapper.xml]'
[2m2025-08-04 16:59:10.272[0;39m [32mDEBUG[0;39m [35m99538[0;39m [2m---[0;39m [2m[           main][0;39m [36mc.b.m.e.s.MybatisSqlSessionFactoryBean  [0;39m [2m:[0;39m Parsed mapper file: 'file [/Users/<USER>/WorkSpace/sanyth-xuegong-5/sanyth-xuegong-5-server/xggl-api/target/classes/com/sanythadmin/project/teacherEvaluate/mapper/xml/FdycpAnswerInfoMapper.xml]'
[2m2025-08-04 16:59:10.293[0;39m [32mDEBUG[0;39m [35m99538[0;39m [2m---[0;39m [2m[           main][0;39m [36mc.b.m.e.s.MybatisSqlSessionFactoryBean  [0;39m [2m:[0;39m Parsed mapper file: 'file [/Users/<USER>/WorkSpace/sanyth-xuegong-5/sanyth-xuegong-5-server/xggl-api/target/classes/com/sanythadmin/project/teacherEvaluate/mapper/xml/FdycpAnswerMapper.xml]'
[2m2025-08-04 16:59:10.307[0;39m [32mDEBUG[0;39m [35m99538[0;39m [2m---[0;39m [2m[           main][0;39m [36mc.b.m.e.s.MybatisSqlSessionFactoryBean  [0;39m [2m:[0;39m Parsed mapper file: 'file [/Users/<USER>/WorkSpace/sanyth-xuegong-5/sanyth-xuegong-5-server/xggl-api/target/classes/com/sanythadmin/project/teacherEvaluate/mapper/xml/FdycpGradeMapper.xml]'
[2m2025-08-04 16:59:10.322[0;39m [32mDEBUG[0;39m [35m99538[0;39m [2m---[0;39m [2m[           main][0;39m [36mc.b.m.e.s.MybatisSqlSessionFactoryBean  [0;39m [2m:[0;39m Parsed mapper file: 'file [/Users/<USER>/WorkSpace/sanyth-xuegong-5/sanyth-xuegong-5-server/xggl-api/target/classes/com/sanythadmin/project/teacherEvaluate/mapper/xml/FdycpItemMapper.xml]'
[2m2025-08-04 16:59:10.336[0;39m [32mDEBUG[0;39m [35m99538[0;39m [2m---[0;39m [2m[           main][0;39m [36mc.b.m.e.s.MybatisSqlSessionFactoryBean  [0;39m [2m:[0;39m Parsed mapper file: 'file [/Users/<USER>/WorkSpace/sanyth-xuegong-5/sanyth-xuegong-5-server/xggl-api/target/classes/com/sanythadmin/project/teacherEvaluate/mapper/xml/FdycpItemStaffMapper.xml]'
[2m2025-08-04 16:59:10.351[0;39m [32mDEBUG[0;39m [35m99538[0;39m [2m---[0;39m [2m[           main][0;39m [36mc.b.m.e.s.MybatisSqlSessionFactoryBean  [0;39m [2m:[0;39m Parsed mapper file: 'file [/Users/<USER>/WorkSpace/sanyth-xuegong-5/sanyth-xuegong-5-server/xggl-api/target/classes/com/sanythadmin/project/teacherEvaluate/mapper/xml/FdycpResultMapper.xml]'
[2m2025-08-04 16:59:10.366[0;39m [32mDEBUG[0;39m [35m99538[0;39m [2m---[0;39m [2m[           main][0;39m [36mc.b.m.e.s.MybatisSqlSessionFactoryBean  [0;39m [2m:[0;39m Parsed mapper file: 'file [/Users/<USER>/WorkSpace/sanyth-xuegong-5/sanyth-xuegong-5-server/xggl-api/target/classes/com/sanythadmin/project/teacherEvaluate/mapper/xml/FdycpSurveyItemMapper.xml]'
[2m2025-08-04 16:59:10.380[0;39m [32mDEBUG[0;39m [35m99538[0;39m [2m---[0;39m [2m[           main][0;39m [36mc.b.m.e.s.MybatisSqlSessionFactoryBean  [0;39m [2m:[0;39m Parsed mapper file: 'file [/Users/<USER>/WorkSpace/sanyth-xuegong-5/sanyth-xuegong-5-server/xggl-api/target/classes/com/sanythadmin/project/teacherEvaluate/mapper/xml/FdycpSurveyItemStaffMapper.xml]'
[2m2025-08-04 16:59:10.400[0;39m [32mDEBUG[0;39m [35m99538[0;39m [2m---[0;39m [2m[           main][0;39m [36mc.b.m.e.s.MybatisSqlSessionFactoryBean  [0;39m [2m:[0;39m Parsed mapper file: 'file [/Users/<USER>/WorkSpace/sanyth-xuegong-5/sanyth-xuegong-5-server/xggl-api/target/classes/com/sanythadmin/project/teacherWork/mapper/xml/FdyWorkItemFieldMapper.xml]'
[2m2025-08-04 16:59:10.424[0;39m [32mDEBUG[0;39m [35m99538[0;39m [2m---[0;39m [2m[           main][0;39m [36mc.b.m.e.s.MybatisSqlSessionFactoryBean  [0;39m [2m:[0;39m Parsed mapper file: 'file [/Users/<USER>/WorkSpace/sanyth-xuegong-5/sanyth-xuegong-5-server/xggl-api/target/classes/com/sanythadmin/project/teacherWork/mapper/xml/FdyWorkItemMapper.xml]'
[2m2025-08-04 16:59:10.451[0;39m [32mDEBUG[0;39m [35m99538[0;39m [2m---[0;39m [2m[           main][0;39m [36mc.b.m.e.s.MybatisSqlSessionFactoryBean  [0;39m [2m:[0;39m Parsed mapper file: 'file [/Users/<USER>/WorkSpace/sanyth-xuegong-5/sanyth-xuegong-5-server/xggl-api/target/classes/com/sanythadmin/project/teacherWork/mapper/xml/FdyWorkItemRecordMapper.xml]'
[2m2025-08-04 16:59:10.470[0;39m [32mDEBUG[0;39m [35m99538[0;39m [2m---[0;39m [2m[           main][0;39m [36mc.b.m.e.s.MybatisSqlSessionFactoryBean  [0;39m [2m:[0;39m Parsed mapper file: 'file [/Users/<USER>/WorkSpace/sanyth-xuegong-5/sanyth-xuegong-5-server/xggl-api/target/classes/com/sanythadmin/project/userInfo/mapper/xml/QuickSearchMapper.xml]'
[2m2025-08-04 16:59:10.493[0;39m [32mDEBUG[0;39m [35m99538[0;39m [2m---[0;39m [2m[           main][0;39m [36mc.b.m.e.s.MybatisSqlSessionFactoryBean  [0;39m [2m:[0;39m Parsed mapper file: 'file [/Users/<USER>/WorkSpace/sanyth-xuegong-5/sanyth-xuegong-5-server/xggl-api/target/classes/com/sanythadmin/project/userInfo/mapper/xml/StudentRecordChangeMapper.xml]'
[2m2025-08-04 16:59:10.511[0;39m [32mDEBUG[0;39m [35m99538[0;39m [2m---[0;39m [2m[           main][0;39m [36mc.b.m.e.s.MybatisSqlSessionFactoryBean  [0;39m [2m:[0;39m Parsed mapper file: 'file [/Users/<USER>/WorkSpace/sanyth-xuegong-5/sanyth-xuegong-5-server/xggl-api/target/classes/com/sanythadmin/project/userInfo/mapper/xml/UserDataScopeMapper.xml]'
[2m2025-08-04 16:59:10.530[0;39m [32mDEBUG[0;39m [35m99538[0;39m [2m---[0;39m [2m[           main][0;39m [36mc.b.m.e.s.MybatisSqlSessionFactoryBean  [0;39m [2m:[0;39m Parsed mapper file: 'file [/Users/<USER>/WorkSpace/sanyth-xuegong-5/sanyth-xuegong-5-server/xggl-api/target/classes/com/sanythadmin/project/userInfo/mapper/xml/UserListInfoMapper.xml]'
[2m2025-08-04 16:59:10.548[0;39m [32mDEBUG[0;39m [35m99538[0;39m [2m---[0;39m [2m[           main][0;39m [36mc.b.m.e.s.MybatisSqlSessionFactoryBean  [0;39m [2m:[0;39m Parsed mapper file: 'file [/Users/<USER>/WorkSpace/sanyth-xuegong-5/sanyth-xuegong-5-server/xggl-api/target/classes/com/sanythadmin/project/workflow/mapper/xml/WorkflowApprovalNodeMapper.xml]'
[2m2025-08-04 16:59:10.565[0;39m [32mDEBUG[0;39m [35m99538[0;39m [2m---[0;39m [2m[           main][0;39m [36mc.b.m.e.s.MybatisSqlSessionFactoryBean  [0;39m [2m:[0;39m Parsed mapper file: 'file [/Users/<USER>/WorkSpace/sanyth-xuegong-5/sanyth-xuegong-5-server/xggl-api/target/classes/com/sanythadmin/project/workflow/mapper/xml/WorkflowApprovalNodeRecordMapper.xml]'
[2m2025-08-04 16:59:10.581[0;39m [32mDEBUG[0;39m [35m99538[0;39m [2m---[0;39m [2m[           main][0;39m [36mc.b.m.e.s.MybatisSqlSessionFactoryBean  [0;39m [2m:[0;39m Parsed mapper file: 'file [/Users/<USER>/WorkSpace/sanyth-xuegong-5/sanyth-xuegong-5-server/xggl-api/target/classes/com/sanythadmin/project/workflow/mapper/xml/WorkflowConditionDetailMapper.xml]'
[2m2025-08-04 16:59:10.595[0;39m [32mDEBUG[0;39m [35m99538[0;39m [2m---[0;39m [2m[           main][0;39m [36mc.b.m.e.s.MybatisSqlSessionFactoryBean  [0;39m [2m:[0;39m Parsed mapper file: 'file [/Users/<USER>/WorkSpace/sanyth-xuegong-5/sanyth-xuegong-5-server/xggl-api/target/classes/com/sanythadmin/project/workflow/mapper/xml/WorkflowConditionMapper.xml]'
[2m2025-08-04 16:59:10.613[0;39m [32mDEBUG[0;39m [35m99538[0;39m [2m---[0;39m [2m[           main][0;39m [36mc.b.m.e.s.MybatisSqlSessionFactoryBean  [0;39m [2m:[0;39m Parsed mapper file: 'file [/Users/<USER>/WorkSpace/sanyth-xuegong-5/sanyth-xuegong-5-server/xggl-api/target/classes/com/sanythadmin/project/workflow/mapper/xml/WorkflowEventMapper.xml]'
[2m2025-08-04 16:59:10.627[0;39m [32mDEBUG[0;39m [35m99538[0;39m [2m---[0;39m [2m[           main][0;39m [36mc.b.m.e.s.MybatisSqlSessionFactoryBean  [0;39m [2m:[0;39m Parsed mapper file: 'file [/Users/<USER>/WorkSpace/sanyth-xuegong-5/sanyth-xuegong-5-server/xggl-api/target/classes/com/sanythadmin/project/workflow/mapper/xml/WorkflowMapper.xml]'
[2m2025-08-04 16:59:10.644[0;39m [32mDEBUG[0;39m [35m99538[0;39m [2m---[0;39m [2m[           main][0;39m [36mc.b.m.e.s.MybatisSqlSessionFactoryBean  [0;39m [2m:[0;39m Parsed mapper file: 'file [/Users/<USER>/WorkSpace/sanyth-xuegong-5/sanyth-xuegong-5-server/xggl-api/target/classes/com/sanythadmin/project/workflow/mapper/xml/WorkflowNodeApproverMapper.xml]'
[2m2025-08-04 16:59:10.659[0;39m [32mDEBUG[0;39m [35m99538[0;39m [2m---[0;39m [2m[           main][0;39m [36mc.b.m.e.s.MybatisSqlSessionFactoryBean  [0;39m [2m:[0;39m Parsed mapper file: 'file [/Users/<USER>/WorkSpace/sanyth-xuegong-5/sanyth-xuegong-5-server/xggl-api/target/classes/com/sanythadmin/project/workflow/mapper/xml/WorkflowNodeFormMapper.xml]'
[2m2025-08-04 16:59:10.681[0;39m [32mDEBUG[0;39m [35m99538[0;39m [2m---[0;39m [2m[           main][0;39m [36mc.b.m.e.s.MybatisSqlSessionFactoryBean  [0;39m [2m:[0;39m Parsed mapper file: 'file [/Users/<USER>/WorkSpace/sanyth-xuegong-5/sanyth-xuegong-5-server/xggl-api/target/classes/com/sanythadmin/project/workflow/mapper/xml/WorkflowNodeMapper.xml]'
[2m2025-08-04 16:59:10.700[0;39m [32mDEBUG[0;39m [35m99538[0;39m [2m---[0;39m [2m[           main][0;39m [36mc.b.m.e.s.MybatisSqlSessionFactoryBean  [0;39m [2m:[0;39m Parsed mapper file: 'file [/Users/<USER>/WorkSpace/sanyth-xuegong-5/sanyth-xuegong-5-server/xggl-api/target/classes/com/sanythadmin/project/workflow/mapper/xml/WorkflowNodeStateMapper.xml]'
[2m2025-08-04 16:59:10.719[0;39m [32mDEBUG[0;39m [35m99538[0;39m [2m---[0;39m [2m[           main][0;39m [36mc.b.m.e.s.MybatisSqlSessionFactoryBean  [0;39m [2m:[0;39m Parsed mapper file: 'file [/Users/<USER>/WorkSpace/sanyth-xuegong-5/sanyth-xuegong-5-server/xggl-api/target/classes/com/sanythadmin/project/workstudy/mapper/xml/QgzxEmployerMapper.xml]'
[2m2025-08-04 16:59:10.743[0;39m [32mDEBUG[0;39m [35m99538[0;39m [2m---[0;39m [2m[           main][0;39m [36mc.b.m.e.s.MybatisSqlSessionFactoryBean  [0;39m [2m:[0;39m Parsed mapper file: 'file [/Users/<USER>/WorkSpace/sanyth-xuegong-5/sanyth-xuegong-5-server/xggl-api/target/classes/com/sanythadmin/project/workstudy/mapper/xml/QgzxInterviewRecordMapper.xml]'
[2m2025-08-04 16:59:10.758[0;39m [32mDEBUG[0;39m [35m99538[0;39m [2m---[0;39m [2m[           main][0;39m [36mc.b.m.e.s.MybatisSqlSessionFactoryBean  [0;39m [2m:[0;39m Parsed mapper file: 'file [/Users/<USER>/WorkSpace/sanyth-xuegong-5/sanyth-xuegong-5-server/xggl-api/target/classes/com/sanythadmin/project/workstudy/mapper/xml/QgzxJobTypeMapper.xml]'
[2m2025-08-04 16:59:10.773[0;39m [32mDEBUG[0;39m [35m99538[0;39m [2m---[0;39m [2m[           main][0;39m [36mc.b.m.e.s.MybatisSqlSessionFactoryBean  [0;39m [2m:[0;39m Parsed mapper file: 'file [/Users/<USER>/WorkSpace/sanyth-xuegong-5/sanyth-xuegong-5-server/xggl-api/target/classes/com/sanythadmin/project/workstudy/mapper/xml/QgzxKsbMapper.xml]'
[2m2025-08-04 16:59:10.787[0;39m [32mDEBUG[0;39m [35m99538[0;39m [2m---[0;39m [2m[           main][0;39m [36mc.b.m.e.s.MybatisSqlSessionFactoryBean  [0;39m [2m:[0;39m Parsed mapper file: 'file [/Users/<USER>/WorkSpace/sanyth-xuegong-5/sanyth-xuegong-5-server/xggl-api/target/classes/com/sanythadmin/project/workstudy/mapper/xml/QgzxStudentsClassTimeMapper.xml]'
[2m2025-08-04 16:59:10.799[0;39m [32mDEBUG[0;39m [35m99538[0;39m [2m---[0;39m [2m[           main][0;39m [36mc.b.mybatisplus.core.toolkit.Sequence   [0;39m [2m:[0;39m Initialization Sequence datacenterId:0 workerId:10
[2m2025-08-04 16:59:11.223[0;39m [32m INFO[0;39m [35m99538[0;39m [2m---[0;39m [2m[           main][0;39m [36morg.redisson.Version                    [0;39m [2m:[0;39m Redisson 3.36.0
[2m2025-08-04 16:59:11.359[0;39m [31mERROR[0;39m [35m99538[0;39m [2m---[0;39m [2m[           main][0;39m [36mi.n.r.d.DnsServerAddressStreamProviders [0;39m [2m:[0;39m Unable to load io.netty.resolver.dns.macos.MacOSDnsServerAddressStreamProvider, fallback to system defaults. This may result in incorrect DNS resolutions on MacOS. Check whether you have a dependency on 'io.netty:netty-resolver-dns-native-macos'. Use DEBUG level to see the full stack: java.lang.UnsatisfiedLinkError: failed to load the required native library
[2m2025-08-04 16:59:11.819[0;39m [32m INFO[0;39m [35m99538[0;39m [2m---[0;39m [2m[isson-netty-1-6][0;39m [36mo.redisson.connection.ConnectionsHolder [0;39m [2m:[0;39m 1 connections initialized for *************/*************:6380
[2m2025-08-04 16:59:20.545[0;39m [32m INFO[0;39m [35m99538[0;39m [2m---[0;39m [2m[sson-netty-1-19][0;39m [36mo.redisson.connection.ConnectionsHolder [0;39m [2m:[0;39m 24 connections initialized for *************/*************:6380
[2m2025-08-04 16:59:21.055[0;39m [32mDEBUG[0;39m [35m99538[0;39m [2m---[0;39m [2m[           main][0;39m [36mc.s.c.c.s.JwtAuthenticationFilter       [0;39m [2m:[0;39m Filter 'jwtAuthenticationFilter' configured for use
[2m2025-08-04 16:59:21.884[0;39m [32m INFO[0;39m [35m99538[0;39m [2m---[0;39m [2m[           main][0;39m [36mo.hibernate.jpa.internal.util.LogHelper [0;39m [2m:[0;39m HHH000204: Processing PersistenceUnitInfo [name: default]
[2m2025-08-04 16:59:22.082[0;39m [32m INFO[0;39m [35m99538[0;39m [2m---[0;39m [2m[           main][0;39m [36morg.hibernate.Version                   [0;39m [2m:[0;39m HHH000412: Hibernate ORM core version 6.6.18.Final
[2m2025-08-04 16:59:22.430[0;39m [32m INFO[0;39m [35m99538[0;39m [2m---[0;39m [2m[           main][0;39m [36mo.h.c.internal.RegionFactoryInitiator   [0;39m [2m:[0;39m HHH000026: Second-level cache disabled
[2m2025-08-04 16:59:24.182[0;39m [32m INFO[0;39m [35m99538[0;39m [2m---[0;39m [2m[           main][0;39m [36mo.s.o.j.p.SpringPersistenceUnitInfo     [0;39m [2m:[0;39m No LoadTimeWeaver setup: ignoring JPA class transformer
[2m2025-08-04 16:59:24.599[0;39m [32m INFO[0;39m [35m99538[0;39m [2m---[0;39m [2m[           main][0;39m [36mcom.alibaba.druid.pool.DruidDataSource  [0;39m [2m:[0;39m {dataSource-1} inited
[2m2025-08-04 16:59:31.567[0;39m [33m WARN[0;39m [35m99538[0;39m [2m---[0;39m [2m[           main][0;39m [36morg.hibernate.orm.deprecation           [0;39m [2m:[0;39m HHH90000025: OracleDialect does not need to be specified explicitly using 'hibernate.dialect' (remove the property setting and it will be selected by default)
[2m2025-08-04 16:59:31.971[0;39m [32m INFO[0;39m [35m99538[0;39m [2m---[0;39m [2m[           main][0;39m [36morg.hibernate.orm.connections.pooling   [0;39m [2m:[0;39m HHH10001005: Database info:
	Database JDBC URL [Connecting through datasource '{
	CreateTime:"2025-08-04 16:59:07",
	ActiveCount:0,
	PoolingCount:1,
	CreateCount:1,
	DestroyCount:0,
	CloseCount:1,
	ConnectCount:1,
	Connections:[
		{ID:1667942931, ConnectTime:"2025-08-04 16:59:30", UseCount:1, LastActiveTime:"2025-08-04 16:59:31"}
	]
}']
	Database driver: undefined/unknown
	Database version: 19.3
	Autocommit mode: undefined/unknown
	Isolation level: undefined/unknown
	Minimum pool size: undefined/unknown
	Maximum pool size: undefined/unknown
[2m2025-08-04 16:59:39.023[0;39m [32m INFO[0;39m [35m99538[0;39m [2m---[0;39m [2m[           main][0;39m [36mo.h.e.t.j.p.i.JtaPlatformInitiator      [0;39m [2m:[0;39m HHH000489: No JTA platform available (set 'hibernate.transaction.jta.platform' to enable JTA platform integration)
[2m2025-08-04 16:59:39.037[0;39m [32m INFO[0;39m [35m99538[0;39m [2m---[0;39m [2m[           main][0;39m [36mj.LocalContainerEntityManagerFactoryBean[0;39m [2m:[0;39m Initialized JPA EntityManagerFactory for persistence unit 'default'
[2m2025-08-04 16:59:39.794[0;39m [32m INFO[0;39m [35m99538[0;39m [2m---[0;39m [2m[           main][0;39m [36mo.s.d.j.r.query.QueryEnhancerFactory    [0;39m [2m:[0;39m JSqlParser is in classpath; If applicable, JSqlParser will be used.
[2m2025-08-04 16:59:39.794[0;39m [32m INFO[0;39m [35m99538[0;39m [2m---[0;39m [2m[           main][0;39m [36mo.s.d.j.r.query.QueryEnhancerFactory    [0;39m [2m:[0;39m Hibernate is in classpath; If applicable, HQL parser will be used.
[2m2025-08-04 16:59:54.831[0;39m [33m WARN[0;39m [35m99538[0;39m [2m---[0;39m [2m[           main][0;39m [36m.s.s.UserDetailsServiceAutoConfiguration[0;39m [2m:[0;39m 

Using generated security password: 776a4abe-1436-4e1a-ac8e-1cccd1d1514b

This generated password is for development use only. Your security configuration must be updated before running your application in production.

[2m2025-08-04 16:59:54.875[0;39m [32m INFO[0;39m [35m99538[0;39m [2m---[0;39m [2m[           main][0;39m [36mr$InitializeUserDetailsManagerConfigurer[0;39m [2m:[0;39m Global AuthenticationManager configured with UserDetailsService bean with name inMemoryUserDetailsManager
[2m2025-08-04 16:59:56.955[0;39m [32m INFO[0;39m [35m99538[0;39m [2m---[0;39m [2m[           main][0;39m [36mctiveUserDetailsServiceAutoConfiguration[0;39m [2m:[0;39m 

Using generated security password: 776a4abe-1436-4e1a-ac8e-1cccd1d1514b

[2m2025-08-04 16:59:57.781[0;39m [32m INFO[0;39m [35m99538[0;39m [2m---[0;39m [2m[           main][0;39m [36mo.a.coyote.http11.Http11NioProtocol     [0;39m [2m:[0;39m Starting ProtocolHandler ["http-nio-8082"]
[2m2025-08-04 16:59:57.846[0;39m [32m INFO[0;39m [35m99538[0;39m [2m---[0;39m [2m[           main][0;39m [36mo.s.b.w.embedded.tomcat.TomcatWebServer [0;39m [2m:[0;39m Tomcat started on port 8082 (http) with context path '/'
[2m2025-08-04 16:59:57.878[0;39m [32m INFO[0;39m [35m99538[0;39m [2m---[0;39m [2m[           main][0;39m [36mcom.sanythadmin.AdminApplication        [0;39m [2m:[0;39m Started AdminApplication in 56.56 seconds (process running for 58.99)
[2m2025-08-04 16:59:57.883[0;39m [32mDEBUG[0;39m [35m99538[0;39m [2m---[0;39m [2m[           main][0;39m [36mc.b.m.a.DdlApplicationRunner            [0;39m [2m:[0;39m   ...  DDL start create  ...  
[2m2025-08-04 16:59:57.884[0;39m [32mDEBUG[0;39m [35m99538[0;39m [2m---[0;39m [2m[           main][0;39m [36mc.b.m.a.DdlApplicationRunner            [0;39m [2m:[0;39m   ...  DDL end create  ...  
[2m2025-08-04 17:00:01.670[0;39m [32mDEBUG[0;39m [35m99538[0;39m [2m---[0;39m [2m[           main][0;39m [36m.c.c.m.i.CustomDataPermissionInterceptor[0;39m [2m:[0;39m original SQL: SELECT COUNT( * ) AS total FROM  SYT_SYS_JWT
[2m2025-08-04 17:00:02.058[0;39m [32mDEBUG[0;39m [35m99538[0;39m [2m---[0;39m [2m[           main][0;39m [36m.c.c.m.i.CustomDataPermissionInterceptor[0;39m [2m:[0;39m SQL to parse, SQL: SELECT COUNT( * ) AS total FROM  SYT_SYS_JWT
[2m2025-08-04 17:00:02.078[0;39m [32mDEBUG[0;39m [35m99538[0;39m [2m---[0;39m [2m[           main][0;39m [36m.c.c.m.i.CustomDataPermissionInterceptor[0;39m [2m:[0;39m parse the finished SQL: SELECT COUNT(*) AS total FROM SYT_SYS_JWT
[2m2025-08-04 17:00:02.116[0;39m [32mDEBUG[0;39m [35m99538[0;39m [2m---[0;39m [2m[           main][0;39m [36mc.s.c.s.mapper.SysJwtMapper.selectCount [0;39m [2m:[0;39m ==>  Preparing: SELECT COUNT(*) AS total FROM SYT_SYS_JWT
[2m2025-08-04 17:00:02.207[0;39m [32mDEBUG[0;39m [35m99538[0;39m [2m---[0;39m [2m[           main][0;39m [36mc.s.c.s.mapper.SysJwtMapper.selectCount [0;39m [2m:[0;39m ==> Parameters: 
[2m2025-08-04 17:00:02.310[0;39m [32mDEBUG[0;39m [35m99538[0;39m [2m---[0;39m [2m[           main][0;39m [36mc.s.c.s.mapper.SysJwtMapper.selectCount [0;39m [2m:[0;39m <==      Total: 1
[2m2025-08-04 17:00:07.125[0;39m [32m INFO[0;39m [35m99538[0;39m [2m---[0;39m [2m[nio-8082-exec-1][0;39m [36mo.a.c.c.C.[Tomcat].[localhost].[/]      [0;39m [2m:[0;39m Initializing Spring DispatcherServlet 'dispatcherServlet'
[2m2025-08-04 17:00:07.125[0;39m [32m INFO[0;39m [35m99538[0;39m [2m---[0;39m [2m[nio-8082-exec-1][0;39m [36mo.s.web.servlet.DispatcherServlet       [0;39m [2m:[0;39m Initializing Servlet 'dispatcherServlet'
[2m2025-08-04 17:00:07.148[0;39m [32m INFO[0;39m [35m99538[0;39m [2m---[0;39m [2m[nio-8082-exec-1][0;39m [36mo.s.web.servlet.DispatcherServlet       [0;39m [2m:[0;39m Completed initialization in 22 ms
[2m2025-08-04 17:00:07.378[0;39m [32mDEBUG[0;39m [35m99538[0;39m [2m---[0;39m [2m[nio-8082-exec-1][0;39m [36m.c.c.m.i.CustomDataPermissionInterceptor[0;39m [2m:[0;39m original SQL: SELECT    ID,PARAM_NAME,PARAM_VALUE,PARAM_MODE,DESCRIPTION,ATTACHMENT,create_time    FROM  SYT_SYS_PARAM         WHERE  (param_mode = ?) ORDER BY create_time DESC
[2m2025-08-04 17:00:07.390[0;39m [32mDEBUG[0;39m [35m99538[0;39m [2m---[0;39m [2m[nio-8082-exec-1][0;39m [36m.c.c.m.i.CustomDataPermissionInterceptor[0;39m [2m:[0;39m SQL to parse, SQL: SELECT    ID,PARAM_NAME,PARAM_VALUE,PARAM_MODE,DESCRIPTION,ATTACHMENT,create_time    FROM  SYT_SYS_PARAM         WHERE  (param_mode = ?) ORDER BY create_time DESC
[2m2025-08-04 17:00:07.391[0;39m [32mDEBUG[0;39m [35m99538[0;39m [2m---[0;39m [2m[nio-8082-exec-1][0;39m [36m.c.c.m.i.CustomDataPermissionInterceptor[0;39m [2m:[0;39m parse the finished SQL: SELECT ID, PARAM_NAME, PARAM_VALUE, PARAM_MODE, DESCRIPTION, ATTACHMENT, create_time FROM SYT_SYS_PARAM WHERE (param_mode = ?) ORDER BY create_time DESC
[2m2025-08-04 17:00:07.393[0;39m [32mDEBUG[0;39m [35m99538[0;39m [2m---[0;39m [2m[nio-8082-exec-1][0;39m [36mc.s.c.s.m.SysParamMapper.selectList     [0;39m [2m:[0;39m ==>  Preparing: SELECT ID, PARAM_NAME, PARAM_VALUE, PARAM_MODE, DESCRIPTION, ATTACHMENT, create_time FROM SYT_SYS_PARAM WHERE (param_mode = ?) ORDER BY create_time DESC
[2m2025-08-04 17:00:07.395[0;39m [32mDEBUG[0;39m [35m99538[0;39m [2m---[0;39m [2m[nio-8082-exec-1][0;39m [36mc.s.c.s.m.SysParamMapper.selectList     [0;39m [2m:[0;39m ==> Parameters: system-info(String)
[2m2025-08-04 17:00:07.465[0;39m [32mDEBUG[0;39m [35m99538[0;39m [2m---[0;39m [2m[nio-8082-exec-1][0;39m [36mc.s.c.s.m.SysParamMapper.selectList     [0;39m [2m:[0;39m <==      Total: 0
[2m2025-08-04 17:00:22.335[0;39m [32mDEBUG[0;39m [35m99538[0;39m [2m---[0;39m [2m[nio-8082-exec-2][0;39m [36m.c.c.m.i.CustomDataPermissionInterceptor[0;39m [2m:[0;39m original SQL: SELECT    ID,PASSWORD,USERNAME,REAL_NAME,GENDER,ID_TYPE,ID_CODE,TEL_MOBILE,STATUS,ACCOUNT_EXPIRE_TIME,PASSWORD_EXPIRE_TIME,LOCK_STATUS,PASSWORD_LAST_UPDATE_TIME,LAST_USED_ROLE    FROM  SYT_SYS_ACCOUNT         WHERE  (USERNAME = ? OR TEL_MOBILE = ?)
[2m2025-08-04 17:00:22.354[0;39m [32mDEBUG[0;39m [35m99538[0;39m [2m---[0;39m [2m[nio-8082-exec-2][0;39m [36m.c.c.m.i.CustomDataPermissionInterceptor[0;39m [2m:[0;39m SQL to parse, SQL: SELECT    ID,PASSWORD,USERNAME,REAL_NAME,GENDER,ID_TYPE,ID_CODE,TEL_MOBILE,STATUS,ACCOUNT_EXPIRE_TIME,PASSWORD_EXPIRE_TIME,LOCK_STATUS,PASSWORD_LAST_UPDATE_TIME,LAST_USED_ROLE    FROM  SYT_SYS_ACCOUNT         WHERE  (USERNAME = ? OR TEL_MOBILE = ?)
[2m2025-08-04 17:00:22.357[0;39m [32mDEBUG[0;39m [35m99538[0;39m [2m---[0;39m [2m[nio-8082-exec-2][0;39m [36m.c.c.m.i.CustomDataPermissionInterceptor[0;39m [2m:[0;39m parse the finished SQL: SELECT ID, PASSWORD, USERNAME, REAL_NAME, GENDER, ID_TYPE, ID_CODE, TEL_MOBILE, STATUS, ACCOUNT_EXPIRE_TIME, PASSWORD_EXPIRE_TIME, LOCK_STATUS, PASSWORD_LAST_UPDATE_TIME, LAST_USED_ROLE FROM SYT_SYS_ACCOUNT WHERE (USERNAME = ? OR TEL_MOBILE = ?)
[2m2025-08-04 17:00:22.358[0;39m [32mDEBUG[0;39m [35m99538[0;39m [2m---[0;39m [2m[nio-8082-exec-2][0;39m [36mc.s.c.s.m.SysAccountMapper.selectList   [0;39m [2m:[0;39m ==>  Preparing: SELECT ID, PASSWORD, USERNAME, REAL_NAME, GENDER, ID_TYPE, ID_CODE, TEL_MOBILE, STATUS, ACCOUNT_EXPIRE_TIME, PASSWORD_EXPIRE_TIME, LOCK_STATUS, PASSWORD_LAST_UPDATE_TIME, LAST_USED_ROLE FROM SYT_SYS_ACCOUNT WHERE (USERNAME = ? OR TEL_MOBILE = ?)
[2m2025-08-04 17:00:22.360[0;39m [32mDEBUG[0;39m [35m99538[0;39m [2m---[0;39m [2m[nio-8082-exec-2][0;39m [36mc.s.c.s.m.SysAccountMapper.selectList   [0;39m [2m:[0;39m ==> Parameters: admin(String), admin(String)
[2m2025-08-04 17:00:22.456[0;39m [32mDEBUG[0;39m [35m99538[0;39m [2m---[0;39m [2m[nio-8082-exec-2][0;39m [36mc.s.c.s.m.SysAccountMapper.selectList   [0;39m [2m:[0;39m <==      Total: 1
[2m2025-08-04 17:00:22.457[0;39m [32mDEBUG[0;39m [35m99538[0;39m [2m---[0;39m [2m[nio-8082-exec-2][0;39m [36m.c.c.m.i.CustomDataPermissionInterceptor[0;39m [2m:[0;39m original SQL: SELECT *
        FROM SYT_SYS_ROLE
        WHERE id IN (
            SELECT role_id
            FROM SYT_SYS_ACCOUNT_ROLE
            WHERE account_id = ?
        )
[2m2025-08-04 17:00:22.468[0;39m [32mDEBUG[0;39m [35m99538[0;39m [2m---[0;39m [2m[nio-8082-exec-2][0;39m [36m.c.c.m.i.CustomDataPermissionInterceptor[0;39m [2m:[0;39m SQL to parse, SQL: SELECT *
        FROM SYT_SYS_ROLE
        WHERE id IN (
            SELECT role_id
            FROM SYT_SYS_ACCOUNT_ROLE
            WHERE account_id = ?
        )
[2m2025-08-04 17:00:22.470[0;39m [32mDEBUG[0;39m [35m99538[0;39m [2m---[0;39m [2m[nio-8082-exec-2][0;39m [36m.c.c.m.i.CustomDataPermissionInterceptor[0;39m [2m:[0;39m parse the finished SQL: SELECT * FROM SYT_SYS_ROLE WHERE id IN (SELECT role_id FROM SYT_SYS_ACCOUNT_ROLE WHERE account_id = ?)
[2m2025-08-04 17:00:22.471[0;39m [32mDEBUG[0;39m [35m99538[0;39m [2m---[0;39m [2m[nio-8082-exec-2][0;39m [36mc.s.c.s.m.S.selectByAccountId           [0;39m [2m:[0;39m ==>  Preparing: SELECT * FROM SYT_SYS_ROLE WHERE id IN (SELECT role_id FROM SYT_SYS_ACCOUNT_ROLE WHERE account_id = ?)
[2m2025-08-04 17:00:22.471[0;39m [32mDEBUG[0;39m [35m99538[0;39m [2m---[0;39m [2m[nio-8082-exec-2][0;39m [36mc.s.c.s.m.S.selectByAccountId           [0;39m [2m:[0;39m ==> Parameters: 13FF28E5AFA4D417E0630100007FE3FC(String)
[2m2025-08-04 17:00:22.867[0;39m [32mDEBUG[0;39m [35m99538[0;39m [2m---[0;39m [2m[nio-8082-exec-2][0;39m [36mc.s.c.s.m.S.selectByAccountId           [0;39m [2m:[0;39m <==      Total: 4
[2m2025-08-04 17:00:25.120[0;39m [32mDEBUG[0;39m [35m99538[0;39m [2m---[0;39m [2m[io-8082-exec-10][0;39m [36m.c.c.m.i.CustomDataPermissionInterceptor[0;39m [2m:[0;39m original SQL: SELECT     t.XGH,t.BZ1,t.BZ2,t.BZ3,t.BZ4,t.BZ5,t.BZ6,t.BZ7,t.BZ8,t.BZ9,t.BZ10,t.BZ11,t.BZ12,t.BZ13,t.BZ14,t.BZ15,t.BZ16,t.BZ17,t.BZ18,t.BZ19,t.BZ20,t.BZ21,t.BZ22,t.BZ23,t.BZ24,t.BZ25,t.BZ26,t.BZ27,t.BZ28,t.BZ29,t.BZ30,t.BZ31,t.BZ32,t.BZ33,t.BZ34,t.BZ35,t.BZ36,t.BZ37,t.BZ38,t.BZ39,t.BZ40,t.BZ41,t.BZ42,t.BZ43,t.BZ44,t.BZ45,t.BZ46,t.BZ47,t.BZ48,t.BZ49,t.BZ50,t.BZ51,t.BZ52,t.BZ53,t.BZ54,t.BZ55,t.BZ56,t.BZ57,t.BZ58,t.BZ59,t.BZ60,t.BZ61,t.BZ62,t.BZ63,t.BZ64,t.BZ65,t.BZ66,t.BZ67,t.BZ68,t.BZ69,t.BZ70,t.BZ71,t.BZ72,t.BZ73,t.BZ74,t.BZ75,t.BZ76,t.BZ77,t.BZ78,t.BZ79,t.BZ80,t.BZ81,t.BZ82,t.BZ83,t.BZ84,t.BZ85,t.BZ86,t.BZ87,t.BZ88,t.BZ89,t.BZ90,t.BZ91,t.BZ92,t.BZ93,t.BZ94,t.BZ95,t.BZ96,t.BZ97,t.BZ98,t.BZ99,t.BZ100,t.XM,t.XB,t.SJH,t.CSRQ,t.JG,t.MZMC,t.ZZMMMC,t.XQMC,t.ZJHM,t.ZJLX,t.XSLB,t.XZLX,t.RYZTID,t.USER_TYPE,t.PHOTO,t2.ID,t2.PYCCID,t2.NJID,t2.XYID,t2.ZYID,t2.BJID,t2.XGH AS joina_XGH   FROM SYT_USER_INFO  t    LEFT JOIN SYT_CODE_XSZT t1 ON (t1.ID = t.RYZTID) LEFT JOIN SYT_USER_ORG_MAP t2 ON (t2.XGH = t.XGH)     WHERE   (t.xgh IN (?) AND t2.xgh IN (?)) ORDER BY t.xgh DESC
[2m2025-08-04 17:00:25.157[0;39m [32mDEBUG[0;39m [35m99538[0;39m [2m---[0;39m [2m[io-8082-exec-10][0;39m [36m.c.c.m.i.CustomDataPermissionInterceptor[0;39m [2m:[0;39m SQL to parse, SQL: SELECT     t.XGH,t.BZ1,t.BZ2,t.BZ3,t.BZ4,t.BZ5,t.BZ6,t.BZ7,t.BZ8,t.BZ9,t.BZ10,t.BZ11,t.BZ12,t.BZ13,t.BZ14,t.BZ15,t.BZ16,t.BZ17,t.BZ18,t.BZ19,t.BZ20,t.BZ21,t.BZ22,t.BZ23,t.BZ24,t.BZ25,t.BZ26,t.BZ27,t.BZ28,t.BZ29,t.BZ30,t.BZ31,t.BZ32,t.BZ33,t.BZ34,t.BZ35,t.BZ36,t.BZ37,t.BZ38,t.BZ39,t.BZ40,t.BZ41,t.BZ42,t.BZ43,t.BZ44,t.BZ45,t.BZ46,t.BZ47,t.BZ48,t.BZ49,t.BZ50,t.BZ51,t.BZ52,t.BZ53,t.BZ54,t.BZ55,t.BZ56,t.BZ57,t.BZ58,t.BZ59,t.BZ60,t.BZ61,t.BZ62,t.BZ63,t.BZ64,t.BZ65,t.BZ66,t.BZ67,t.BZ68,t.BZ69,t.BZ70,t.BZ71,t.BZ72,t.BZ73,t.BZ74,t.BZ75,t.BZ76,t.BZ77,t.BZ78,t.BZ79,t.BZ80,t.BZ81,t.BZ82,t.BZ83,t.BZ84,t.BZ85,t.BZ86,t.BZ87,t.BZ88,t.BZ89,t.BZ90,t.BZ91,t.BZ92,t.BZ93,t.BZ94,t.BZ95,t.BZ96,t.BZ97,t.BZ98,t.BZ99,t.BZ100,t.XM,t.XB,t.SJH,t.CSRQ,t.JG,t.MZMC,t.ZZMMMC,t.XQMC,t.ZJHM,t.ZJLX,t.XSLB,t.XZLX,t.RYZTID,t.USER_TYPE,t.PHOTO,t2.ID,t2.PYCCID,t2.NJID,t2.XYID,t2.ZYID,t2.BJID,t2.XGH AS joina_XGH   FROM SYT_USER_INFO  t    LEFT JOIN SYT_CODE_XSZT t1 ON (t1.ID = t.RYZTID) LEFT JOIN SYT_USER_ORG_MAP t2 ON (t2.XGH = t.XGH)     WHERE   (t.xgh IN (?) AND t2.xgh IN (?)) ORDER BY t.xgh DESC
[2m2025-08-04 17:00:25.163[0;39m [32mDEBUG[0;39m [35m99538[0;39m [2m---[0;39m [2m[io-8082-exec-10][0;39m [36m.c.c.m.i.CustomDataPermissionInterceptor[0;39m [2m:[0;39m parse the finished SQL: SELECT t.XGH, t.BZ1, t.BZ2, t.BZ3, t.BZ4, t.BZ5, t.BZ6, t.BZ7, t.BZ8, t.BZ9, t.BZ10, t.BZ11, t.BZ12, t.BZ13, t.BZ14, t.BZ15, t.BZ16, t.BZ17, t.BZ18, t.BZ19, t.BZ20, t.BZ21, t.BZ22, t.BZ23, t.BZ24, t.BZ25, t.BZ26, t.BZ27, t.BZ28, t.BZ29, t.BZ30, t.BZ31, t.BZ32, t.BZ33, t.BZ34, t.BZ35, t.BZ36, t.BZ37, t.BZ38, t.BZ39, t.BZ40, t.BZ41, t.BZ42, t.BZ43, t.BZ44, t.BZ45, t.BZ46, t.BZ47, t.BZ48, t.BZ49, t.BZ50, t.BZ51, t.BZ52, t.BZ53, t.BZ54, t.BZ55, t.BZ56, t.BZ57, t.BZ58, t.BZ59, t.BZ60, t.BZ61, t.BZ62, t.BZ63, t.BZ64, t.BZ65, t.BZ66, t.BZ67, t.BZ68, t.BZ69, t.BZ70, t.BZ71, t.BZ72, t.BZ73, t.BZ74, t.BZ75, t.BZ76, t.BZ77, t.BZ78, t.BZ79, t.BZ80, t.BZ81, t.BZ82, t.BZ83, t.BZ84, t.BZ85, t.BZ86, t.BZ87, t.BZ88, t.BZ89, t.BZ90, t.BZ91, t.BZ92, t.BZ93, t.BZ94, t.BZ95, t.BZ96, t.BZ97, t.BZ98, t.BZ99, t.BZ100, t.XM, t.XB, t.SJH, t.CSRQ, t.JG, t.MZMC, t.ZZMMMC, t.XQMC, t.ZJHM, t.ZJLX, t.XSLB, t.XZLX, t.RYZTID, t.USER_TYPE, t.PHOTO, t2.ID, t2.PYCCID, t2.NJID, t2.XYID, t2.ZYID, t2.BJID, t2.XGH AS joina_XGH FROM SYT_USER_INFO t LEFT JOIN SYT_CODE_XSZT t1 ON (t1.ID = t.RYZTID) LEFT JOIN SYT_USER_ORG_MAP t2 ON (t2.XGH = t.XGH) WHERE (t.xgh IN (?) AND t2.xgh IN (?)) ORDER BY t.xgh DESC
[2m2025-08-04 17:00:25.171[0;39m [32mDEBUG[0;39m [35m99538[0;39m [2m---[0;39m [2m[io-8082-exec-10][0;39m [36mc.s.c.s.m.UserInfoMapper.selectJoinList [0;39m [2m:[0;39m ==>  Preparing: SELECT t.XGH, t.BZ1, t.BZ2, t.BZ3, t.BZ4, t.BZ5, t.BZ6, t.BZ7, t.BZ8, t.BZ9, t.BZ10, t.BZ11, t.BZ12, t.BZ13, t.BZ14, t.BZ15, t.BZ16, t.BZ17, t.BZ18, t.BZ19, t.BZ20, t.BZ21, t.BZ22, t.BZ23, t.BZ24, t.BZ25, t.BZ26, t.BZ27, t.BZ28, t.BZ29, t.BZ30, t.BZ31, t.BZ32, t.BZ33, t.BZ34, t.BZ35, t.BZ36, t.BZ37, t.BZ38, t.BZ39, t.BZ40, t.BZ41, t.BZ42, t.BZ43, t.BZ44, t.BZ45, t.BZ46, t.BZ47, t.BZ48, t.BZ49, t.BZ50, t.BZ51, t.BZ52, t.BZ53, t.BZ54, t.BZ55, t.BZ56, t.BZ57, t.BZ58, t.BZ59, t.BZ60, t.BZ61, t.BZ62, t.BZ63, t.BZ64, t.BZ65, t.BZ66, t.BZ67, t.BZ68, t.BZ69, t.BZ70, t.BZ71, t.BZ72, t.BZ73, t.BZ74, t.BZ75, t.BZ76, t.BZ77, t.BZ78, t.BZ79, t.BZ80, t.BZ81, t.BZ82, t.BZ83, t.BZ84, t.BZ85, t.BZ86, t.BZ87, t.BZ88, t.BZ89, t.BZ90, t.BZ91, t.BZ92, t.BZ93, t.BZ94, t.BZ95, t.BZ96, t.BZ97, t.BZ98, t.BZ99, t.BZ100, t.XM, t.XB, t.SJH, t.CSRQ, t.JG, t.MZMC, t.ZZMMMC, t.XQMC, t.ZJHM, t.ZJLX, t.XSLB, t.XZLX, t.RYZTID, t.USER_TYPE, t.PHOTO, t2.ID, t2.PYCCID, t2.NJID, t2.XYID, t2.ZYID, t2.BJID, t2.XGH AS joina_XGH FROM SYT_USER_INFO t LEFT JOIN SYT_CODE_XSZT t1 ON (t1.ID = t.RYZTID) LEFT JOIN SYT_USER_ORG_MAP t2 ON (t2.XGH = t.XGH) WHERE (t.xgh IN (?) AND t2.xgh IN (?)) ORDER BY t.xgh DESC
[2m2025-08-04 17:00:25.173[0;39m [32mDEBUG[0;39m [35m99538[0;39m [2m---[0;39m [2m[io-8082-exec-10][0;39m [36mc.s.c.s.m.UserInfoMapper.selectJoinList [0;39m [2m:[0;39m ==> Parameters: admin(String), admin(String)
[2m2025-08-04 17:00:25.272[0;39m [32mDEBUG[0;39m [35m99538[0;39m [2m---[0;39m [2m[io-8082-exec-10][0;39m [36mc.s.c.s.m.UserInfoMapper.selectJoinList [0;39m [2m:[0;39m <==      Total: 1
[2m2025-08-04 17:00:40.160[0;39m [32mDEBUG[0;39m [35m99538[0;39m [2m---[0;39m [2m[nio-8082-exec-7][0;39m [36m.c.c.m.i.CustomDataPermissionInterceptor[0;39m [2m:[0;39m original SQL: SELECT    ID,PARAM_NAME,PARAM_VALUE,PARAM_MODE,DESCRIPTION,ATTACHMENT,create_time    FROM  SYT_SYS_PARAM         WHERE  (param_mode = ?) ORDER BY create_time DESC
[2m2025-08-04 17:00:40.172[0;39m [32mDEBUG[0;39m [35m99538[0;39m [2m---[0;39m [2m[nio-8082-exec-7][0;39m [36m.c.c.m.i.CustomDataPermissionInterceptor[0;39m [2m:[0;39m SQL to parse, SQL: SELECT    ID,PARAM_NAME,PARAM_VALUE,PARAM_MODE,DESCRIPTION,ATTACHMENT,create_time    FROM  SYT_SYS_PARAM         WHERE  (param_mode = ?) ORDER BY create_time DESC
[2m2025-08-04 17:00:40.174[0;39m [32mDEBUG[0;39m [35m99538[0;39m [2m---[0;39m [2m[nio-8082-exec-7][0;39m [36m.c.c.m.i.CustomDataPermissionInterceptor[0;39m [2m:[0;39m parse the finished SQL: SELECT ID, PARAM_NAME, PARAM_VALUE, PARAM_MODE, DESCRIPTION, ATTACHMENT, create_time FROM SYT_SYS_PARAM WHERE (param_mode = ?) ORDER BY create_time DESC
[2m2025-08-04 17:00:40.175[0;39m [32mDEBUG[0;39m [35m99538[0;39m [2m---[0;39m [2m[nio-8082-exec-7][0;39m [36mc.s.c.s.m.SysParamMapper.selectList     [0;39m [2m:[0;39m ==>  Preparing: SELECT ID, PARAM_NAME, PARAM_VALUE, PARAM_MODE, DESCRIPTION, ATTACHMENT, create_time FROM SYT_SYS_PARAM WHERE (param_mode = ?) ORDER BY create_time DESC
[2m2025-08-04 17:00:40.177[0;39m [32mDEBUG[0;39m [35m99538[0;39m [2m---[0;39m [2m[nio-8082-exec-7][0;39m [36mc.s.c.s.m.SysParamMapper.selectList     [0;39m [2m:[0;39m ==> Parameters: system-info(String)
[2m2025-08-04 17:00:40.259[0;39m [32mDEBUG[0;39m [35m99538[0;39m [2m---[0;39m [2m[nio-8082-exec-7][0;39m [36mc.s.c.s.m.SysParamMapper.selectList     [0;39m [2m:[0;39m <==      Total: 0
[2m2025-08-04 17:00:41.079[0;39m [32mDEBUG[0;39m [35m99538[0;39m [2m---[0;39m [2m[nio-8082-exec-8][0;39m [36m.c.c.m.i.CustomDataPermissionInterceptor[0;39m [2m:[0;39m original SQL: SELECT     t.XGH,t.BZ1,t.BZ2,t.BZ3,t.BZ4,t.BZ5,t.BZ6,t.BZ7,t.BZ8,t.BZ9,t.BZ10,t.BZ11,t.BZ12,t.BZ13,t.BZ14,t.BZ15,t.BZ16,t.BZ17,t.BZ18,t.BZ19,t.BZ20,t.BZ21,t.BZ22,t.BZ23,t.BZ24,t.BZ25,t.BZ26,t.BZ27,t.BZ28,t.BZ29,t.BZ30,t.BZ31,t.BZ32,t.BZ33,t.BZ34,t.BZ35,t.BZ36,t.BZ37,t.BZ38,t.BZ39,t.BZ40,t.BZ41,t.BZ42,t.BZ43,t.BZ44,t.BZ45,t.BZ46,t.BZ47,t.BZ48,t.BZ49,t.BZ50,t.BZ51,t.BZ52,t.BZ53,t.BZ54,t.BZ55,t.BZ56,t.BZ57,t.BZ58,t.BZ59,t.BZ60,t.BZ61,t.BZ62,t.BZ63,t.BZ64,t.BZ65,t.BZ66,t.BZ67,t.BZ68,t.BZ69,t.BZ70,t.BZ71,t.BZ72,t.BZ73,t.BZ74,t.BZ75,t.BZ76,t.BZ77,t.BZ78,t.BZ79,t.BZ80,t.BZ81,t.BZ82,t.BZ83,t.BZ84,t.BZ85,t.BZ86,t.BZ87,t.BZ88,t.BZ89,t.BZ90,t.BZ91,t.BZ92,t.BZ93,t.BZ94,t.BZ95,t.BZ96,t.BZ97,t.BZ98,t.BZ99,t.BZ100,t.XM,t.XB,t.SJH,t.CSRQ,t.JG,t.MZMC,t.ZZMMMC,t.XQMC,t.ZJHM,t.ZJLX,t.XSLB,t.XZLX,t.RYZTID,t.USER_TYPE,t.PHOTO,t2.ID,t2.PYCCID,t2.NJID,t2.XYID,t2.ZYID,t2.BJID,t2.XGH AS joina_XGH   FROM SYT_USER_INFO  t    LEFT JOIN SYT_CODE_XSZT t1 ON (t1.ID = t.RYZTID) LEFT JOIN SYT_USER_ORG_MAP t2 ON (t2.XGH = t.XGH)     WHERE   (t.xgh IN (?) AND t2.xgh IN (?)) ORDER BY t.xgh DESC
[2m2025-08-04 17:00:41.108[0;39m [32mDEBUG[0;39m [35m99538[0;39m [2m---[0;39m [2m[nio-8082-exec-8][0;39m [36m.c.c.m.i.CustomDataPermissionInterceptor[0;39m [2m:[0;39m SQL to parse, SQL: SELECT     t.XGH,t.BZ1,t.BZ2,t.BZ3,t.BZ4,t.BZ5,t.BZ6,t.BZ7,t.BZ8,t.BZ9,t.BZ10,t.BZ11,t.BZ12,t.BZ13,t.BZ14,t.BZ15,t.BZ16,t.BZ17,t.BZ18,t.BZ19,t.BZ20,t.BZ21,t.BZ22,t.BZ23,t.BZ24,t.BZ25,t.BZ26,t.BZ27,t.BZ28,t.BZ29,t.BZ30,t.BZ31,t.BZ32,t.BZ33,t.BZ34,t.BZ35,t.BZ36,t.BZ37,t.BZ38,t.BZ39,t.BZ40,t.BZ41,t.BZ42,t.BZ43,t.BZ44,t.BZ45,t.BZ46,t.BZ47,t.BZ48,t.BZ49,t.BZ50,t.BZ51,t.BZ52,t.BZ53,t.BZ54,t.BZ55,t.BZ56,t.BZ57,t.BZ58,t.BZ59,t.BZ60,t.BZ61,t.BZ62,t.BZ63,t.BZ64,t.BZ65,t.BZ66,t.BZ67,t.BZ68,t.BZ69,t.BZ70,t.BZ71,t.BZ72,t.BZ73,t.BZ74,t.BZ75,t.BZ76,t.BZ77,t.BZ78,t.BZ79,t.BZ80,t.BZ81,t.BZ82,t.BZ83,t.BZ84,t.BZ85,t.BZ86,t.BZ87,t.BZ88,t.BZ89,t.BZ90,t.BZ91,t.BZ92,t.BZ93,t.BZ94,t.BZ95,t.BZ96,t.BZ97,t.BZ98,t.BZ99,t.BZ100,t.XM,t.XB,t.SJH,t.CSRQ,t.JG,t.MZMC,t.ZZMMMC,t.XQMC,t.ZJHM,t.ZJLX,t.XSLB,t.XZLX,t.RYZTID,t.USER_TYPE,t.PHOTO,t2.ID,t2.PYCCID,t2.NJID,t2.XYID,t2.ZYID,t2.BJID,t2.XGH AS joina_XGH   FROM SYT_USER_INFO  t    LEFT JOIN SYT_CODE_XSZT t1 ON (t1.ID = t.RYZTID) LEFT JOIN SYT_USER_ORG_MAP t2 ON (t2.XGH = t.XGH)     WHERE   (t.xgh IN (?) AND t2.xgh IN (?)) ORDER BY t.xgh DESC
[2m2025-08-04 17:00:41.111[0;39m [32mDEBUG[0;39m [35m99538[0;39m [2m---[0;39m [2m[nio-8082-exec-8][0;39m [36m.c.c.m.i.CustomDataPermissionInterceptor[0;39m [2m:[0;39m parse the finished SQL: SELECT t.XGH, t.BZ1, t.BZ2, t.BZ3, t.BZ4, t.BZ5, t.BZ6, t.BZ7, t.BZ8, t.BZ9, t.BZ10, t.BZ11, t.BZ12, t.BZ13, t.BZ14, t.BZ15, t.BZ16, t.BZ17, t.BZ18, t.BZ19, t.BZ20, t.BZ21, t.BZ22, t.BZ23, t.BZ24, t.BZ25, t.BZ26, t.BZ27, t.BZ28, t.BZ29, t.BZ30, t.BZ31, t.BZ32, t.BZ33, t.BZ34, t.BZ35, t.BZ36, t.BZ37, t.BZ38, t.BZ39, t.BZ40, t.BZ41, t.BZ42, t.BZ43, t.BZ44, t.BZ45, t.BZ46, t.BZ47, t.BZ48, t.BZ49, t.BZ50, t.BZ51, t.BZ52, t.BZ53, t.BZ54, t.BZ55, t.BZ56, t.BZ57, t.BZ58, t.BZ59, t.BZ60, t.BZ61, t.BZ62, t.BZ63, t.BZ64, t.BZ65, t.BZ66, t.BZ67, t.BZ68, t.BZ69, t.BZ70, t.BZ71, t.BZ72, t.BZ73, t.BZ74, t.BZ75, t.BZ76, t.BZ77, t.BZ78, t.BZ79, t.BZ80, t.BZ81, t.BZ82, t.BZ83, t.BZ84, t.BZ85, t.BZ86, t.BZ87, t.BZ88, t.BZ89, t.BZ90, t.BZ91, t.BZ92, t.BZ93, t.BZ94, t.BZ95, t.BZ96, t.BZ97, t.BZ98, t.BZ99, t.BZ100, t.XM, t.XB, t.SJH, t.CSRQ, t.JG, t.MZMC, t.ZZMMMC, t.XQMC, t.ZJHM, t.ZJLX, t.XSLB, t.XZLX, t.RYZTID, t.USER_TYPE, t.PHOTO, t2.ID, t2.PYCCID, t2.NJID, t2.XYID, t2.ZYID, t2.BJID, t2.XGH AS joina_XGH FROM SYT_USER_INFO t LEFT JOIN SYT_CODE_XSZT t1 ON (t1.ID = t.RYZTID) LEFT JOIN SYT_USER_ORG_MAP t2 ON (t2.XGH = t.XGH) WHERE (t.xgh IN (?) AND t2.xgh IN (?)) ORDER BY t.xgh DESC
[2m2025-08-04 17:00:41.111[0;39m [32mDEBUG[0;39m [35m99538[0;39m [2m---[0;39m [2m[nio-8082-exec-8][0;39m [36mc.s.c.s.m.UserInfoMapper.selectJoinList [0;39m [2m:[0;39m ==>  Preparing: SELECT t.XGH, t.BZ1, t.BZ2, t.BZ3, t.BZ4, t.BZ5, t.BZ6, t.BZ7, t.BZ8, t.BZ9, t.BZ10, t.BZ11, t.BZ12, t.BZ13, t.BZ14, t.BZ15, t.BZ16, t.BZ17, t.BZ18, t.BZ19, t.BZ20, t.BZ21, t.BZ22, t.BZ23, t.BZ24, t.BZ25, t.BZ26, t.BZ27, t.BZ28, t.BZ29, t.BZ30, t.BZ31, t.BZ32, t.BZ33, t.BZ34, t.BZ35, t.BZ36, t.BZ37, t.BZ38, t.BZ39, t.BZ40, t.BZ41, t.BZ42, t.BZ43, t.BZ44, t.BZ45, t.BZ46, t.BZ47, t.BZ48, t.BZ49, t.BZ50, t.BZ51, t.BZ52, t.BZ53, t.BZ54, t.BZ55, t.BZ56, t.BZ57, t.BZ58, t.BZ59, t.BZ60, t.BZ61, t.BZ62, t.BZ63, t.BZ64, t.BZ65, t.BZ66, t.BZ67, t.BZ68, t.BZ69, t.BZ70, t.BZ71, t.BZ72, t.BZ73, t.BZ74, t.BZ75, t.BZ76, t.BZ77, t.BZ78, t.BZ79, t.BZ80, t.BZ81, t.BZ82, t.BZ83, t.BZ84, t.BZ85, t.BZ86, t.BZ87, t.BZ88, t.BZ89, t.BZ90, t.BZ91, t.BZ92, t.BZ93, t.BZ94, t.BZ95, t.BZ96, t.BZ97, t.BZ98, t.BZ99, t.BZ100, t.XM, t.XB, t.SJH, t.CSRQ, t.JG, t.MZMC, t.ZZMMMC, t.XQMC, t.ZJHM, t.ZJLX, t.XSLB, t.XZLX, t.RYZTID, t.USER_TYPE, t.PHOTO, t2.ID, t2.PYCCID, t2.NJID, t2.XYID, t2.ZYID, t2.BJID, t2.XGH AS joina_XGH FROM SYT_USER_INFO t LEFT JOIN SYT_CODE_XSZT t1 ON (t1.ID = t.RYZTID) LEFT JOIN SYT_USER_ORG_MAP t2 ON (t2.XGH = t.XGH) WHERE (t.xgh IN (?) AND t2.xgh IN (?)) ORDER BY t.xgh DESC
[2m2025-08-04 17:00:41.112[0;39m [32mDEBUG[0;39m [35m99538[0;39m [2m---[0;39m [2m[nio-8082-exec-8][0;39m [36mc.s.c.s.m.UserInfoMapper.selectJoinList [0;39m [2m:[0;39m ==> Parameters: admin(String), admin(String)
[2m2025-08-04 17:00:41.204[0;39m [32mDEBUG[0;39m [35m99538[0;39m [2m---[0;39m [2m[nio-8082-exec-8][0;39m [36mc.s.c.s.m.UserInfoMapper.selectJoinList [0;39m [2m:[0;39m <==      Total: 1
[2m2025-08-04 17:00:43.735[0;39m [32mDEBUG[0;39m [35m99538[0;39m [2m---[0;39m [2m[nio-8082-exec-3][0;39m [36m.c.c.m.i.CustomDataPermissionInterceptor[0;39m [2m:[0;39m original SQL: SELECT    ID,GROUP_NAME,SHOW_FLAG,ACTIVE_FLAG,ADD_FLAG,MIN_NUM,MAX_NUM,LIST_FLAG,MODIFY_FLAG,ICON,EMPTY_DATA_HIDE,SORT,USER_TYPE,LIST_GROUP_ID    FROM  SYT_DICTIONARY_GROUP         WHERE  (user_type = ?) ORDER BY sort ASC
[2m2025-08-04 17:00:43.744[0;39m [32mDEBUG[0;39m [35m99538[0;39m [2m---[0;39m [2m[nio-8082-exec-3][0;39m [36m.c.c.m.i.CustomDataPermissionInterceptor[0;39m [2m:[0;39m SQL to parse, SQL: SELECT    ID,GROUP_NAME,SHOW_FLAG,ACTIVE_FLAG,ADD_FLAG,MIN_NUM,MAX_NUM,LIST_FLAG,MODIFY_FLAG,ICON,EMPTY_DATA_HIDE,SORT,USER_TYPE,LIST_GROUP_ID    FROM  SYT_DICTIONARY_GROUP         WHERE  (user_type = ?) ORDER BY sort ASC
[2m2025-08-04 17:00:43.746[0;39m [32mDEBUG[0;39m [35m99538[0;39m [2m---[0;39m [2m[nio-8082-exec-3][0;39m [36m.c.c.m.i.CustomDataPermissionInterceptor[0;39m [2m:[0;39m parse the finished SQL: SELECT ID, GROUP_NAME, SHOW_FLAG, ACTIVE_FLAG, ADD_FLAG, MIN_NUM, MAX_NUM, LIST_FLAG, MODIFY_FLAG, ICON, EMPTY_DATA_HIDE, SORT, USER_TYPE, LIST_GROUP_ID FROM SYT_DICTIONARY_GROUP WHERE (user_type = ?) ORDER BY sort ASC
[2m2025-08-04 17:00:43.746[0;39m [32mDEBUG[0;39m [35m99538[0;39m [2m---[0;39m [2m[nio-8082-exec-3][0;39m [36mc.s.p.d.m.D.selectList                  [0;39m [2m:[0;39m ==>  Preparing: SELECT ID, GROUP_NAME, SHOW_FLAG, ACTIVE_FLAG, ADD_FLAG, MIN_NUM, MAX_NUM, LIST_FLAG, MODIFY_FLAG, ICON, EMPTY_DATA_HIDE, SORT, USER_TYPE, LIST_GROUP_ID FROM SYT_DICTIONARY_GROUP WHERE (user_type = ?) ORDER BY sort ASC
[2m2025-08-04 17:00:43.751[0;39m [32mDEBUG[0;39m [35m99538[0;39m [2m---[0;39m [2m[nio-8082-exec-3][0;39m [36mc.s.p.d.m.D.selectList                  [0;39m [2m:[0;39m ==> Parameters: 2(Integer)
[2m2025-08-04 17:00:43.804[0;39m [32mDEBUG[0;39m [35m99538[0;39m [2m---[0;39m [2m[nio-8082-exec-2][0;39m [36m.c.c.m.i.CustomDataPermissionInterceptor[0;39m [2m:[0;39m original SQL: SELECT    ID,CODE,NAME,SORT,DESCRIPTION,CODE_TYPE    FROM  SYT_CODE_COMMON         WHERE  (code_type IN (?)) ORDER BY sort ASC
[2m2025-08-04 17:00:43.810[0;39m [32mDEBUG[0;39m [35m99538[0;39m [2m---[0;39m [2m[nio-8082-exec-2][0;39m [36m.c.c.m.i.CustomDataPermissionInterceptor[0;39m [2m:[0;39m SQL to parse, SQL: SELECT    ID,CODE,NAME,SORT,DESCRIPTION,CODE_TYPE    FROM  SYT_CODE_COMMON         WHERE  (code_type IN (?)) ORDER BY sort ASC
[2m2025-08-04 17:00:43.812[0;39m [32mDEBUG[0;39m [35m99538[0;39m [2m---[0;39m [2m[nio-8082-exec-2][0;39m [36m.c.c.m.i.CustomDataPermissionInterceptor[0;39m [2m:[0;39m parse the finished SQL: SELECT ID, CODE, NAME, SORT, DESCRIPTION, CODE_TYPE FROM SYT_CODE_COMMON WHERE (code_type IN (?)) ORDER BY sort ASC
[2m2025-08-04 17:00:43.858[0;39m [32mDEBUG[0;39m [35m99538[0;39m [2m---[0;39m [2m[nio-8082-exec-3][0;39m [36mc.s.p.d.m.D.selectList                  [0;39m [2m:[0;39m <==      Total: 5
[2m2025-08-04 17:00:43.858[0;39m [32mDEBUG[0;39m [35m99538[0;39m [2m---[0;39m [2m[nio-8082-exec-2][0;39m [36mc.s.p.c.m.CodeCommonMapper.selectList   [0;39m [2m:[0;39m ==>  Preparing: SELECT ID, CODE, NAME, SORT, DESCRIPTION, CODE_TYPE FROM SYT_CODE_COMMON WHERE (code_type IN (?)) ORDER BY sort ASC
[2m2025-08-04 17:00:43.859[0;39m [32mDEBUG[0;39m [35m99538[0;39m [2m---[0;39m [2m[nio-8082-exec-2][0;39m [36mc.s.p.c.m.CodeCommonMapper.selectList   [0;39m [2m:[0;39m ==> Parameters: listDicUrl(String)
[2m2025-08-04 17:00:43.932[0;39m [32mDEBUG[0;39m [35m99538[0;39m [2m---[0;39m [2m[nio-8082-exec-2][0;39m [36mc.s.p.c.m.CodeCommonMapper.selectList   [0;39m [2m:[0;39m <==      Total: 0
[2m2025-08-04 17:00:44.275[0;39m [32mDEBUG[0;39m [35m99538[0;39m [2m---[0;39m [2m[nio-8082-exec-9][0;39m [36m.c.c.m.i.CustomDataPermissionInterceptor[0;39m [2m:[0;39m original SQL: SELECT    ID,CODE,NAME,SORT,TEXT_FIELD,VALUE_FIELD    FROM  SYT_CODE_TYPE              ORDER BY sort DESC
[2m2025-08-04 17:00:44.283[0;39m [32mDEBUG[0;39m [35m99538[0;39m [2m---[0;39m [2m[nio-8082-exec-9][0;39m [36m.c.c.m.i.CustomDataPermissionInterceptor[0;39m [2m:[0;39m SQL to parse, SQL: SELECT    ID,CODE,NAME,SORT,TEXT_FIELD,VALUE_FIELD    FROM  SYT_CODE_TYPE              ORDER BY sort DESC
[2m2025-08-04 17:00:44.285[0;39m [32mDEBUG[0;39m [35m99538[0;39m [2m---[0;39m [2m[nio-8082-exec-9][0;39m [36m.c.c.m.i.CustomDataPermissionInterceptor[0;39m [2m:[0;39m parse the finished SQL: SELECT ID, CODE, NAME, SORT, TEXT_FIELD, VALUE_FIELD FROM SYT_CODE_TYPE ORDER BY sort DESC
[2m2025-08-04 17:00:44.286[0;39m [32mDEBUG[0;39m [35m99538[0;39m [2m---[0;39m [2m[nio-8082-exec-9][0;39m [36mc.s.p.c.m.CodeTypeMapper.selectList     [0;39m [2m:[0;39m ==>  Preparing: SELECT ID, CODE, NAME, SORT, TEXT_FIELD, VALUE_FIELD FROM SYT_CODE_TYPE ORDER BY sort DESC
[2m2025-08-04 17:00:44.286[0;39m [32mDEBUG[0;39m [35m99538[0;39m [2m---[0;39m [2m[nio-8082-exec-9][0;39m [36mc.s.p.c.m.CodeTypeMapper.selectList     [0;39m [2m:[0;39m ==> Parameters: 
[2m2025-08-04 17:00:44.637[0;39m [32mDEBUG[0;39m [35m99538[0;39m [2m---[0;39m [2m[nio-8082-exec-9][0;39m [36mc.s.p.c.m.CodeTypeMapper.selectList     [0;39m [2m:[0;39m <==      Total: 37
[2m2025-08-04 17:00:45.045[0;39m [32mDEBUG[0;39m [35m99538[0;39m [2m---[0;39m [2m[nio-8082-exec-6][0;39m [36m.c.c.m.i.CustomDataPermissionInterceptor[0;39m [2m:[0;39m original SQL: SELECT    ID,GROUP_NAME,SHOW_FLAG,ACTIVE_FLAG,ADD_FLAG,MIN_NUM,MAX_NUM,LIST_FLAG,MODIFY_FLAG,ICON,EMPTY_DATA_HIDE,SORT,USER_TYPE,LIST_GROUP_ID    FROM  SYT_DICTIONARY_GROUP         WHERE  (user_type = ?) ORDER BY sort ASC
[2m2025-08-04 17:00:45.057[0;39m [32mDEBUG[0;39m [35m99538[0;39m [2m---[0;39m [2m[nio-8082-exec-6][0;39m [36m.c.c.m.i.CustomDataPermissionInterceptor[0;39m [2m:[0;39m SQL to parse, SQL: SELECT    ID,GROUP_NAME,SHOW_FLAG,ACTIVE_FLAG,ADD_FLAG,MIN_NUM,MAX_NUM,LIST_FLAG,MODIFY_FLAG,ICON,EMPTY_DATA_HIDE,SORT,USER_TYPE,LIST_GROUP_ID    FROM  SYT_DICTIONARY_GROUP         WHERE  (user_type = ?) ORDER BY sort ASC
[2m2025-08-04 17:00:45.059[0;39m [32mDEBUG[0;39m [35m99538[0;39m [2m---[0;39m [2m[nio-8082-exec-6][0;39m [36m.c.c.m.i.CustomDataPermissionInterceptor[0;39m [2m:[0;39m parse the finished SQL: SELECT ID, GROUP_NAME, SHOW_FLAG, ACTIVE_FLAG, ADD_FLAG, MIN_NUM, MAX_NUM, LIST_FLAG, MODIFY_FLAG, ICON, EMPTY_DATA_HIDE, SORT, USER_TYPE, LIST_GROUP_ID FROM SYT_DICTIONARY_GROUP WHERE (user_type = ?) ORDER BY sort ASC
[2m2025-08-04 17:00:45.059[0;39m [32mDEBUG[0;39m [35m99538[0;39m [2m---[0;39m [2m[nio-8082-exec-6][0;39m [36mc.s.p.d.m.D.selectList                  [0;39m [2m:[0;39m ==>  Preparing: SELECT ID, GROUP_NAME, SHOW_FLAG, ACTIVE_FLAG, ADD_FLAG, MIN_NUM, MAX_NUM, LIST_FLAG, MODIFY_FLAG, ICON, EMPTY_DATA_HIDE, SORT, USER_TYPE, LIST_GROUP_ID FROM SYT_DICTIONARY_GROUP WHERE (user_type = ?) ORDER BY sort ASC
[2m2025-08-04 17:00:45.060[0;39m [32mDEBUG[0;39m [35m99538[0;39m [2m---[0;39m [2m[nio-8082-exec-6][0;39m [36mc.s.p.d.m.D.selectList                  [0;39m [2m:[0;39m ==> Parameters: 2(Integer)
[2m2025-08-04 17:00:45.396[0;39m [32mDEBUG[0;39m [35m99538[0;39m [2m---[0;39m [2m[nio-8082-exec-5][0;39m [36m.c.c.m.i.CustomDataPermissionInterceptor[0;39m [2m:[0;39m original SQL: SELECT    ID,GROUP_NAME,LOAD_DATA_URL,OPERATION_DATA_URL,DELETE_DATA_URL,DICTIONARY_FIELD_URL    FROM  SYT_LIST_GROUP_CONFIG
[2m2025-08-04 17:00:45.403[0;39m [32mDEBUG[0;39m [35m99538[0;39m [2m---[0;39m [2m[nio-8082-exec-5][0;39m [36m.c.c.m.i.CustomDataPermissionInterceptor[0;39m [2m:[0;39m SQL to parse, SQL: SELECT    ID,GROUP_NAME,LOAD_DATA_URL,OPERATION_DATA_URL,DELETE_DATA_URL,DICTIONARY_FIELD_URL    FROM  SYT_LIST_GROUP_CONFIG
[2m2025-08-04 17:00:45.404[0;39m [32mDEBUG[0;39m [35m99538[0;39m [2m---[0;39m [2m[nio-8082-exec-5][0;39m [36m.c.c.m.i.CustomDataPermissionInterceptor[0;39m [2m:[0;39m parse the finished SQL: SELECT ID, GROUP_NAME, LOAD_DATA_URL, OPERATION_DATA_URL, DELETE_DATA_URL, DICTIONARY_FIELD_URL FROM SYT_LIST_GROUP_CONFIG
[2m2025-08-04 17:00:45.405[0;39m [32mDEBUG[0;39m [35m99538[0;39m [2m---[0;39m [2m[nio-8082-exec-6][0;39m [36mc.s.p.d.m.D.selectList                  [0;39m [2m:[0;39m <==      Total: 5
[2m2025-08-04 17:00:45.406[0;39m [32mDEBUG[0;39m [35m99538[0;39m [2m---[0;39m [2m[nio-8082-exec-5][0;39m [36mc.s.p.d.m.L.selectList                  [0;39m [2m:[0;39m ==>  Preparing: SELECT ID, GROUP_NAME, LOAD_DATA_URL, OPERATION_DATA_URL, DELETE_DATA_URL, DICTIONARY_FIELD_URL FROM SYT_LIST_GROUP_CONFIG
[2m2025-08-04 17:00:45.406[0;39m [32mDEBUG[0;39m [35m99538[0;39m [2m---[0;39m [2m[nio-8082-exec-5][0;39m [36mc.s.p.d.m.L.selectList                  [0;39m [2m:[0;39m ==> Parameters: 
[2m2025-08-04 17:00:45.488[0;39m [32mDEBUG[0;39m [35m99538[0;39m [2m---[0;39m [2m[nio-8082-exec-5][0;39m [36mc.s.p.d.m.L.selectList                  [0;39m [2m:[0;39m <==      Total: 1
[2m2025-08-04 17:00:46.032[0;39m [32mDEBUG[0;39m [35m99538[0;39m [2m---[0;39m [2m[nio-8082-exec-1][0;39m [36m.c.c.m.i.CustomDataPermissionInterceptor[0;39m [2m:[0;39m original SQL: SELECT    ID,CODE,NAME,SORT,TEXT_FIELD,VALUE_FIELD    FROM  SYT_CODE_TYPE              ORDER BY sort DESC
[2m2025-08-04 17:00:46.041[0;39m [32mDEBUG[0;39m [35m99538[0;39m [2m---[0;39m [2m[nio-8082-exec-1][0;39m [36m.c.c.m.i.CustomDataPermissionInterceptor[0;39m [2m:[0;39m SQL to parse, SQL: SELECT    ID,CODE,NAME,SORT,TEXT_FIELD,VALUE_FIELD    FROM  SYT_CODE_TYPE              ORDER BY sort DESC
[2m2025-08-04 17:00:46.044[0;39m [32mDEBUG[0;39m [35m99538[0;39m [2m---[0;39m [2m[nio-8082-exec-1][0;39m [36m.c.c.m.i.CustomDataPermissionInterceptor[0;39m [2m:[0;39m parse the finished SQL: SELECT ID, CODE, NAME, SORT, TEXT_FIELD, VALUE_FIELD FROM SYT_CODE_TYPE ORDER BY sort DESC
[2m2025-08-04 17:00:46.045[0;39m [32mDEBUG[0;39m [35m99538[0;39m [2m---[0;39m [2m[nio-8082-exec-1][0;39m [36mc.s.p.c.m.CodeTypeMapper.selectList     [0;39m [2m:[0;39m ==>  Preparing: SELECT ID, CODE, NAME, SORT, TEXT_FIELD, VALUE_FIELD FROM SYT_CODE_TYPE ORDER BY sort DESC
[2m2025-08-04 17:00:46.045[0;39m [32mDEBUG[0;39m [35m99538[0;39m [2m---[0;39m [2m[nio-8082-exec-1][0;39m [36mc.s.p.c.m.CodeTypeMapper.selectList     [0;39m [2m:[0;39m ==> Parameters: 
[2m2025-08-04 17:00:46.048[0;39m [32mDEBUG[0;39m [35m99538[0;39m [2m---[0;39m [2m[io-8082-exec-10][0;39m [36m.c.c.m.i.CustomDataPermissionInterceptor[0;39m [2m:[0;39m original SQL: SELECT    ID,GROUP_NAME,SHOW_FLAG,ACTIVE_FLAG,ADD_FLAG,MIN_NUM,MAX_NUM,LIST_FLAG,MODIFY_FLAG,ICON,EMPTY_DATA_HIDE,SORT,USER_TYPE,LIST_GROUP_ID    FROM  SYT_DICTIONARY_GROUP         WHERE  (user_type = ?)
[2m2025-08-04 17:00:46.057[0;39m [32mDEBUG[0;39m [35m99538[0;39m [2m---[0;39m [2m[io-8082-exec-10][0;39m [36m.c.c.m.i.CustomDataPermissionInterceptor[0;39m [2m:[0;39m SQL to parse, SQL: SELECT    ID,GROUP_NAME,SHOW_FLAG,ACTIVE_FLAG,ADD_FLAG,MIN_NUM,MAX_NUM,LIST_FLAG,MODIFY_FLAG,ICON,EMPTY_DATA_HIDE,SORT,USER_TYPE,LIST_GROUP_ID    FROM  SYT_DICTIONARY_GROUP         WHERE  (user_type = ?)
[2m2025-08-04 17:00:46.060[0;39m [32mDEBUG[0;39m [35m99538[0;39m [2m---[0;39m [2m[io-8082-exec-10][0;39m [36m.c.c.m.i.CustomDataPermissionInterceptor[0;39m [2m:[0;39m parse the finished SQL: SELECT ID, GROUP_NAME, SHOW_FLAG, ACTIVE_FLAG, ADD_FLAG, MIN_NUM, MAX_NUM, LIST_FLAG, MODIFY_FLAG, ICON, EMPTY_DATA_HIDE, SORT, USER_TYPE, LIST_GROUP_ID FROM SYT_DICTIONARY_GROUP WHERE (user_type = ?)
[2m2025-08-04 17:00:46.085[0;39m [32mDEBUG[0;39m [35m99538[0;39m [2m---[0;39m [2m[io-8082-exec-10][0;39m [36mc.s.p.d.m.D.selectList_mpCount          [0;39m [2m:[0;39m ==>  Preparing: SELECT COUNT(*) AS total FROM SYT_DICTIONARY_GROUP WHERE (user_type = ?)
[2m2025-08-04 17:00:46.086[0;39m [32mDEBUG[0;39m [35m99538[0;39m [2m---[0;39m [2m[io-8082-exec-10][0;39m [36mc.s.p.d.m.D.selectList_mpCount          [0;39m [2m:[0;39m ==> Parameters: 2(Integer)
[2m2025-08-04 17:00:46.343[0;39m [32mDEBUG[0;39m [35m99538[0;39m [2m---[0;39m [2m[nio-8082-exec-1][0;39m [36mc.s.p.c.m.CodeTypeMapper.selectList     [0;39m [2m:[0;39m <==      Total: 37
[2m2025-08-04 17:00:46.435[0;39m [32mDEBUG[0;39m [35m99538[0;39m [2m---[0;39m [2m[io-8082-exec-10][0;39m [36mc.s.p.d.m.D.selectList_mpCount          [0;39m [2m:[0;39m <==      Total: 1
[2m2025-08-04 17:00:46.444[0;39m [32mDEBUG[0;39m [35m99538[0;39m [2m---[0;39m [2m[nio-8082-exec-3][0;39m [36m.c.c.m.i.CustomDataPermissionInterceptor[0;39m [2m:[0;39m original SQL: SELECT    ID,GROUP_NAME,LOAD_DATA_URL,OPERATION_DATA_URL,DELETE_DATA_URL,DICTIONARY_FIELD_URL    FROM  SYT_LIST_GROUP_CONFIG
[2m2025-08-04 17:00:46.453[0;39m [32mDEBUG[0;39m [35m99538[0;39m [2m---[0;39m [2m[nio-8082-exec-3][0;39m [36m.c.c.m.i.CustomDataPermissionInterceptor[0;39m [2m:[0;39m SQL to parse, SQL: SELECT    ID,GROUP_NAME,LOAD_DATA_URL,OPERATION_DATA_URL,DELETE_DATA_URL,DICTIONARY_FIELD_URL    FROM  SYT_LIST_GROUP_CONFIG
[2m2025-08-04 17:00:46.454[0;39m [32mDEBUG[0;39m [35m99538[0;39m [2m---[0;39m [2m[nio-8082-exec-3][0;39m [36m.c.c.m.i.CustomDataPermissionInterceptor[0;39m [2m:[0;39m parse the finished SQL: SELECT ID, GROUP_NAME, LOAD_DATA_URL, OPERATION_DATA_URL, DELETE_DATA_URL, DICTIONARY_FIELD_URL FROM SYT_LIST_GROUP_CONFIG
[2m2025-08-04 17:00:46.454[0;39m [32mDEBUG[0;39m [35m99538[0;39m [2m---[0;39m [2m[nio-8082-exec-3][0;39m [36mc.s.p.d.m.L.selectList                  [0;39m [2m:[0;39m ==>  Preparing: SELECT ID, GROUP_NAME, LOAD_DATA_URL, OPERATION_DATA_URL, DELETE_DATA_URL, DICTIONARY_FIELD_URL FROM SYT_LIST_GROUP_CONFIG
[2m2025-08-04 17:00:46.454[0;39m [32mDEBUG[0;39m [35m99538[0;39m [2m---[0;39m [2m[nio-8082-exec-3][0;39m [36mc.s.p.d.m.L.selectList                  [0;39m [2m:[0;39m ==> Parameters: 
[2m2025-08-04 17:00:46.472[0;39m [32mDEBUG[0;39m [35m99538[0;39m [2m---[0;39m [2m[io-8082-exec-10][0;39m [36mc.s.p.d.m.D.selectList                  [0;39m [2m:[0;39m ==>  Preparing: SELECT * FROM ( SELECT TMP.*, ROWNUM ROW_ID FROM ( SELECT ID, GROUP_NAME, SHOW_FLAG, ACTIVE_FLAG, ADD_FLAG, MIN_NUM, MAX_NUM, LIST_FLAG, MODIFY_FLAG, ICON, EMPTY_DATA_HIDE, SORT, USER_TYPE, LIST_GROUP_ID FROM SYT_DICTIONARY_GROUP WHERE (user_type = ?) ORDER BY sort ASC ) TMP WHERE ROWNUM <=?) WHERE ROW_ID > ?
[2m2025-08-04 17:00:46.474[0;39m [32mDEBUG[0;39m [35m99538[0;39m [2m---[0;39m [2m[io-8082-exec-10][0;39m [36mc.s.p.d.m.D.selectList                  [0;39m [2m:[0;39m ==> Parameters: 2(Integer), 10(Long), 0(Long)
[2m2025-08-04 17:00:46.524[0;39m [32mDEBUG[0;39m [35m99538[0;39m [2m---[0;39m [2m[nio-8082-exec-3][0;39m [36mc.s.p.d.m.L.selectList                  [0;39m [2m:[0;39m <==      Total: 1
[2m2025-08-04 17:00:46.818[0;39m [32mDEBUG[0;39m [35m99538[0;39m [2m---[0;39m [2m[io-8082-exec-10][0;39m [36mc.s.p.d.m.D.selectList                  [0;39m [2m:[0;39m <==      Total: 5
[2m2025-08-04 17:00:46.826[0;39m [32mDEBUG[0;39m [35m99538[0;39m [2m---[0;39m [2m[io-8082-exec-10][0;39m [36m.c.c.m.i.CustomDataPermissionInterceptor[0;39m [2m:[0;39m original SQL: SELECT    ID,GROUP_ID,ROLE_ID    FROM  SYT_DICTIONARY_GROUP_HIDE_ROLE         WHERE  (GROUP_ID = ?)
[2m2025-08-04 17:00:46.835[0;39m [32mDEBUG[0;39m [35m99538[0;39m [2m---[0;39m [2m[io-8082-exec-10][0;39m [36m.c.c.m.i.CustomDataPermissionInterceptor[0;39m [2m:[0;39m SQL to parse, SQL: SELECT    ID,GROUP_ID,ROLE_ID    FROM  SYT_DICTIONARY_GROUP_HIDE_ROLE         WHERE  (GROUP_ID = ?)
[2m2025-08-04 17:00:46.837[0;39m [32mDEBUG[0;39m [35m99538[0;39m [2m---[0;39m [2m[io-8082-exec-10][0;39m [36m.c.c.m.i.CustomDataPermissionInterceptor[0;39m [2m:[0;39m parse the finished SQL: SELECT ID, GROUP_ID, ROLE_ID FROM SYT_DICTIONARY_GROUP_HIDE_ROLE WHERE (GROUP_ID = ?)
[2m2025-08-04 17:00:46.838[0;39m [32mDEBUG[0;39m [35m99538[0;39m [2m---[0;39m [2m[io-8082-exec-10][0;39m [36mc.s.p.d.m.D.selectList                  [0;39m [2m:[0;39m ==>  Preparing: SELECT ID, GROUP_ID, ROLE_ID FROM SYT_DICTIONARY_GROUP_HIDE_ROLE WHERE (GROUP_ID = ?)
[2m2025-08-04 17:00:46.838[0;39m [32mDEBUG[0;39m [35m99538[0;39m [2m---[0;39m [2m[io-8082-exec-10][0;39m [36mc.s.p.d.m.D.selectList                  [0;39m [2m:[0;39m ==> Parameters: 7d6988d4498a122de59589537bb45cee(String)
[2m2025-08-04 17:00:46.911[0;39m [32mDEBUG[0;39m [35m99538[0;39m [2m---[0;39m [2m[io-8082-exec-10][0;39m [36mc.s.p.d.m.D.selectList                  [0;39m [2m:[0;39m <==      Total: 0
[2m2025-08-04 17:00:46.912[0;39m [32mDEBUG[0;39m [35m99538[0;39m [2m---[0;39m [2m[io-8082-exec-10][0;39m [36m.c.c.m.i.CustomDataPermissionInterceptor[0;39m [2m:[0;39m original SQL: SELECT    ID,GROUP_ID,EDU_LEVEL_ID    FROM  SYT_DICTIONARY_GROUP_EDU_LEVEL         WHERE  (GROUP_ID = ?)
[2m2025-08-04 17:00:46.919[0;39m [32mDEBUG[0;39m [35m99538[0;39m [2m---[0;39m [2m[io-8082-exec-10][0;39m [36m.c.c.m.i.CustomDataPermissionInterceptor[0;39m [2m:[0;39m SQL to parse, SQL: SELECT    ID,GROUP_ID,EDU_LEVEL_ID    FROM  SYT_DICTIONARY_GROUP_EDU_LEVEL         WHERE  (GROUP_ID = ?)
[2m2025-08-04 17:00:46.921[0;39m [32mDEBUG[0;39m [35m99538[0;39m [2m---[0;39m [2m[io-8082-exec-10][0;39m [36m.c.c.m.i.CustomDataPermissionInterceptor[0;39m [2m:[0;39m parse the finished SQL: SELECT ID, GROUP_ID, EDU_LEVEL_ID FROM SYT_DICTIONARY_GROUP_EDU_LEVEL WHERE (GROUP_ID = ?)
[2m2025-08-04 17:00:46.922[0;39m [32mDEBUG[0;39m [35m99538[0;39m [2m---[0;39m [2m[io-8082-exec-10][0;39m [36mc.s.p.d.m.D.selectList                  [0;39m [2m:[0;39m ==>  Preparing: SELECT ID, GROUP_ID, EDU_LEVEL_ID FROM SYT_DICTIONARY_GROUP_EDU_LEVEL WHERE (GROUP_ID = ?)
[2m2025-08-04 17:00:46.922[0;39m [32mDEBUG[0;39m [35m99538[0;39m [2m---[0;39m [2m[io-8082-exec-10][0;39m [36mc.s.p.d.m.D.selectList                  [0;39m [2m:[0;39m ==> Parameters: 7d6988d4498a122de59589537bb45cee(String)
[2m2025-08-04 17:00:46.991[0;39m [32mDEBUG[0;39m [35m99538[0;39m [2m---[0;39m [2m[io-8082-exec-10][0;39m [36mc.s.p.d.m.D.selectList                  [0;39m [2m:[0;39m <==      Total: 0
[2m2025-08-04 17:00:46.993[0;39m [32mDEBUG[0;39m [35m99538[0;39m [2m---[0;39m [2m[io-8082-exec-10][0;39m [36m.c.c.m.i.CustomDataPermissionInterceptor[0;39m [2m:[0;39m original SQL: SELECT    ID,GROUP_ID,ROLE_ID    FROM  SYT_DICTIONARY_GROUP_HIDE_ROLE         WHERE  (GROUP_ID = ?)
[2m2025-08-04 17:00:47.003[0;39m [32mDEBUG[0;39m [35m99538[0;39m [2m---[0;39m [2m[io-8082-exec-10][0;39m [36m.c.c.m.i.CustomDataPermissionInterceptor[0;39m [2m:[0;39m SQL to parse, SQL: SELECT    ID,GROUP_ID,ROLE_ID    FROM  SYT_DICTIONARY_GROUP_HIDE_ROLE         WHERE  (GROUP_ID = ?)
[2m2025-08-04 17:00:47.006[0;39m [32mDEBUG[0;39m [35m99538[0;39m [2m---[0;39m [2m[io-8082-exec-10][0;39m [36m.c.c.m.i.CustomDataPermissionInterceptor[0;39m [2m:[0;39m parse the finished SQL: SELECT ID, GROUP_ID, ROLE_ID FROM SYT_DICTIONARY_GROUP_HIDE_ROLE WHERE (GROUP_ID = ?)
[2m2025-08-04 17:00:47.007[0;39m [32mDEBUG[0;39m [35m99538[0;39m [2m---[0;39m [2m[io-8082-exec-10][0;39m [36mc.s.p.d.m.D.selectList                  [0;39m [2m:[0;39m ==>  Preparing: SELECT ID, GROUP_ID, ROLE_ID FROM SYT_DICTIONARY_GROUP_HIDE_ROLE WHERE (GROUP_ID = ?)
[2m2025-08-04 17:00:47.007[0;39m [32mDEBUG[0;39m [35m99538[0;39m [2m---[0;39m [2m[io-8082-exec-10][0;39m [36mc.s.p.d.m.D.selectList                  [0;39m [2m:[0;39m ==> Parameters: e0594c19ea31172b0ce7a37d3381d5de(String)
[2m2025-08-04 17:00:47.079[0;39m [32mDEBUG[0;39m [35m99538[0;39m [2m---[0;39m [2m[io-8082-exec-10][0;39m [36mc.s.p.d.m.D.selectList                  [0;39m [2m:[0;39m <==      Total: 0
[2m2025-08-04 17:00:47.081[0;39m [32mDEBUG[0;39m [35m99538[0;39m [2m---[0;39m [2m[io-8082-exec-10][0;39m [36m.c.c.m.i.CustomDataPermissionInterceptor[0;39m [2m:[0;39m original SQL: SELECT    ID,GROUP_ID,EDU_LEVEL_ID    FROM  SYT_DICTIONARY_GROUP_EDU_LEVEL         WHERE  (GROUP_ID = ?)
[2m2025-08-04 17:00:47.087[0;39m [32mDEBUG[0;39m [35m99538[0;39m [2m---[0;39m [2m[io-8082-exec-10][0;39m [36m.c.c.m.i.CustomDataPermissionInterceptor[0;39m [2m:[0;39m SQL to parse, SQL: SELECT    ID,GROUP_ID,EDU_LEVEL_ID    FROM  SYT_DICTIONARY_GROUP_EDU_LEVEL         WHERE  (GROUP_ID = ?)
[2m2025-08-04 17:00:47.090[0;39m [32mDEBUG[0;39m [35m99538[0;39m [2m---[0;39m [2m[io-8082-exec-10][0;39m [36m.c.c.m.i.CustomDataPermissionInterceptor[0;39m [2m:[0;39m parse the finished SQL: SELECT ID, GROUP_ID, EDU_LEVEL_ID FROM SYT_DICTIONARY_GROUP_EDU_LEVEL WHERE (GROUP_ID = ?)
[2m2025-08-04 17:00:47.090[0;39m [32mDEBUG[0;39m [35m99538[0;39m [2m---[0;39m [2m[io-8082-exec-10][0;39m [36mc.s.p.d.m.D.selectList                  [0;39m [2m:[0;39m ==>  Preparing: SELECT ID, GROUP_ID, EDU_LEVEL_ID FROM SYT_DICTIONARY_GROUP_EDU_LEVEL WHERE (GROUP_ID = ?)
[2m2025-08-04 17:00:47.090[0;39m [32mDEBUG[0;39m [35m99538[0;39m [2m---[0;39m [2m[io-8082-exec-10][0;39m [36mc.s.p.d.m.D.selectList                  [0;39m [2m:[0;39m ==> Parameters: e0594c19ea31172b0ce7a37d3381d5de(String)
[2m2025-08-04 17:00:47.161[0;39m [32mDEBUG[0;39m [35m99538[0;39m [2m---[0;39m [2m[io-8082-exec-10][0;39m [36mc.s.p.d.m.D.selectList                  [0;39m [2m:[0;39m <==      Total: 0
[2m2025-08-04 17:00:47.163[0;39m [32mDEBUG[0;39m [35m99538[0;39m [2m---[0;39m [2m[io-8082-exec-10][0;39m [36m.c.c.m.i.CustomDataPermissionInterceptor[0;39m [2m:[0;39m original SQL: SELECT    ID,GROUP_ID,ROLE_ID    FROM  SYT_DICTIONARY_GROUP_HIDE_ROLE         WHERE  (GROUP_ID = ?)
[2m2025-08-04 17:00:47.169[0;39m [32mDEBUG[0;39m [35m99538[0;39m [2m---[0;39m [2m[io-8082-exec-10][0;39m [36m.c.c.m.i.CustomDataPermissionInterceptor[0;39m [2m:[0;39m SQL to parse, SQL: SELECT    ID,GROUP_ID,ROLE_ID    FROM  SYT_DICTIONARY_GROUP_HIDE_ROLE         WHERE  (GROUP_ID = ?)
[2m2025-08-04 17:00:47.171[0;39m [32mDEBUG[0;39m [35m99538[0;39m [2m---[0;39m [2m[io-8082-exec-10][0;39m [36m.c.c.m.i.CustomDataPermissionInterceptor[0;39m [2m:[0;39m parse the finished SQL: SELECT ID, GROUP_ID, ROLE_ID FROM SYT_DICTIONARY_GROUP_HIDE_ROLE WHERE (GROUP_ID = ?)
[2m2025-08-04 17:00:47.171[0;39m [32mDEBUG[0;39m [35m99538[0;39m [2m---[0;39m [2m[io-8082-exec-10][0;39m [36mc.s.p.d.m.D.selectList                  [0;39m [2m:[0;39m ==>  Preparing: SELECT ID, GROUP_ID, ROLE_ID FROM SYT_DICTIONARY_GROUP_HIDE_ROLE WHERE (GROUP_ID = ?)
[2m2025-08-04 17:00:47.171[0;39m [32mDEBUG[0;39m [35m99538[0;39m [2m---[0;39m [2m[io-8082-exec-10][0;39m [36mc.s.p.d.m.D.selectList                  [0;39m [2m:[0;39m ==> Parameters: 3d308b159e369a31fa7035c7a38a11be(String)
[2m2025-08-04 17:00:47.241[0;39m [32mDEBUG[0;39m [35m99538[0;39m [2m---[0;39m [2m[io-8082-exec-10][0;39m [36mc.s.p.d.m.D.selectList                  [0;39m [2m:[0;39m <==      Total: 0
[2m2025-08-04 17:00:47.242[0;39m [32mDEBUG[0;39m [35m99538[0;39m [2m---[0;39m [2m[io-8082-exec-10][0;39m [36m.c.c.m.i.CustomDataPermissionInterceptor[0;39m [2m:[0;39m original SQL: SELECT    ID,GROUP_ID,EDU_LEVEL_ID    FROM  SYT_DICTIONARY_GROUP_EDU_LEVEL         WHERE  (GROUP_ID = ?)
[2m2025-08-04 17:00:47.249[0;39m [32mDEBUG[0;39m [35m99538[0;39m [2m---[0;39m [2m[io-8082-exec-10][0;39m [36m.c.c.m.i.CustomDataPermissionInterceptor[0;39m [2m:[0;39m SQL to parse, SQL: SELECT    ID,GROUP_ID,EDU_LEVEL_ID    FROM  SYT_DICTIONARY_GROUP_EDU_LEVEL         WHERE  (GROUP_ID = ?)
[2m2025-08-04 17:00:47.251[0;39m [32mDEBUG[0;39m [35m99538[0;39m [2m---[0;39m [2m[io-8082-exec-10][0;39m [36m.c.c.m.i.CustomDataPermissionInterceptor[0;39m [2m:[0;39m parse the finished SQL: SELECT ID, GROUP_ID, EDU_LEVEL_ID FROM SYT_DICTIONARY_GROUP_EDU_LEVEL WHERE (GROUP_ID = ?)
[2m2025-08-04 17:00:47.251[0;39m [32mDEBUG[0;39m [35m99538[0;39m [2m---[0;39m [2m[io-8082-exec-10][0;39m [36mc.s.p.d.m.D.selectList                  [0;39m [2m:[0;39m ==>  Preparing: SELECT ID, GROUP_ID, EDU_LEVEL_ID FROM SYT_DICTIONARY_GROUP_EDU_LEVEL WHERE (GROUP_ID = ?)
[2m2025-08-04 17:00:47.253[0;39m [32mDEBUG[0;39m [35m99538[0;39m [2m---[0;39m [2m[io-8082-exec-10][0;39m [36mc.s.p.d.m.D.selectList                  [0;39m [2m:[0;39m ==> Parameters: 3d308b159e369a31fa7035c7a38a11be(String)
[2m2025-08-04 17:00:47.326[0;39m [32mDEBUG[0;39m [35m99538[0;39m [2m---[0;39m [2m[io-8082-exec-10][0;39m [36mc.s.p.d.m.D.selectList                  [0;39m [2m:[0;39m <==      Total: 0
[2m2025-08-04 17:00:47.327[0;39m [32mDEBUG[0;39m [35m99538[0;39m [2m---[0;39m [2m[io-8082-exec-10][0;39m [36m.c.c.m.i.CustomDataPermissionInterceptor[0;39m [2m:[0;39m original SQL: SELECT    ID,GROUP_ID,ROLE_ID    FROM  SYT_DICTIONARY_GROUP_HIDE_ROLE         WHERE  (GROUP_ID = ?)
[2m2025-08-04 17:00:47.334[0;39m [32mDEBUG[0;39m [35m99538[0;39m [2m---[0;39m [2m[io-8082-exec-10][0;39m [36m.c.c.m.i.CustomDataPermissionInterceptor[0;39m [2m:[0;39m SQL to parse, SQL: SELECT    ID,GROUP_ID,ROLE_ID    FROM  SYT_DICTIONARY_GROUP_HIDE_ROLE         WHERE  (GROUP_ID = ?)
[2m2025-08-04 17:00:47.336[0;39m [32mDEBUG[0;39m [35m99538[0;39m [2m---[0;39m [2m[io-8082-exec-10][0;39m [36m.c.c.m.i.CustomDataPermissionInterceptor[0;39m [2m:[0;39m parse the finished SQL: SELECT ID, GROUP_ID, ROLE_ID FROM SYT_DICTIONARY_GROUP_HIDE_ROLE WHERE (GROUP_ID = ?)
[2m2025-08-04 17:00:47.336[0;39m [32mDEBUG[0;39m [35m99538[0;39m [2m---[0;39m [2m[io-8082-exec-10][0;39m [36mc.s.p.d.m.D.selectList                  [0;39m [2m:[0;39m ==>  Preparing: SELECT ID, GROUP_ID, ROLE_ID FROM SYT_DICTIONARY_GROUP_HIDE_ROLE WHERE (GROUP_ID = ?)
[2m2025-08-04 17:00:47.336[0;39m [32mDEBUG[0;39m [35m99538[0;39m [2m---[0;39m [2m[io-8082-exec-10][0;39m [36mc.s.p.d.m.D.selectList                  [0;39m [2m:[0;39m ==> Parameters: d8ae5ab2384304bc3cbe2c9cf1512bd3(String)
[2m2025-08-04 17:00:47.404[0;39m [32mDEBUG[0;39m [35m99538[0;39m [2m---[0;39m [2m[io-8082-exec-10][0;39m [36mc.s.p.d.m.D.selectList                  [0;39m [2m:[0;39m <==      Total: 0
[2m2025-08-04 17:00:47.404[0;39m [32mDEBUG[0;39m [35m99538[0;39m [2m---[0;39m [2m[io-8082-exec-10][0;39m [36m.c.c.m.i.CustomDataPermissionInterceptor[0;39m [2m:[0;39m original SQL: SELECT    ID,GROUP_ID,EDU_LEVEL_ID    FROM  SYT_DICTIONARY_GROUP_EDU_LEVEL         WHERE  (GROUP_ID = ?)
[2m2025-08-04 17:00:47.409[0;39m [32mDEBUG[0;39m [35m99538[0;39m [2m---[0;39m [2m[io-8082-exec-10][0;39m [36m.c.c.m.i.CustomDataPermissionInterceptor[0;39m [2m:[0;39m SQL to parse, SQL: SELECT    ID,GROUP_ID,EDU_LEVEL_ID    FROM  SYT_DICTIONARY_GROUP_EDU_LEVEL         WHERE  (GROUP_ID = ?)
[2m2025-08-04 17:00:47.411[0;39m [32mDEBUG[0;39m [35m99538[0;39m [2m---[0;39m [2m[io-8082-exec-10][0;39m [36m.c.c.m.i.CustomDataPermissionInterceptor[0;39m [2m:[0;39m parse the finished SQL: SELECT ID, GROUP_ID, EDU_LEVEL_ID FROM SYT_DICTIONARY_GROUP_EDU_LEVEL WHERE (GROUP_ID = ?)
[2m2025-08-04 17:00:47.411[0;39m [32mDEBUG[0;39m [35m99538[0;39m [2m---[0;39m [2m[io-8082-exec-10][0;39m [36mc.s.p.d.m.D.selectList                  [0;39m [2m:[0;39m ==>  Preparing: SELECT ID, GROUP_ID, EDU_LEVEL_ID FROM SYT_DICTIONARY_GROUP_EDU_LEVEL WHERE (GROUP_ID = ?)
[2m2025-08-04 17:00:47.411[0;39m [32mDEBUG[0;39m [35m99538[0;39m [2m---[0;39m [2m[io-8082-exec-10][0;39m [36mc.s.p.d.m.D.selectList                  [0;39m [2m:[0;39m ==> Parameters: d8ae5ab2384304bc3cbe2c9cf1512bd3(String)
[2m2025-08-04 17:00:47.485[0;39m [32mDEBUG[0;39m [35m99538[0;39m [2m---[0;39m [2m[io-8082-exec-10][0;39m [36mc.s.p.d.m.D.selectList                  [0;39m [2m:[0;39m <==      Total: 0
[2m2025-08-04 17:00:47.486[0;39m [32mDEBUG[0;39m [35m99538[0;39m [2m---[0;39m [2m[io-8082-exec-10][0;39m [36m.c.c.m.i.CustomDataPermissionInterceptor[0;39m [2m:[0;39m original SQL: SELECT    ID,GROUP_ID,ROLE_ID    FROM  SYT_DICTIONARY_GROUP_HIDE_ROLE         WHERE  (GROUP_ID = ?)
[2m2025-08-04 17:00:47.492[0;39m [32mDEBUG[0;39m [35m99538[0;39m [2m---[0;39m [2m[io-8082-exec-10][0;39m [36m.c.c.m.i.CustomDataPermissionInterceptor[0;39m [2m:[0;39m SQL to parse, SQL: SELECT    ID,GROUP_ID,ROLE_ID    FROM  SYT_DICTIONARY_GROUP_HIDE_ROLE         WHERE  (GROUP_ID = ?)
[2m2025-08-04 17:00:47.494[0;39m [32mDEBUG[0;39m [35m99538[0;39m [2m---[0;39m [2m[io-8082-exec-10][0;39m [36m.c.c.m.i.CustomDataPermissionInterceptor[0;39m [2m:[0;39m parse the finished SQL: SELECT ID, GROUP_ID, ROLE_ID FROM SYT_DICTIONARY_GROUP_HIDE_ROLE WHERE (GROUP_ID = ?)
[2m2025-08-04 17:00:47.494[0;39m [32mDEBUG[0;39m [35m99538[0;39m [2m---[0;39m [2m[io-8082-exec-10][0;39m [36mc.s.p.d.m.D.selectList                  [0;39m [2m:[0;39m ==>  Preparing: SELECT ID, GROUP_ID, ROLE_ID FROM SYT_DICTIONARY_GROUP_HIDE_ROLE WHERE (GROUP_ID = ?)
[2m2025-08-04 17:00:47.494[0;39m [32mDEBUG[0;39m [35m99538[0;39m [2m---[0;39m [2m[io-8082-exec-10][0;39m [36mc.s.p.d.m.D.selectList                  [0;39m [2m:[0;39m ==> Parameters: e60c2aa27ff4813382f2d008c17aed97(String)
[2m2025-08-04 17:00:47.567[0;39m [32mDEBUG[0;39m [35m99538[0;39m [2m---[0;39m [2m[io-8082-exec-10][0;39m [36mc.s.p.d.m.D.selectList                  [0;39m [2m:[0;39m <==      Total: 0
[2m2025-08-04 17:00:47.568[0;39m [32mDEBUG[0;39m [35m99538[0;39m [2m---[0;39m [2m[io-8082-exec-10][0;39m [36m.c.c.m.i.CustomDataPermissionInterceptor[0;39m [2m:[0;39m original SQL: SELECT    ID,GROUP_ID,EDU_LEVEL_ID    FROM  SYT_DICTIONARY_GROUP_EDU_LEVEL         WHERE  (GROUP_ID = ?)
[2m2025-08-04 17:00:47.574[0;39m [32mDEBUG[0;39m [35m99538[0;39m [2m---[0;39m [2m[io-8082-exec-10][0;39m [36m.c.c.m.i.CustomDataPermissionInterceptor[0;39m [2m:[0;39m SQL to parse, SQL: SELECT    ID,GROUP_ID,EDU_LEVEL_ID    FROM  SYT_DICTIONARY_GROUP_EDU_LEVEL         WHERE  (GROUP_ID = ?)
[2m2025-08-04 17:00:47.576[0;39m [32mDEBUG[0;39m [35m99538[0;39m [2m---[0;39m [2m[io-8082-exec-10][0;39m [36m.c.c.m.i.CustomDataPermissionInterceptor[0;39m [2m:[0;39m parse the finished SQL: SELECT ID, GROUP_ID, EDU_LEVEL_ID FROM SYT_DICTIONARY_GROUP_EDU_LEVEL WHERE (GROUP_ID = ?)
[2m2025-08-04 17:00:47.577[0;39m [32mDEBUG[0;39m [35m99538[0;39m [2m---[0;39m [2m[io-8082-exec-10][0;39m [36mc.s.p.d.m.D.selectList                  [0;39m [2m:[0;39m ==>  Preparing: SELECT ID, GROUP_ID, EDU_LEVEL_ID FROM SYT_DICTIONARY_GROUP_EDU_LEVEL WHERE (GROUP_ID = ?)
[2m2025-08-04 17:00:47.577[0;39m [32mDEBUG[0;39m [35m99538[0;39m [2m---[0;39m [2m[io-8082-exec-10][0;39m [36mc.s.p.d.m.D.selectList                  [0;39m [2m:[0;39m ==> Parameters: e60c2aa27ff4813382f2d008c17aed97(String)
[2m2025-08-04 17:00:47.656[0;39m [32mDEBUG[0;39m [35m99538[0;39m [2m---[0;39m [2m[io-8082-exec-10][0;39m [36mc.s.p.d.m.D.selectList                  [0;39m [2m:[0;39m <==      Total: 0
[2m2025-08-04 17:01:55.644[0;39m [32m INFO[0;39m [35m99538[0;39m [2m---[0;39m [2m[ionShutdownHook][0;39m [36mo.s.b.w.e.tomcat.GracefulShutdown       [0;39m [2m:[0;39m Commencing graceful shutdown. Waiting for active requests to complete
[2m2025-08-04 17:01:55.664[0;39m [32m INFO[0;39m [35m99538[0;39m [2m---[0;39m [2m[tomcat-shutdown][0;39m [36mo.s.b.w.e.tomcat.GracefulShutdown       [0;39m [2m:[0;39m Graceful shutdown complete
[2m2025-08-04 17:01:55.851[0;39m [32m INFO[0;39m [35m99538[0;39m [2m---[0;39m [2m[ionShutdownHook][0;39m [36mj.LocalContainerEntityManagerFactoryBean[0;39m [2m:[0;39m Closing JPA EntityManagerFactory for persistence unit 'default'
[2m2025-08-04 17:01:55.938[0;39m [32m INFO[0;39m [35m99538[0;39m [2m---[0;39m [2m[ionShutdownHook][0;39m [36mcom.alibaba.druid.pool.DruidDataSource  [0;39m [2m:[0;39m {dataSource-1} closing ...
[2m2025-08-04 17:02:42.174[0;39m [32m INFO[0;39m [35m1312[0;39m [2m---[0;39m [2m[kground-preinit][0;39m [36mo.h.validator.internal.util.Version     [0;39m [2m:[0;39m HV000001: Hibernate Validator 8.0.2.Final
[2m2025-08-04 17:02:42.431[0;39m [32m INFO[0;39m [35m1312[0;39m [2m---[0;39m [2m[           main][0;39m [36mcom.sanythadmin.AdminApplication        [0;39m [2m:[0;39m Starting AdminApplication using Java 17.0.15 with PID 1312 (/Users/<USER>/WorkSpace/sanyth-xuegong-5/sanyth-xuegong-5-server/xggl-api/target/classes started by paynexc in /Users/<USER>/WorkSpace/sanyth-xuegong-5)
[2m2025-08-04 17:02:42.432[0;39m [32mDEBUG[0;39m [35m1312[0;39m [2m---[0;39m [2m[           main][0;39m [36mcom.sanythadmin.AdminApplication        [0;39m [2m:[0;39m Running with Spring Boot v3.5.3, Spring v6.2.8
[2m2025-08-04 17:02:42.433[0;39m [32m INFO[0;39m [35m1312[0;39m [2m---[0;39m [2m[           main][0;39m [36mcom.sanythadmin.AdminApplication        [0;39m [2m:[0;39m The following 1 profile is active: "dev"
[2m2025-08-04 17:02:44.255[0;39m [32m INFO[0;39m [35m1312[0;39m [2m---[0;39m [2m[           main][0;39m [36m.s.d.r.c.RepositoryConfigurationDelegate[0;39m [2m:[0;39m Multiple Spring Data modules found, entering strict repository configuration mode
[2m2025-08-04 17:02:44.258[0;39m [32m INFO[0;39m [35m1312[0;39m [2m---[0;39m [2m[           main][0;39m [36m.s.d.r.c.RepositoryConfigurationDelegate[0;39m [2m:[0;39m Bootstrapping Spring Data JPA repositories in DEFAULT mode.
[2m2025-08-04 17:02:44.694[0;39m [32m INFO[0;39m [35m1312[0;39m [2m---[0;39m [2m[           main][0;39m [36m.RepositoryConfigurationExtensionSupport[0;39m [2m:[0;39m Spring Data JPA - Could not safely identify store assignment for repository candidate interface com.sanythadmin.project.export.repository.ExportTaskRepository; If you want this repository to be a JPA repository, consider annotating your entities with one of these annotations: jakarta.persistence.Entity, jakarta.persistence.MappedSuperclass (preferred), or consider extending one of the following types with your repository: org.springframework.data.jpa.repository.JpaRepository
[2m2025-08-04 17:02:44.823[0;39m [32m INFO[0;39m [35m1312[0;39m [2m---[0;39m [2m[           main][0;39m [36m.s.d.r.c.RepositoryConfigurationDelegate[0;39m [2m:[0;39m Finished Spring Data repository scanning in 549 ms. Found 23 JPA repository interfaces.
[2m2025-08-04 17:02:44.869[0;39m [32m INFO[0;39m [35m1312[0;39m [2m---[0;39m [2m[           main][0;39m [36m.s.d.r.c.RepositoryConfigurationDelegate[0;39m [2m:[0;39m Multiple Spring Data modules found, entering strict repository configuration mode
[2m2025-08-04 17:02:44.870[0;39m [32m INFO[0;39m [35m1312[0;39m [2m---[0;39m [2m[           main][0;39m [36m.s.d.r.c.RepositoryConfigurationDelegate[0;39m [2m:[0;39m Bootstrapping Spring Data MongoDB repositories in DEFAULT mode.
[2m2025-08-04 17:02:44.907[0;39m [32m INFO[0;39m [35m1312[0;39m [2m---[0;39m [2m[           main][0;39m [36m.RepositoryConfigurationExtensionSupport[0;39m [2m:[0;39m Spring Data MongoDB - Could not safely identify store assignment for repository candidate interface com.sanythadmin.common.integration.authcenter.repository.AuthCenterParamRepository; If you want this repository to be a MongoDB repository, consider annotating your entities with one of these annotations: org.springframework.data.mongodb.core.mapping.Document (preferred), or consider extending one of the following types with your repository: org.springframework.data.mongodb.repository.MongoRepository
[2m2025-08-04 17:02:44.907[0;39m [32m INFO[0;39m [35m1312[0;39m [2m---[0;39m [2m[           main][0;39m [36m.RepositoryConfigurationExtensionSupport[0;39m [2m:[0;39m Spring Data MongoDB - Could not safely identify store assignment for repository candidate interface com.sanythadmin.platform.dingtalk.repository.DingtalkAppRepository; If you want this repository to be a MongoDB repository, consider annotating your entities with one of these annotations: org.springframework.data.mongodb.core.mapping.Document (preferred), or consider extending one of the following types with your repository: org.springframework.data.mongodb.repository.MongoRepository
[2m2025-08-04 17:02:44.907[0;39m [32m INFO[0;39m [35m1312[0;39m [2m---[0;39m [2m[           main][0;39m [36m.RepositoryConfigurationExtensionSupport[0;39m [2m:[0;39m Spring Data MongoDB - Could not safely identify store assignment for repository candidate interface com.sanythadmin.platform.dingtalk.repository.DingtalkParamRepository; If you want this repository to be a MongoDB repository, consider annotating your entities with one of these annotations: org.springframework.data.mongodb.core.mapping.Document (preferred), or consider extending one of the following types with your repository: org.springframework.data.mongodb.repository.MongoRepository
[2m2025-08-04 17:02:44.907[0;39m [32m INFO[0;39m [35m1312[0;39m [2m---[0;39m [2m[           main][0;39m [36m.RepositoryConfigurationExtensionSupport[0;39m [2m:[0;39m Spring Data MongoDB - Could not safely identify store assignment for repository candidate interface com.sanythadmin.platform.dingtalk.repository.DingtalkSysAccountRepository; If you want this repository to be a MongoDB repository, consider annotating your entities with one of these annotations: org.springframework.data.mongodb.core.mapping.Document (preferred), or consider extending one of the following types with your repository: org.springframework.data.mongodb.repository.MongoRepository
[2m2025-08-04 17:02:44.908[0;39m [32m INFO[0;39m [35m1312[0;39m [2m---[0;39m [2m[           main][0;39m [36m.RepositoryConfigurationExtensionSupport[0;39m [2m:[0;39m Spring Data MongoDB - Could not safely identify store assignment for repository candidate interface com.sanythadmin.platform.dingtalk.repository.DingtalkSysAccountRoleRepository; If you want this repository to be a MongoDB repository, consider annotating your entities with one of these annotations: org.springframework.data.mongodb.core.mapping.Document (preferred), or consider extending one of the following types with your repository: org.springframework.data.mongodb.repository.MongoRepository
[2m2025-08-04 17:02:44.908[0;39m [32m INFO[0;39m [35m1312[0;39m [2m---[0;39m [2m[           main][0;39m [36m.RepositoryConfigurationExtensionSupport[0;39m [2m:[0;39m Spring Data MongoDB - Could not safely identify store assignment for repository candidate interface com.sanythadmin.platform.dingtalk.repository.DingtalkUserRepository; If you want this repository to be a MongoDB repository, consider annotating your entities with one of these annotations: org.springframework.data.mongodb.core.mapping.Document (preferred), or consider extending one of the following types with your repository: org.springframework.data.mongodb.repository.MongoRepository
[2m2025-08-04 17:02:44.910[0;39m [32m INFO[0;39m [35m1312[0;39m [2m---[0;39m [2m[           main][0;39m [36m.RepositoryConfigurationExtensionSupport[0;39m [2m:[0;39m Spring Data MongoDB - Could not safely identify store assignment for repository candidate interface com.sanythadmin.project.leave.repository.LeaveApplicationFieldFileRepository; If you want this repository to be a MongoDB repository, consider annotating your entities with one of these annotations: org.springframework.data.mongodb.core.mapping.Document (preferred), or consider extending one of the following types with your repository: org.springframework.data.mongodb.repository.MongoRepository
[2m2025-08-04 17:02:44.911[0;39m [32m INFO[0;39m [35m1312[0;39m [2m---[0;39m [2m[           main][0;39m [36m.RepositoryConfigurationExtensionSupport[0;39m [2m:[0;39m Spring Data MongoDB - Could not safely identify store assignment for repository candidate interface com.sanythadmin.project.leave.repository.LeaveApplicationProcessNodeAssigneeRepository; If you want this repository to be a MongoDB repository, consider annotating your entities with one of these annotations: org.springframework.data.mongodb.core.mapping.Document (preferred), or consider extending one of the following types with your repository: org.springframework.data.mongodb.repository.MongoRepository
[2m2025-08-04 17:02:44.911[0;39m [32m INFO[0;39m [35m1312[0;39m [2m---[0;39m [2m[           main][0;39m [36m.RepositoryConfigurationExtensionSupport[0;39m [2m:[0;39m Spring Data MongoDB - Could not safely identify store assignment for repository candidate interface com.sanythadmin.project.leave.repository.LeaveApplicationProcessNodeRepository; If you want this repository to be a MongoDB repository, consider annotating your entities with one of these annotations: org.springframework.data.mongodb.core.mapping.Document (preferred), or consider extending one of the following types with your repository: org.springframework.data.mongodb.repository.MongoRepository
[2m2025-08-04 17:02:44.911[0;39m [32m INFO[0;39m [35m1312[0;39m [2m---[0;39m [2m[           main][0;39m [36m.RepositoryConfigurationExtensionSupport[0;39m [2m:[0;39m Spring Data MongoDB - Could not safely identify store assignment for repository candidate interface com.sanythadmin.project.leave.repository.LeaveApplicationProcessRecordRepository; If you want this repository to be a MongoDB repository, consider annotating your entities with one of these annotations: org.springframework.data.mongodb.core.mapping.Document (preferred), or consider extending one of the following types with your repository: org.springframework.data.mongodb.repository.MongoRepository
[2m2025-08-04 17:02:44.911[0;39m [32m INFO[0;39m [35m1312[0;39m [2m---[0;39m [2m[           main][0;39m [36m.RepositoryConfigurationExtensionSupport[0;39m [2m:[0;39m Spring Data MongoDB - Could not safely identify store assignment for repository candidate interface com.sanythadmin.project.leave.repository.LeaveApplicationRepository; If you want this repository to be a MongoDB repository, consider annotating your entities with one of these annotations: org.springframework.data.mongodb.core.mapping.Document (preferred), or consider extending one of the following types with your repository: org.springframework.data.mongodb.repository.MongoRepository
[2m2025-08-04 17:02:44.911[0;39m [32m INFO[0;39m [35m1312[0;39m [2m---[0;39m [2m[           main][0;39m [36m.RepositoryConfigurationExtensionSupport[0;39m [2m:[0;39m Spring Data MongoDB - Could not safely identify store assignment for repository candidate interface com.sanythadmin.project.leave.repository.LeaveBasicFieldRepository; If you want this repository to be a MongoDB repository, consider annotating your entities with one of these annotations: org.springframework.data.mongodb.core.mapping.Document (preferred), or consider extending one of the following types with your repository: org.springframework.data.mongodb.repository.MongoRepository
[2m2025-08-04 17:02:44.911[0;39m [32m INFO[0;39m [35m1312[0;39m [2m---[0;39m [2m[           main][0;39m [36m.RepositoryConfigurationExtensionSupport[0;39m [2m:[0;39m Spring Data MongoDB - Could not safely identify store assignment for repository candidate interface com.sanythadmin.project.leave.repository.LeaveProjectApprovalRuleNodeRepository; If you want this repository to be a MongoDB repository, consider annotating your entities with one of these annotations: org.springframework.data.mongodb.core.mapping.Document (preferred), or consider extending one of the following types with your repository: org.springframework.data.mongodb.repository.MongoRepository
[2m2025-08-04 17:02:44.911[0;39m [32m INFO[0;39m [35m1312[0;39m [2m---[0;39m [2m[           main][0;39m [36m.RepositoryConfigurationExtensionSupport[0;39m [2m:[0;39m Spring Data MongoDB - Could not safely identify store assignment for repository candidate interface com.sanythadmin.project.leave.repository.LeaveProjectAreaRepository; If you want this repository to be a MongoDB repository, consider annotating your entities with one of these annotations: org.springframework.data.mongodb.core.mapping.Document (preferred), or consider extending one of the following types with your repository: org.springframework.data.mongodb.repository.MongoRepository
[2m2025-08-04 17:02:44.911[0;39m [32m INFO[0;39m [35m1312[0;39m [2m---[0;39m [2m[           main][0;39m [36m.RepositoryConfigurationExtensionSupport[0;39m [2m:[0;39m Spring Data MongoDB - Could not safely identify store assignment for repository candidate interface com.sanythadmin.project.leave.repository.LeaveProjectFieldRepository; If you want this repository to be a MongoDB repository, consider annotating your entities with one of these annotations: org.springframework.data.mongodb.core.mapping.Document (preferred), or consider extending one of the following types with your repository: org.springframework.data.mongodb.repository.MongoRepository
[2m2025-08-04 17:02:44.912[0;39m [32m INFO[0;39m [35m1312[0;39m [2m---[0;39m [2m[           main][0;39m [36m.RepositoryConfigurationExtensionSupport[0;39m [2m:[0;39m Spring Data MongoDB - Could not safely identify store assignment for repository candidate interface com.sanythadmin.project.leave.repository.LeaveProjectRepository; If you want this repository to be a MongoDB repository, consider annotating your entities with one of these annotations: org.springframework.data.mongodb.core.mapping.Document (preferred), or consider extending one of the following types with your repository: org.springframework.data.mongodb.repository.MongoRepository
[2m2025-08-04 17:02:44.912[0;39m [32m INFO[0;39m [35m1312[0;39m [2m---[0;39m [2m[           main][0;39m [36m.RepositoryConfigurationExtensionSupport[0;39m [2m:[0;39m Spring Data MongoDB - Could not safely identify store assignment for repository candidate interface com.sanythadmin.project.mobile.repository.MobileAppRepository; If you want this repository to be a MongoDB repository, consider annotating your entities with one of these annotations: org.springframework.data.mongodb.core.mapping.Document (preferred), or consider extending one of the following types with your repository: org.springframework.data.mongodb.repository.MongoRepository
[2m2025-08-04 17:02:44.912[0;39m [32m INFO[0;39m [35m1312[0;39m [2m---[0;39m [2m[           main][0;39m [36m.RepositoryConfigurationExtensionSupport[0;39m [2m:[0;39m Spring Data MongoDB - Could not safely identify store assignment for repository candidate interface com.sanythadmin.project.mobile.repository.MobileAppRoleRepository; If you want this repository to be a MongoDB repository, consider annotating your entities with one of these annotations: org.springframework.data.mongodb.core.mapping.Document (preferred), or consider extending one of the following types with your repository: org.springframework.data.mongodb.repository.MongoRepository
[2m2025-08-04 17:02:44.912[0;39m [32m INFO[0;39m [35m1312[0;39m [2m---[0;39m [2m[           main][0;39m [36m.RepositoryConfigurationExtensionSupport[0;39m [2m:[0;39m Spring Data MongoDB - Could not safely identify store assignment for repository candidate interface com.sanythadmin.project.mobile.repository.MobileGroupRepository; If you want this repository to be a MongoDB repository, consider annotating your entities with one of these annotations: org.springframework.data.mongodb.core.mapping.Document (preferred), or consider extending one of the following types with your repository: org.springframework.data.mongodb.repository.MongoRepository
[2m2025-08-04 17:02:44.912[0;39m [32m INFO[0;39m [35m1312[0;39m [2m---[0;39m [2m[           main][0;39m [36m.RepositoryConfigurationExtensionSupport[0;39m [2m:[0;39m Spring Data MongoDB - Could not safely identify store assignment for repository candidate interface com.sanythadmin.project.mobile.repository.MobileGroupRoleRepository; If you want this repository to be a MongoDB repository, consider annotating your entities with one of these annotations: org.springframework.data.mongodb.core.mapping.Document (preferred), or consider extending one of the following types with your repository: org.springframework.data.mongodb.repository.MongoRepository
[2m2025-08-04 17:02:44.912[0;39m [32m INFO[0;39m [35m1312[0;39m [2m---[0;39m [2m[           main][0;39m [36m.RepositoryConfigurationExtensionSupport[0;39m [2m:[0;39m Spring Data MongoDB - Could not safely identify store assignment for repository candidate interface com.sanythadmin.project.mobile.repository.MobileHomeComponentItemRepository; If you want this repository to be a MongoDB repository, consider annotating your entities with one of these annotations: org.springframework.data.mongodb.core.mapping.Document (preferred), or consider extending one of the following types with your repository: org.springframework.data.mongodb.repository.MongoRepository
[2m2025-08-04 17:02:44.912[0;39m [32m INFO[0;39m [35m1312[0;39m [2m---[0;39m [2m[           main][0;39m [36m.RepositoryConfigurationExtensionSupport[0;39m [2m:[0;39m Spring Data MongoDB - Could not safely identify store assignment for repository candidate interface com.sanythadmin.project.mobile.repository.MobileHomeComponentRepository; If you want this repository to be a MongoDB repository, consider annotating your entities with one of these annotations: org.springframework.data.mongodb.core.mapping.Document (preferred), or consider extending one of the following types with your repository: org.springframework.data.mongodb.repository.MongoRepository
[2m2025-08-04 17:02:44.912[0;39m [32m INFO[0;39m [35m1312[0;39m [2m---[0;39m [2m[           main][0;39m [36m.RepositoryConfigurationExtensionSupport[0;39m [2m:[0;39m Spring Data MongoDB - Could not safely identify store assignment for repository candidate interface com.sanythadmin.project.mobile.repository.MobileSysRoleRepository; If you want this repository to be a MongoDB repository, consider annotating your entities with one of these annotations: org.springframework.data.mongodb.core.mapping.Document (preferred), or consider extending one of the following types with your repository: org.springframework.data.mongodb.repository.MongoRepository
[2m2025-08-04 17:02:44.940[0;39m [32m INFO[0;39m [35m1312[0;39m [2m---[0;39m [2m[           main][0;39m [36m.s.d.r.c.RepositoryConfigurationDelegate[0;39m [2m:[0;39m Finished Spring Data repository scanning in 68 ms. Found 1 MongoDB repository interface.
[2m2025-08-04 17:02:44.954[0;39m [32m INFO[0;39m [35m1312[0;39m [2m---[0;39m [2m[           main][0;39m [36m.s.d.r.c.RepositoryConfigurationDelegate[0;39m [2m:[0;39m Multiple Spring Data modules found, entering strict repository configuration mode
[2m2025-08-04 17:02:44.955[0;39m [32m INFO[0;39m [35m1312[0;39m [2m---[0;39m [2m[           main][0;39m [36m.s.d.r.c.RepositoryConfigurationDelegate[0;39m [2m:[0;39m Bootstrapping Spring Data Redis repositories in DEFAULT mode.
[2m2025-08-04 17:02:44.995[0;39m [32m INFO[0;39m [35m1312[0;39m [2m---[0;39m [2m[           main][0;39m [36m.RepositoryConfigurationExtensionSupport[0;39m [2m:[0;39m Spring Data Redis - Could not safely identify store assignment for repository candidate interface com.sanythadmin.common.integration.authcenter.repository.AuthCenterParamRepository; If you want this repository to be a Redis repository, consider annotating your entities with one of these annotations: org.springframework.data.redis.core.RedisHash (preferred), or consider extending one of the following types with your repository: org.springframework.data.keyvalue.repository.KeyValueRepository
[2m2025-08-04 17:02:44.996[0;39m [32m INFO[0;39m [35m1312[0;39m [2m---[0;39m [2m[           main][0;39m [36m.RepositoryConfigurationExtensionSupport[0;39m [2m:[0;39m Spring Data Redis - Could not safely identify store assignment for repository candidate interface com.sanythadmin.platform.dingtalk.repository.DingtalkAppRepository; If you want this repository to be a Redis repository, consider annotating your entities with one of these annotations: org.springframework.data.redis.core.RedisHash (preferred), or consider extending one of the following types with your repository: org.springframework.data.keyvalue.repository.KeyValueRepository
[2m2025-08-04 17:02:44.996[0;39m [32m INFO[0;39m [35m1312[0;39m [2m---[0;39m [2m[           main][0;39m [36m.RepositoryConfigurationExtensionSupport[0;39m [2m:[0;39m Spring Data Redis - Could not safely identify store assignment for repository candidate interface com.sanythadmin.platform.dingtalk.repository.DingtalkParamRepository; If you want this repository to be a Redis repository, consider annotating your entities with one of these annotations: org.springframework.data.redis.core.RedisHash (preferred), or consider extending one of the following types with your repository: org.springframework.data.keyvalue.repository.KeyValueRepository
[2m2025-08-04 17:02:44.996[0;39m [32m INFO[0;39m [35m1312[0;39m [2m---[0;39m [2m[           main][0;39m [36m.RepositoryConfigurationExtensionSupport[0;39m [2m:[0;39m Spring Data Redis - Could not safely identify store assignment for repository candidate interface com.sanythadmin.platform.dingtalk.repository.DingtalkSysAccountRepository; If you want this repository to be a Redis repository, consider annotating your entities with one of these annotations: org.springframework.data.redis.core.RedisHash (preferred), or consider extending one of the following types with your repository: org.springframework.data.keyvalue.repository.KeyValueRepository
[2m2025-08-04 17:02:44.996[0;39m [32m INFO[0;39m [35m1312[0;39m [2m---[0;39m [2m[           main][0;39m [36m.RepositoryConfigurationExtensionSupport[0;39m [2m:[0;39m Spring Data Redis - Could not safely identify store assignment for repository candidate interface com.sanythadmin.platform.dingtalk.repository.DingtalkSysAccountRoleRepository; If you want this repository to be a Redis repository, consider annotating your entities with one of these annotations: org.springframework.data.redis.core.RedisHash (preferred), or consider extending one of the following types with your repository: org.springframework.data.keyvalue.repository.KeyValueRepository
[2m2025-08-04 17:02:44.996[0;39m [32m INFO[0;39m [35m1312[0;39m [2m---[0;39m [2m[           main][0;39m [36m.RepositoryConfigurationExtensionSupport[0;39m [2m:[0;39m Spring Data Redis - Could not safely identify store assignment for repository candidate interface com.sanythadmin.platform.dingtalk.repository.DingtalkUserRepository; If you want this repository to be a Redis repository, consider annotating your entities with one of these annotations: org.springframework.data.redis.core.RedisHash (preferred), or consider extending one of the following types with your repository: org.springframework.data.keyvalue.repository.KeyValueRepository
[2m2025-08-04 17:02:44.996[0;39m [32m INFO[0;39m [35m1312[0;39m [2m---[0;39m [2m[           main][0;39m [36m.RepositoryConfigurationExtensionSupport[0;39m [2m:[0;39m Spring Data Redis - Could not safely identify store assignment for repository candidate interface com.sanythadmin.project.export.repository.ExportTaskRepository; If you want this repository to be a Redis repository, consider annotating your entities with one of these annotations: org.springframework.data.redis.core.RedisHash (preferred), or consider extending one of the following types with your repository: org.springframework.data.keyvalue.repository.KeyValueRepository
[2m2025-08-04 17:02:44.996[0;39m [32m INFO[0;39m [35m1312[0;39m [2m---[0;39m [2m[           main][0;39m [36m.RepositoryConfigurationExtensionSupport[0;39m [2m:[0;39m Spring Data Redis - Could not safely identify store assignment for repository candidate interface com.sanythadmin.project.leave.repository.LeaveApplicationFieldFileRepository; If you want this repository to be a Redis repository, consider annotating your entities with one of these annotations: org.springframework.data.redis.core.RedisHash (preferred), or consider extending one of the following types with your repository: org.springframework.data.keyvalue.repository.KeyValueRepository
[2m2025-08-04 17:02:44.996[0;39m [32m INFO[0;39m [35m1312[0;39m [2m---[0;39m [2m[           main][0;39m [36m.RepositoryConfigurationExtensionSupport[0;39m [2m:[0;39m Spring Data Redis - Could not safely identify store assignment for repository candidate interface com.sanythadmin.project.leave.repository.LeaveApplicationProcessNodeAssigneeRepository; If you want this repository to be a Redis repository, consider annotating your entities with one of these annotations: org.springframework.data.redis.core.RedisHash (preferred), or consider extending one of the following types with your repository: org.springframework.data.keyvalue.repository.KeyValueRepository
[2m2025-08-04 17:02:44.996[0;39m [32m INFO[0;39m [35m1312[0;39m [2m---[0;39m [2m[           main][0;39m [36m.RepositoryConfigurationExtensionSupport[0;39m [2m:[0;39m Spring Data Redis - Could not safely identify store assignment for repository candidate interface com.sanythadmin.project.leave.repository.LeaveApplicationProcessNodeRepository; If you want this repository to be a Redis repository, consider annotating your entities with one of these annotations: org.springframework.data.redis.core.RedisHash (preferred), or consider extending one of the following types with your repository: org.springframework.data.keyvalue.repository.KeyValueRepository
[2m2025-08-04 17:02:44.996[0;39m [32m INFO[0;39m [35m1312[0;39m [2m---[0;39m [2m[           main][0;39m [36m.RepositoryConfigurationExtensionSupport[0;39m [2m:[0;39m Spring Data Redis - Could not safely identify store assignment for repository candidate interface com.sanythadmin.project.leave.repository.LeaveApplicationProcessRecordRepository; If you want this repository to be a Redis repository, consider annotating your entities with one of these annotations: org.springframework.data.redis.core.RedisHash (preferred), or consider extending one of the following types with your repository: org.springframework.data.keyvalue.repository.KeyValueRepository
[2m2025-08-04 17:02:44.996[0;39m [32m INFO[0;39m [35m1312[0;39m [2m---[0;39m [2m[           main][0;39m [36m.RepositoryConfigurationExtensionSupport[0;39m [2m:[0;39m Spring Data Redis - Could not safely identify store assignment for repository candidate interface com.sanythadmin.project.leave.repository.LeaveApplicationRepository; If you want this repository to be a Redis repository, consider annotating your entities with one of these annotations: org.springframework.data.redis.core.RedisHash (preferred), or consider extending one of the following types with your repository: org.springframework.data.keyvalue.repository.KeyValueRepository
[2m2025-08-04 17:02:44.996[0;39m [32m INFO[0;39m [35m1312[0;39m [2m---[0;39m [2m[           main][0;39m [36m.RepositoryConfigurationExtensionSupport[0;39m [2m:[0;39m Spring Data Redis - Could not safely identify store assignment for repository candidate interface com.sanythadmin.project.leave.repository.LeaveBasicFieldRepository; If you want this repository to be a Redis repository, consider annotating your entities with one of these annotations: org.springframework.data.redis.core.RedisHash (preferred), or consider extending one of the following types with your repository: org.springframework.data.keyvalue.repository.KeyValueRepository
[2m2025-08-04 17:02:44.997[0;39m [32m INFO[0;39m [35m1312[0;39m [2m---[0;39m [2m[           main][0;39m [36m.RepositoryConfigurationExtensionSupport[0;39m [2m:[0;39m Spring Data Redis - Could not safely identify store assignment for repository candidate interface com.sanythadmin.project.leave.repository.LeaveProjectApprovalRuleNodeRepository; If you want this repository to be a Redis repository, consider annotating your entities with one of these annotations: org.springframework.data.redis.core.RedisHash (preferred), or consider extending one of the following types with your repository: org.springframework.data.keyvalue.repository.KeyValueRepository
[2m2025-08-04 17:02:44.997[0;39m [32m INFO[0;39m [35m1312[0;39m [2m---[0;39m [2m[           main][0;39m [36m.RepositoryConfigurationExtensionSupport[0;39m [2m:[0;39m Spring Data Redis - Could not safely identify store assignment for repository candidate interface com.sanythadmin.project.leave.repository.LeaveProjectAreaRepository; If you want this repository to be a Redis repository, consider annotating your entities with one of these annotations: org.springframework.data.redis.core.RedisHash (preferred), or consider extending one of the following types with your repository: org.springframework.data.keyvalue.repository.KeyValueRepository
[2m2025-08-04 17:02:44.997[0;39m [32m INFO[0;39m [35m1312[0;39m [2m---[0;39m [2m[           main][0;39m [36m.RepositoryConfigurationExtensionSupport[0;39m [2m:[0;39m Spring Data Redis - Could not safely identify store assignment for repository candidate interface com.sanythadmin.project.leave.repository.LeaveProjectFieldRepository; If you want this repository to be a Redis repository, consider annotating your entities with one of these annotations: org.springframework.data.redis.core.RedisHash (preferred), or consider extending one of the following types with your repository: org.springframework.data.keyvalue.repository.KeyValueRepository
[2m2025-08-04 17:02:44.997[0;39m [32m INFO[0;39m [35m1312[0;39m [2m---[0;39m [2m[           main][0;39m [36m.RepositoryConfigurationExtensionSupport[0;39m [2m:[0;39m Spring Data Redis - Could not safely identify store assignment for repository candidate interface com.sanythadmin.project.leave.repository.LeaveProjectRepository; If you want this repository to be a Redis repository, consider annotating your entities with one of these annotations: org.springframework.data.redis.core.RedisHash (preferred), or consider extending one of the following types with your repository: org.springframework.data.keyvalue.repository.KeyValueRepository
[2m2025-08-04 17:02:44.997[0;39m [32m INFO[0;39m [35m1312[0;39m [2m---[0;39m [2m[           main][0;39m [36m.RepositoryConfigurationExtensionSupport[0;39m [2m:[0;39m Spring Data Redis - Could not safely identify store assignment for repository candidate interface com.sanythadmin.project.mobile.repository.MobileAppRepository; If you want this repository to be a Redis repository, consider annotating your entities with one of these annotations: org.springframework.data.redis.core.RedisHash (preferred), or consider extending one of the following types with your repository: org.springframework.data.keyvalue.repository.KeyValueRepository
[2m2025-08-04 17:02:44.997[0;39m [32m INFO[0;39m [35m1312[0;39m [2m---[0;39m [2m[           main][0;39m [36m.RepositoryConfigurationExtensionSupport[0;39m [2m:[0;39m Spring Data Redis - Could not safely identify store assignment for repository candidate interface com.sanythadmin.project.mobile.repository.MobileAppRoleRepository; If you want this repository to be a Redis repository, consider annotating your entities with one of these annotations: org.springframework.data.redis.core.RedisHash (preferred), or consider extending one of the following types with your repository: org.springframework.data.keyvalue.repository.KeyValueRepository
[2m2025-08-04 17:02:44.997[0;39m [32m INFO[0;39m [35m1312[0;39m [2m---[0;39m [2m[           main][0;39m [36m.RepositoryConfigurationExtensionSupport[0;39m [2m:[0;39m Spring Data Redis - Could not safely identify store assignment for repository candidate interface com.sanythadmin.project.mobile.repository.MobileGroupRepository; If you want this repository to be a Redis repository, consider annotating your entities with one of these annotations: org.springframework.data.redis.core.RedisHash (preferred), or consider extending one of the following types with your repository: org.springframework.data.keyvalue.repository.KeyValueRepository
[2m2025-08-04 17:02:44.997[0;39m [32m INFO[0;39m [35m1312[0;39m [2m---[0;39m [2m[           main][0;39m [36m.RepositoryConfigurationExtensionSupport[0;39m [2m:[0;39m Spring Data Redis - Could not safely identify store assignment for repository candidate interface com.sanythadmin.project.mobile.repository.MobileGroupRoleRepository; If you want this repository to be a Redis repository, consider annotating your entities with one of these annotations: org.springframework.data.redis.core.RedisHash (preferred), or consider extending one of the following types with your repository: org.springframework.data.keyvalue.repository.KeyValueRepository
[2m2025-08-04 17:02:44.997[0;39m [32m INFO[0;39m [35m1312[0;39m [2m---[0;39m [2m[           main][0;39m [36m.RepositoryConfigurationExtensionSupport[0;39m [2m:[0;39m Spring Data Redis - Could not safely identify store assignment for repository candidate interface com.sanythadmin.project.mobile.repository.MobileHomeComponentItemRepository; If you want this repository to be a Redis repository, consider annotating your entities with one of these annotations: org.springframework.data.redis.core.RedisHash (preferred), or consider extending one of the following types with your repository: org.springframework.data.keyvalue.repository.KeyValueRepository
[2m2025-08-04 17:02:44.997[0;39m [32m INFO[0;39m [35m1312[0;39m [2m---[0;39m [2m[           main][0;39m [36m.RepositoryConfigurationExtensionSupport[0;39m [2m:[0;39m Spring Data Redis - Could not safely identify store assignment for repository candidate interface com.sanythadmin.project.mobile.repository.MobileHomeComponentRepository; If you want this repository to be a Redis repository, consider annotating your entities with one of these annotations: org.springframework.data.redis.core.RedisHash (preferred), or consider extending one of the following types with your repository: org.springframework.data.keyvalue.repository.KeyValueRepository
[2m2025-08-04 17:02:44.997[0;39m [32m INFO[0;39m [35m1312[0;39m [2m---[0;39m [2m[           main][0;39m [36m.RepositoryConfigurationExtensionSupport[0;39m [2m:[0;39m Spring Data Redis - Could not safely identify store assignment for repository candidate interface com.sanythadmin.project.mobile.repository.MobileSysRoleRepository; If you want this repository to be a Redis repository, consider annotating your entities with one of these annotations: org.springframework.data.redis.core.RedisHash (preferred), or consider extending one of the following types with your repository: org.springframework.data.keyvalue.repository.KeyValueRepository
[2m2025-08-04 17:02:44.997[0;39m [32m INFO[0;39m [35m1312[0;39m [2m---[0;39m [2m[           main][0;39m [36m.s.d.r.c.RepositoryConfigurationDelegate[0;39m [2m:[0;39m Finished Spring Data repository scanning in 35 ms. Found 0 Redis repository interfaces.
[2m2025-08-04 17:02:45.413[0;39m [32m INFO[0;39m [35m1312[0;39m [2m---[0;39m [2m[           main][0;39m [36mc.g.y.a.MybatisPlusJoinAutoConfiguration[0;39m [2m:[0;39m MPJ SqlSessionFactory bean definition: sqlSessionFactory factoryBeanName: com.baomidou.mybatisplus.autoconfigure.MybatisPlusAutoConfiguration factoryMethodName: sqlSessionFactory source: com.baomidou.mybatisplus.autoconfigure.MybatisPlusAutoConfiguration.sqlSessionFactory(javax.sql.DataSource)
[2m2025-08-04 17:02:46.009[0;39m [32m INFO[0;39m [35m1312[0;39m [2m---[0;39m [2m[           main][0;39m [36mo.s.b.w.embedded.tomcat.TomcatWebServer [0;39m [2m:[0;39m Tomcat initialized with port 8082 (http)
[2m2025-08-04 17:02:46.034[0;39m [32m INFO[0;39m [35m1312[0;39m [2m---[0;39m [2m[           main][0;39m [36mo.a.coyote.http11.Http11NioProtocol     [0;39m [2m:[0;39m Initializing ProtocolHandler ["http-nio-8082"]
[2m2025-08-04 17:02:46.036[0;39m [32m INFO[0;39m [35m1312[0;39m [2m---[0;39m [2m[           main][0;39m [36mo.apache.catalina.core.StandardService  [0;39m [2m:[0;39m Starting service [Tomcat]
[2m2025-08-04 17:02:46.037[0;39m [32m INFO[0;39m [35m1312[0;39m [2m---[0;39m [2m[           main][0;39m [36mo.apache.catalina.core.StandardEngine   [0;39m [2m:[0;39m Starting Servlet engine: [Apache Tomcat/10.1.42]
[2m2025-08-04 17:02:46.103[0;39m [32m INFO[0;39m [35m1312[0;39m [2m---[0;39m [2m[           main][0;39m [36mo.a.c.c.C.[Tomcat].[localhost].[/]      [0;39m [2m:[0;39m Initializing Spring embedded WebApplicationContext
[2m2025-08-04 17:02:46.103[0;39m [32m INFO[0;39m [35m1312[0;39m [2m---[0;39m [2m[           main][0;39m [36mw.s.c.ServletWebServerApplicationContext[0;39m [2m:[0;39m Root WebApplicationContext: initialization completed in 3610 ms
[2m2025-08-04 17:02:46.280[0;39m [32m INFO[0;39m [35m1312[0;39m [2m---[0;39m [2m[           main][0;39m [36morg.mongodb.driver.client               [0;39m [2m:[0;39m MongoClient with metadata {"driver": {"name": "mongo-java-driver|sync|spring-boot", "version": "5.5.1"}, "os": {"type": "Darwin", "name": "Mac OS X", "architecture": "aarch64", "version": "15.5"}, "platform": "Java/Azul Systems, Inc./17.0.15+6-LTS"} created with settings MongoClientSettings{readPreference=primary, writeConcern=WriteConcern{w=null, wTimeout=null ms, journal=null}, retryWrites=true, retryReads=true, readConcern=ReadConcern{level=null}, credential=MongoCredential{mechanism=null, userName='admin', source='admin', password=<hidden>, mechanismProperties=<hidden>}, transportSettings=null, commandListeners=[], codecRegistry=ProvidersCodecRegistry{codecProviders=[ValueCodecProvider{}, BsonValueCodecProvider{}, DBRefCodecProvider{}, DBObjectCodecProvider{}, DocumentCodecProvider{}, CollectionCodecProvider{}, IterableCodecProvider{}, MapCodecProvider{}, GeoJsonCodecProvider{}, GridFSFileCodecProvider{}, Jsr310CodecProvider{}, JsonObjectCodecProvider{}, BsonCodecProvider{}, EnumCodecProvider{}, com.mongodb.client.model.mql.ExpressionCodecProvider@26a5007, com.mongodb.Jep395RecordCodecProvider@4d4f6121, com.mongodb.KotlinCodecProvider@2845fd12]}, loggerSettings=LoggerSettings{maxDocumentLength=1000}, clusterSettings={hosts=[*************:37017], srvServiceName=mongodb, mode=SINGLE, requiredClusterType=UNKNOWN, requiredReplicaSetName='null', serverSelector='null', clusterListeners='[]', serverSelectionTimeout='30000 ms', localThreshold='15 ms'}, socketSettings=SocketSettings{connectTimeoutMS=10000, readTimeoutMS=0, receiveBufferSize=0, proxySettings=ProxySettings{host=null, port=null, username=null, password=null}}, heartbeatSocketSettings=SocketSettings{connectTimeoutMS=10000, readTimeoutMS=10000, receiveBufferSize=0, proxySettings=ProxySettings{host=null, port=null, username=null, password=null}}, connectionPoolSettings=ConnectionPoolSettings{maxSize=100, minSize=0, maxWaitTimeMS=120000, maxConnectionLifeTimeMS=0, maxConnectionIdleTimeMS=0, maintenanceInitialDelayMS=0, maintenanceFrequencyMS=60000, connectionPoolListeners=[], maxConnecting=2}, serverSettings=ServerSettings{heartbeatFrequencyMS=10000, minHeartbeatFrequencyMS=500, serverMonitoringMode=AUTO, serverListeners='[]', serverMonitorListeners='[]'}, sslSettings=SslSettings{enabled=false, invalidHostNameAllowed=false, context=null}, applicationName='null', compressorList=[], uuidRepresentation=JAVA_LEGACY, serverApi=null, autoEncryptionSettings=null, dnsClient=null, inetAddressResolver=null, contextProvider=null, timeoutMS=null}
[2m2025-08-04 17:02:46.438[0;39m [32m INFO[0;39m [35m1312[0;39m [2m---[0;39m [2m[168.9.254:37017][0;39m [36morg.mongodb.driver.cluster              [0;39m [2m:[0;39m Monitor thread successfully connected to server with description ServerDescription{address=*************:37017, type=STANDALONE, cryptd=false, state=CONNECTED, ok=true, minWireVersion=0, maxWireVersion=13, maxDocumentSize=16777216, logicalSessionTimeoutMinutes=30, roundTripTimeNanos=87695416, minRoundTripTimeNanos=0}
[2m2025-08-04 17:02:46.665[0;39m [32m INFO[0;39m [35m1312[0;39m [2m---[0;39m [2m[           main][0;39m [36mc.g.y.a.MybatisPlusJoinAutoConfiguration[0;39m [2m:[0;39m mybatis plus join properties config complete
[2m2025-08-04 17:02:46.904[0;39m [32m INFO[0;39m [35m1312[0;39m [2m---[0;39m [2m[           main][0;39m [36mc.g.y.a.MybatisPlusJoinAutoConfiguration[0;39m [2m:[0;39m mybatis plus join SqlInjector init
[2m2025-08-04 17:02:46.910[0;39m [32mDEBUG[0;39m [35m1312[0;39m [2m---[0;39m [2m[           main][0;39m [36mc.b.m.e.s.MybatisSqlSessionFactoryBean  [0;39m [2m:[0;39m Registered plugin: 'com.sanythadmin.common.core.mybatisplus.interceptor.DecryptInterceptor@7a65e842'
[2m2025-08-04 17:02:46.910[0;39m [32mDEBUG[0;39m [35m1312[0;39m [2m---[0;39m [2m[           main][0;39m [36mc.b.m.e.s.MybatisSqlSessionFactoryBean  [0;39m [2m:[0;39m Registered plugin: 'com.baomidou.mybatisplus.extension.plugins.MybatisPlusInterceptor@4a842e8a'
[2m2025-08-04 17:02:46.910[0;39m [32mDEBUG[0;39m [35m1312[0;39m [2m---[0;39m [2m[           main][0;39m [36mc.b.m.e.s.MybatisSqlSessionFactoryBean  [0;39m [2m:[0;39m Registered plugin: 'com.github.yulichang.interceptor.MPJInterceptor@73b6fbd1'
[2m2025-08-04 17:02:47.125[0;39m [32mDEBUG[0;39m [35m1312[0;39m [2m---[0;39m [2m[           main][0;39m [36mc.b.m.e.s.MybatisSqlSessionFactoryBean  [0;39m [2m:[0;39m Parsed mapper file: 'file [/Users/<USER>/WorkSpace/sanyth-xuegong-5/sanyth-xuegong-5-server/xggl-api/target/classes/com/sanythadmin/common/system/mapper/xml/EmailRecordMapper.xml]'
[2m2025-08-04 17:02:47.172[0;39m [32mDEBUG[0;39m [35m1312[0;39m [2m---[0;39m [2m[           main][0;39m [36mc.b.m.e.s.MybatisSqlSessionFactoryBean  [0;39m [2m:[0;39m Parsed mapper file: 'file [/Users/<USER>/WorkSpace/sanyth-xuegong-5/sanyth-xuegong-5-server/xggl-api/target/classes/com/sanythadmin/common/system/mapper/xml/LoginRecordMapper.xml]'
[2m2025-08-04 17:02:47.210[0;39m [32mDEBUG[0;39m [35m1312[0;39m [2m---[0;39m [2m[           main][0;39m [36mc.b.m.e.s.MybatisSqlSessionFactoryBean  [0;39m [2m:[0;39m Parsed mapper file: 'file [/Users/<USER>/WorkSpace/sanyth-xuegong-5/sanyth-xuegong-5-server/xggl-api/target/classes/com/sanythadmin/common/system/mapper/xml/RoleMenuMapper.xml]'
[2m2025-08-04 17:02:47.258[0;39m [32mDEBUG[0;39m [35m1312[0;39m [2m---[0;39m [2m[           main][0;39m [36mc.b.m.e.s.MybatisSqlSessionFactoryBean  [0;39m [2m:[0;39m Parsed mapper file: 'file [/Users/<USER>/WorkSpace/sanyth-xuegong-5/sanyth-xuegong-5-server/xggl-api/target/classes/com/sanythadmin/common/system/mapper/xml/SysAccountMapper.xml]'
[2m2025-08-04 17:02:47.282[0;39m [32mDEBUG[0;39m [35m1312[0;39m [2m---[0;39m [2m[           main][0;39m [36mc.b.m.e.s.MybatisSqlSessionFactoryBean  [0;39m [2m:[0;39m Parsed mapper file: 'file [/Users/<USER>/WorkSpace/sanyth-xuegong-5/sanyth-xuegong-5-server/xggl-api/target/classes/com/sanythadmin/common/system/mapper/xml/SysAccountRoleMapper.xml]'
[2m2025-08-04 17:02:47.306[0;39m [32mDEBUG[0;39m [35m1312[0;39m [2m---[0;39m [2m[           main][0;39m [36mc.b.m.e.s.MybatisSqlSessionFactoryBean  [0;39m [2m:[0;39m Parsed mapper file: 'file [/Users/<USER>/WorkSpace/sanyth-xuegong-5/sanyth-xuegong-5-server/xggl-api/target/classes/com/sanythadmin/common/system/mapper/xml/SysJwtMapper.xml]'
[2m2025-08-04 17:02:47.358[0;39m [32mDEBUG[0;39m [35m1312[0;39m [2m---[0;39m [2m[           main][0;39m [36mc.b.m.e.s.MybatisSqlSessionFactoryBean  [0;39m [2m:[0;39m Parsed mapper file: 'file [/Users/<USER>/WorkSpace/sanyth-xuegong-5/sanyth-xuegong-5-server/xggl-api/target/classes/com/sanythadmin/common/system/mapper/xml/SysMenuMapper.xml]'
[2m2025-08-04 17:02:47.383[0;39m [32mDEBUG[0;39m [35m1312[0;39m [2m---[0;39m [2m[           main][0;39m [36mc.b.m.e.s.MybatisSqlSessionFactoryBean  [0;39m [2m:[0;39m Parsed mapper file: 'file [/Users/<USER>/WorkSpace/sanyth-xuegong-5/sanyth-xuegong-5-server/xggl-api/target/classes/com/sanythadmin/common/system/mapper/xml/SysModuleSetupMapper.xml]'
[2m2025-08-04 17:02:47.409[0;39m [32mDEBUG[0;39m [35m1312[0;39m [2m---[0;39m [2m[           main][0;39m [36mc.b.m.e.s.MybatisSqlSessionFactoryBean  [0;39m [2m:[0;39m Parsed mapper file: 'file [/Users/<USER>/WorkSpace/sanyth-xuegong-5/sanyth-xuegong-5-server/xggl-api/target/classes/com/sanythadmin/common/system/mapper/xml/SysParamMapper.xml]'
[2m2025-08-04 17:02:47.428[0;39m [32mDEBUG[0;39m [35m1312[0;39m [2m---[0;39m [2m[           main][0;39m [36mc.b.m.e.s.MybatisSqlSessionFactoryBean  [0;39m [2m:[0;39m Parsed mapper file: 'file [/Users/<USER>/WorkSpace/sanyth-xuegong-5/sanyth-xuegong-5-server/xggl-api/target/classes/com/sanythadmin/common/system/mapper/xml/SysRoleMapper.xml]'
[2m2025-08-04 17:02:47.458[0;39m [32mDEBUG[0;39m [35m1312[0;39m [2m---[0;39m [2m[           main][0;39m [36mc.b.m.e.s.MybatisSqlSessionFactoryBean  [0;39m [2m:[0;39m Parsed mapper file: 'file [/Users/<USER>/WorkSpace/sanyth-xuegong-5/sanyth-xuegong-5-server/xggl-api/target/classes/com/sanythadmin/common/system/mapper/xml/SysRoleScopeMapper.xml]'
[2m2025-08-04 17:02:47.518[0;39m [32mDEBUG[0;39m [35m1312[0;39m [2m---[0;39m [2m[           main][0;39m [36mc.b.m.e.s.MybatisSqlSessionFactoryBean  [0;39m [2m:[0;39m Parsed mapper file: 'file [/Users/<USER>/WorkSpace/sanyth-xuegong-5/sanyth-xuegong-5-server/xggl-api/target/classes/com/sanythadmin/common/system/mapper/xml/UserInfoMapper.xml]'
[2m2025-08-04 17:02:47.535[0;39m [32mDEBUG[0;39m [35m1312[0;39m [2m---[0;39m [2m[           main][0;39m [36mc.b.m.e.s.MybatisSqlSessionFactoryBean  [0;39m [2m:[0;39m Parsed mapper file: 'file [/Users/<USER>/WorkSpace/sanyth-xuegong-5/sanyth-xuegong-5-server/xggl-api/target/classes/com/sanythadmin/common/system/mapper/xml/UserOrgMapMapper.xml]'
[2m2025-08-04 17:02:47.562[0;39m [32mDEBUG[0;39m [35m1312[0;39m [2m---[0;39m [2m[           main][0;39m [36mc.b.m.e.s.MybatisSqlSessionFactoryBean  [0;39m [2m:[0;39m Parsed mapper file: 'file [/Users/<USER>/WorkSpace/sanyth-xuegong-5/sanyth-xuegong-5-server/xggl-api/target/classes/com/sanythadmin/project/checkin/mapper/xml/CheckinAddressMapper.xml]'
[2m2025-08-04 17:02:47.581[0;39m [32mDEBUG[0;39m [35m1312[0;39m [2m---[0;39m [2m[           main][0;39m [36mc.b.m.e.s.MybatisSqlSessionFactoryBean  [0;39m [2m:[0;39m Parsed mapper file: 'file [/Users/<USER>/WorkSpace/sanyth-xuegong-5/sanyth-xuegong-5-server/xggl-api/target/classes/com/sanythadmin/project/checkin/mapper/xml/CheckinItemAddressMapper.xml]'
[2m2025-08-04 17:02:47.604[0;39m [32mDEBUG[0;39m [35m1312[0;39m [2m---[0;39m [2m[           main][0;39m [36mc.b.m.e.s.MybatisSqlSessionFactoryBean  [0;39m [2m:[0;39m Parsed mapper file: 'file [/Users/<USER>/WorkSpace/sanyth-xuegong-5/sanyth-xuegong-5-server/xggl-api/target/classes/com/sanythadmin/project/checkin/mapper/xml/CheckinItemMapper.xml]'
[2m2025-08-04 17:02:47.630[0;39m [32mDEBUG[0;39m [35m1312[0;39m [2m---[0;39m [2m[           main][0;39m [36mc.b.m.e.s.MybatisSqlSessionFactoryBean  [0;39m [2m:[0;39m Parsed mapper file: 'file [/Users/<USER>/WorkSpace/sanyth-xuegong-5/sanyth-xuegong-5-server/xggl-api/target/classes/com/sanythadmin/project/checkin/mapper/xml/CheckinRecordMapper.xml]'
[2m2025-08-04 17:02:47.652[0;39m [32mDEBUG[0;39m [35m1312[0;39m [2m---[0;39m [2m[           main][0;39m [36mc.b.m.e.s.MybatisSqlSessionFactoryBean  [0;39m [2m:[0;39m Parsed mapper file: 'file [/Users/<USER>/WorkSpace/sanyth-xuegong-5/sanyth-xuegong-5-server/xggl-api/target/classes/com/sanythadmin/project/code/mapper/xml/CodeBjbMapper.xml]'
[2m2025-08-04 17:02:47.671[0;39m [32mDEBUG[0;39m [35m1312[0;39m [2m---[0;39m [2m[           main][0;39m [36mc.b.m.e.s.MybatisSqlSessionFactoryBean  [0;39m [2m:[0;39m Parsed mapper file: 'file [/Users/<USER>/WorkSpace/sanyth-xuegong-5/sanyth-xuegong-5-server/xggl-api/target/classes/com/sanythadmin/project/code/mapper/xml/CodeCommonMapper.xml]'
[2m2025-08-04 17:02:47.692[0;39m [32mDEBUG[0;39m [35m1312[0;39m [2m---[0;39m [2m[           main][0;39m [36mc.b.m.e.s.MybatisSqlSessionFactoryBean  [0;39m [2m:[0;39m Parsed mapper file: 'file [/Users/<USER>/WorkSpace/sanyth-xuegong-5/sanyth-xuegong-5-server/xggl-api/target/classes/com/sanythadmin/project/code/mapper/xml/CodeDwbMapper.xml]'
[2m2025-08-04 17:02:47.708[0;39m [32mDEBUG[0;39m [35m1312[0;39m [2m---[0;39m [2m[           main][0;39m [36mc.b.m.e.s.MybatisSqlSessionFactoryBean  [0;39m [2m:[0;39m Parsed mapper file: 'file [/Users/<USER>/WorkSpace/sanyth-xuegong-5/sanyth-xuegong-5-server/xggl-api/target/classes/com/sanythadmin/project/code/mapper/xml/CodeMzbMapper.xml]'
[2m2025-08-04 17:02:47.724[0;39m [32mDEBUG[0;39m [35m1312[0;39m [2m---[0;39m [2m[           main][0;39m [36mc.b.m.e.s.MybatisSqlSessionFactoryBean  [0;39m [2m:[0;39m Parsed mapper file: 'file [/Users/<USER>/WorkSpace/sanyth-xuegong-5/sanyth-xuegong-5-server/xggl-api/target/classes/com/sanythadmin/project/code/mapper/xml/CodeNjbMapper.xml]'
[2m2025-08-04 17:02:47.741[0;39m [32mDEBUG[0;39m [35m1312[0;39m [2m---[0;39m [2m[           main][0;39m [36mc.b.m.e.s.MybatisSqlSessionFactoryBean  [0;39m [2m:[0;39m Parsed mapper file: 'file [/Users/<USER>/WorkSpace/sanyth-xuegong-5/sanyth-xuegong-5-server/xggl-api/target/classes/com/sanythadmin/project/code/mapper/xml/CodePyccbMapper.xml]'
[2m2025-08-04 17:02:47.759[0;39m [32mDEBUG[0;39m [35m1312[0;39m [2m---[0;39m [2m[           main][0;39m [36mc.b.m.e.s.MybatisSqlSessionFactoryBean  [0;39m [2m:[0;39m Parsed mapper file: 'file [/Users/<USER>/WorkSpace/sanyth-xuegong-5/sanyth-xuegong-5-server/xggl-api/target/classes/com/sanythadmin/project/code/mapper/xml/CodeTypeMapper.xml]'
[2m2025-08-04 17:02:47.775[0;39m [32mDEBUG[0;39m [35m1312[0;39m [2m---[0;39m [2m[           main][0;39m [36mc.b.m.e.s.MybatisSqlSessionFactoryBean  [0;39m [2m:[0;39m Parsed mapper file: 'file [/Users/<USER>/WorkSpace/sanyth-xuegong-5/sanyth-xuegong-5-server/xggl-api/target/classes/com/sanythadmin/project/code/mapper/xml/CodeXqbMapper.xml]'
[2m2025-08-04 17:02:47.808[0;39m [32mDEBUG[0;39m [35m1312[0;39m [2m---[0;39m [2m[           main][0;39m [36mc.b.m.e.s.MybatisSqlSessionFactoryBean  [0;39m [2m:[0;39m Parsed mapper file: 'file [/Users/<USER>/WorkSpace/sanyth-xuegong-5/sanyth-xuegong-5-server/xggl-api/target/classes/com/sanythadmin/project/code/mapper/xml/CodeXsztMapper.xml]'
[2m2025-08-04 17:02:47.835[0;39m [32mDEBUG[0;39m [35m1312[0;39m [2m---[0;39m [2m[           main][0;39m [36mc.b.m.e.s.MybatisSqlSessionFactoryBean  [0;39m [2m:[0;39m Parsed mapper file: 'file [/Users/<USER>/WorkSpace/sanyth-xuegong-5/sanyth-xuegong-5-server/xggl-api/target/classes/com/sanythadmin/project/code/mapper/xml/CodeZybMapper.xml]'
[2m2025-08-04 17:02:47.857[0;39m [32mDEBUG[0;39m [35m1312[0;39m [2m---[0;39m [2m[           main][0;39m [36mc.b.m.e.s.MybatisSqlSessionFactoryBean  [0;39m [2m:[0;39m Parsed mapper file: 'file [/Users/<USER>/WorkSpace/sanyth-xuegong-5/sanyth-xuegong-5-server/xggl-api/target/classes/com/sanythadmin/project/code/mapper/xml/CodeZzmmbMapper.xml]'
[2m2025-08-04 17:02:47.877[0;39m [32mDEBUG[0;39m [35m1312[0;39m [2m---[0;39m [2m[           main][0;39m [36mc.b.m.e.s.MybatisSqlSessionFactoryBean  [0;39m [2m:[0;39m Parsed mapper file: 'file [/Users/<USER>/WorkSpace/sanyth-xuegong-5/sanyth-xuegong-5-server/xggl-api/target/classes/com/sanythadmin/project/code/mapper/xml/ScoreSourceConfigMapper.xml]'
[2m2025-08-04 17:02:47.901[0;39m [32mDEBUG[0;39m [35m1312[0;39m [2m---[0;39m [2m[           main][0;39m [36mc.b.m.e.s.MybatisSqlSessionFactoryBean  [0;39m [2m:[0;39m Parsed mapper file: 'file [/Users/<USER>/WorkSpace/sanyth-xuegong-5/sanyth-xuegong-5-server/xggl-api/target/classes/com/sanythadmin/project/condition/mapper/xml/TempConditionFieldMapper.xml]'
[2m2025-08-04 17:02:47.922[0;39m [32mDEBUG[0;39m [35m1312[0;39m [2m---[0;39m [2m[           main][0;39m [36mc.b.m.e.s.MybatisSqlSessionFactoryBean  [0;39m [2m:[0;39m Parsed mapper file: 'file [/Users/<USER>/WorkSpace/sanyth-xuegong-5/sanyth-xuegong-5-server/xggl-api/target/classes/com/sanythadmin/project/condition/mapper/xml/TempConditionMapper.xml]'
[2m2025-08-04 17:02:47.944[0;39m [32mDEBUG[0;39m [35m1312[0;39m [2m---[0;39m [2m[           main][0;39m [36mc.b.m.e.s.MybatisSqlSessionFactoryBean  [0;39m [2m:[0;39m Parsed mapper file: 'file [/Users/<USER>/WorkSpace/sanyth-xuegong-5/sanyth-xuegong-5-server/xggl-api/target/classes/com/sanythadmin/project/dictionary/mapper/xml/DictionaryFieldEduLevelMapper.xml]'
[2m2025-08-04 17:02:47.963[0;39m [32mDEBUG[0;39m [35m1312[0;39m [2m---[0;39m [2m[           main][0;39m [36mc.b.m.e.s.MybatisSqlSessionFactoryBean  [0;39m [2m:[0;39m Parsed mapper file: 'file [/Users/<USER>/WorkSpace/sanyth-xuegong-5/sanyth-xuegong-5-server/xggl-api/target/classes/com/sanythadmin/project/dictionary/mapper/xml/DictionaryFieldHideRoleMapper.xml]'
[2m2025-08-04 17:02:47.983[0;39m [32mDEBUG[0;39m [35m1312[0;39m [2m---[0;39m [2m[           main][0;39m [36mc.b.m.e.s.MybatisSqlSessionFactoryBean  [0;39m [2m:[0;39m Parsed mapper file: 'file [/Users/<USER>/WorkSpace/sanyth-xuegong-5/sanyth-xuegong-5-server/xggl-api/target/classes/com/sanythadmin/project/dictionary/mapper/xml/DictionaryFieldLinkMapper.xml]'
[2m2025-08-04 17:02:48.010[0;39m [32mDEBUG[0;39m [35m1312[0;39m [2m---[0;39m [2m[           main][0;39m [36mc.b.m.e.s.MybatisSqlSessionFactoryBean  [0;39m [2m:[0;39m Parsed mapper file: 'file [/Users/<USER>/WorkSpace/sanyth-xuegong-5/sanyth-xuegong-5-server/xggl-api/target/classes/com/sanythadmin/project/dictionary/mapper/xml/DictionaryFieldMapper.xml]'
[2m2025-08-04 17:02:48.027[0;39m [32mDEBUG[0;39m [35m1312[0;39m [2m---[0;39m [2m[           main][0;39m [36mc.b.m.e.s.MybatisSqlSessionFactoryBean  [0;39m [2m:[0;39m Parsed mapper file: 'file [/Users/<USER>/WorkSpace/sanyth-xuegong-5/sanyth-xuegong-5-server/xggl-api/target/classes/com/sanythadmin/project/dictionary/mapper/xml/DictionaryGroupEduLevelMapper.xml]'
[2m2025-08-04 17:02:48.045[0;39m [32mDEBUG[0;39m [35m1312[0;39m [2m---[0;39m [2m[           main][0;39m [36mc.b.m.e.s.MybatisSqlSessionFactoryBean  [0;39m [2m:[0;39m Parsed mapper file: 'file [/Users/<USER>/WorkSpace/sanyth-xuegong-5/sanyth-xuegong-5-server/xggl-api/target/classes/com/sanythadmin/project/dictionary/mapper/xml/DictionaryGroupHideRoleMapper.xml]'
[2m2025-08-04 17:02:48.072[0;39m [32mDEBUG[0;39m [35m1312[0;39m [2m---[0;39m [2m[           main][0;39m [36mc.b.m.e.s.MybatisSqlSessionFactoryBean  [0;39m [2m:[0;39m Parsed mapper file: 'file [/Users/<USER>/WorkSpace/sanyth-xuegong-5/sanyth-xuegong-5-server/xggl-api/target/classes/com/sanythadmin/project/dictionary/mapper/xml/DictionaryGroupMapper.xml]'
[2m2025-08-04 17:02:48.091[0;39m [32mDEBUG[0;39m [35m1312[0;39m [2m---[0;39m [2m[           main][0;39m [36mc.b.m.e.s.MybatisSqlSessionFactoryBean  [0;39m [2m:[0;39m Parsed mapper file: 'file [/Users/<USER>/WorkSpace/sanyth-xuegong-5/sanyth-xuegong-5-server/xggl-api/target/classes/com/sanythadmin/project/dictionary/mapper/xml/ListGroupConfigMapper.xml]'
[2m2025-08-04 17:02:48.112[0;39m [32mDEBUG[0;39m [35m1312[0;39m [2m---[0;39m [2m[           main][0;39m [36mc.b.m.e.s.MybatisSqlSessionFactoryBean  [0;39m [2m:[0;39m Parsed mapper file: 'file [/Users/<USER>/WorkSpace/sanyth-xuegong-5/sanyth-xuegong-5-server/xggl-api/target/classes/com/sanythadmin/project/dictionary/mapper/xml/SelectControlApiMapper.xml]'
[2m2025-08-04 17:02:48.142[0;39m [32mDEBUG[0;39m [35m1312[0;39m [2m---[0;39m [2m[           main][0;39m [36mc.b.m.e.s.MybatisSqlSessionFactoryBean  [0;39m [2m:[0;39m Parsed mapper file: 'file [/Users/<USER>/WorkSpace/sanyth-xuegong-5/sanyth-xuegong-5-server/xggl-api/target/classes/com/sanythadmin/project/evaluate/mapper/xml/EvaluateApplicationInfoMapper.xml]'
[2m2025-08-04 17:02:48.160[0;39m [32mDEBUG[0;39m [35m1312[0;39m [2m---[0;39m [2m[           main][0;39m [36mc.b.m.e.s.MybatisSqlSessionFactoryBean  [0;39m [2m:[0;39m Parsed mapper file: 'file [/Users/<USER>/WorkSpace/sanyth-xuegong-5/sanyth-xuegong-5-server/xggl-api/target/classes/com/sanythadmin/project/evaluate/mapper/xml/EvaluateApplicationReferenceInfoMapper.xml]'
[2m2025-08-04 17:02:48.192[0;39m [32mDEBUG[0;39m [35m1312[0;39m [2m---[0;39m [2m[           main][0;39m [36mc.b.m.e.s.MybatisSqlSessionFactoryBean  [0;39m [2m:[0;39m Parsed mapper file: 'file [/Users/<USER>/WorkSpace/sanyth-xuegong-5/sanyth-xuegong-5-server/xggl-api/target/classes/com/sanythadmin/project/evaluate/mapper/xml/EvaluateApprovalNodeMapper.xml]'
[2m2025-08-04 17:02:48.214[0;39m [32mDEBUG[0;39m [35m1312[0;39m [2m---[0;39m [2m[           main][0;39m [36mc.b.m.e.s.MybatisSqlSessionFactoryBean  [0;39m [2m:[0;39m Parsed mapper file: 'file [/Users/<USER>/WorkSpace/sanyth-xuegong-5/sanyth-xuegong-5-server/xggl-api/target/classes/com/sanythadmin/project/evaluate/mapper/xml/EvaluateApprovalNodeRecordMapper.xml]'
[2m2025-08-04 17:02:48.233[0;39m [32mDEBUG[0;39m [35m1312[0;39m [2m---[0;39m [2m[           main][0;39m [36mc.b.m.e.s.MybatisSqlSessionFactoryBean  [0;39m [2m:[0;39m Parsed mapper file: 'file [/Users/<USER>/WorkSpace/sanyth-xuegong-5/sanyth-xuegong-5-server/xggl-api/target/classes/com/sanythadmin/project/evaluate/mapper/xml/EvaluateConfigMapper.xml]'
[2m2025-08-04 17:02:48.250[0;39m [32mDEBUG[0;39m [35m1312[0;39m [2m---[0;39m [2m[           main][0;39m [36mc.b.m.e.s.MybatisSqlSessionFactoryBean  [0;39m [2m:[0;39m Parsed mapper file: 'file [/Users/<USER>/WorkSpace/sanyth-xuegong-5/sanyth-xuegong-5-server/xggl-api/target/classes/com/sanythadmin/project/evaluate/mapper/xml/EvaluateConfigScopeMapper.xml]'
[2m2025-08-04 17:02:48.269[0;39m [32mDEBUG[0;39m [35m1312[0;39m [2m---[0;39m [2m[           main][0;39m [36mc.b.m.e.s.MybatisSqlSessionFactoryBean  [0;39m [2m:[0;39m Parsed mapper file: 'file [/Users/<USER>/WorkSpace/sanyth-xuegong-5/sanyth-xuegong-5-server/xggl-api/target/classes/com/sanythadmin/project/evaluate/mapper/xml/EvaluateItemDetailMapper.xml]'
[2m2025-08-04 17:02:48.287[0;39m [32mDEBUG[0;39m [35m1312[0;39m [2m---[0;39m [2m[           main][0;39m [36mc.b.m.e.s.MybatisSqlSessionFactoryBean  [0;39m [2m:[0;39m Parsed mapper file: 'file [/Users/<USER>/WorkSpace/sanyth-xuegong-5/sanyth-xuegong-5-server/xggl-api/target/classes/com/sanythadmin/project/evaluate/mapper/xml/EvaluateItemDetailScopeMapper.xml]'
[2m2025-08-04 17:02:48.307[0;39m [32mDEBUG[0;39m [35m1312[0;39m [2m---[0;39m [2m[           main][0;39m [36mc.b.m.e.s.MybatisSqlSessionFactoryBean  [0;39m [2m:[0;39m Parsed mapper file: 'file [/Users/<USER>/WorkSpace/sanyth-xuegong-5/sanyth-xuegong-5-server/xggl-api/target/classes/com/sanythadmin/project/evaluate/mapper/xml/EvaluateItemMapper.xml]'
[2m2025-08-04 17:02:48.328[0;39m [32mDEBUG[0;39m [35m1312[0;39m [2m---[0;39m [2m[           main][0;39m [36mc.b.m.e.s.MybatisSqlSessionFactoryBean  [0;39m [2m:[0;39m Parsed mapper file: 'file [/Users/<USER>/WorkSpace/sanyth-xuegong-5/sanyth-xuegong-5-server/xggl-api/target/classes/com/sanythadmin/project/evaluate/mapper/xml/EvaluatePeerReviewRecordMapper.xml]'
[2m2025-08-04 17:02:48.346[0;39m [32mDEBUG[0;39m [35m1312[0;39m [2m---[0;39m [2m[           main][0;39m [36mc.b.m.e.s.MybatisSqlSessionFactoryBean  [0;39m [2m:[0;39m Parsed mapper file: 'file [/Users/<USER>/WorkSpace/sanyth-xuegong-5/sanyth-xuegong-5-server/xggl-api/target/classes/com/sanythadmin/project/evaluate/mapper/xml/EvaluateReviewScoreMapper.xml]'
[2m2025-08-04 17:02:48.365[0;39m [32mDEBUG[0;39m [35m1312[0;39m [2m---[0;39m [2m[           main][0;39m [36mc.b.m.e.s.MybatisSqlSessionFactoryBean  [0;39m [2m:[0;39m Parsed mapper file: 'file [/Users/<USER>/WorkSpace/sanyth-xuegong-5/sanyth-xuegong-5-server/xggl-api/target/classes/com/sanythadmin/project/evaluate/mapper/xml/EvaluateScoreConfirmRecordMapper.xml]'
[2m2025-08-04 17:02:48.391[0;39m [32mDEBUG[0;39m [35m1312[0;39m [2m---[0;39m [2m[           main][0;39m [36mc.b.m.e.s.MybatisSqlSessionFactoryBean  [0;39m [2m:[0;39m Parsed mapper file: 'file [/Users/<USER>/WorkSpace/sanyth-xuegong-5/sanyth-xuegong-5-server/xggl-api/target/classes/com/sanythadmin/project/evaluate/mapper/xml/EvaluateScoreMapper.xml]'
[2m2025-08-04 17:02:48.418[0;39m [32mDEBUG[0;39m [35m1312[0;39m [2m---[0;39m [2m[           main][0;39m [36mc.b.m.e.s.MybatisSqlSessionFactoryBean  [0;39m [2m:[0;39m Parsed mapper file: 'file [/Users/<USER>/WorkSpace/sanyth-xuegong-5/sanyth-xuegong-5-server/xggl-api/target/classes/com/sanythadmin/project/exam/mapper/xml/ExamAnswerInfoMapper.xml]'
[2m2025-08-04 17:02:48.437[0;39m [32mDEBUG[0;39m [35m1312[0;39m [2m---[0;39m [2m[           main][0;39m [36mc.b.m.e.s.MybatisSqlSessionFactoryBean  [0;39m [2m:[0;39m Parsed mapper file: 'file [/Users/<USER>/WorkSpace/sanyth-xuegong-5/sanyth-xuegong-5-server/xggl-api/target/classes/com/sanythadmin/project/exam/mapper/xml/ExamAnswerMapper.xml]'
[2m2025-08-04 17:02:48.454[0;39m [32mDEBUG[0;39m [35m1312[0;39m [2m---[0;39m [2m[           main][0;39m [36mc.b.m.e.s.MybatisSqlSessionFactoryBean  [0;39m [2m:[0;39m Parsed mapper file: 'file [/Users/<USER>/WorkSpace/sanyth-xuegong-5/sanyth-xuegong-5-server/xggl-api/target/classes/com/sanythadmin/project/exam/mapper/xml/ExamClassMapper.xml]'
[2m2025-08-04 17:02:48.476[0;39m [32mDEBUG[0;39m [35m1312[0;39m [2m---[0;39m [2m[           main][0;39m [36mc.b.m.e.s.MybatisSqlSessionFactoryBean  [0;39m [2m:[0;39m Parsed mapper file: 'file [/Users/<USER>/WorkSpace/sanyth-xuegong-5/sanyth-xuegong-5-server/xggl-api/target/classes/com/sanythadmin/project/exam/mapper/xml/ExamMapper.xml]'
[2m2025-08-04 17:02:48.494[0;39m [32mDEBUG[0;39m [35m1312[0;39m [2m---[0;39m [2m[           main][0;39m [36mc.b.m.e.s.MybatisSqlSessionFactoryBean  [0;39m [2m:[0;39m Parsed mapper file: 'file [/Users/<USER>/WorkSpace/sanyth-xuegong-5/sanyth-xuegong-5-server/xggl-api/target/classes/com/sanythadmin/project/exam/mapper/xml/ExamPaperClassMapper.xml]'
[2m2025-08-04 17:02:48.496[0;39m [32mDEBUG[0;39m [35m1312[0;39m [2m---[0;39m [2m[           main][0;39m [36mc.b.m.e.s.MybatisSqlSessionFactoryBean  [0;39m [2m:[0;39m Parsed mapper file: 'file [/Users/<USER>/WorkSpace/sanyth-xuegong-5/sanyth-xuegong-5-server/xggl-api/target/classes/com/sanythadmin/project/exam/mapper/xml/ExamPaperContentMapper.xml]'
[2m2025-08-04 17:02:48.520[0;39m [32mDEBUG[0;39m [35m1312[0;39m [2m---[0;39m [2m[           main][0;39m [36mc.b.m.e.s.MybatisSqlSessionFactoryBean  [0;39m [2m:[0;39m Parsed mapper file: 'file [/Users/<USER>/WorkSpace/sanyth-xuegong-5/sanyth-xuegong-5-server/xggl-api/target/classes/com/sanythadmin/project/exam/mapper/xml/ExamPaperMapper.xml]'
[2m2025-08-04 17:02:48.538[0;39m [32mDEBUG[0;39m [35m1312[0;39m [2m---[0;39m [2m[           main][0;39m [36mc.b.m.e.s.MybatisSqlSessionFactoryBean  [0;39m [2m:[0;39m Parsed mapper file: 'file [/Users/<USER>/WorkSpace/sanyth-xuegong-5/sanyth-xuegong-5-server/xggl-api/target/classes/com/sanythadmin/project/exam/mapper/xml/ExamPaperQgroupMapper.xml]'
[2m2025-08-04 17:02:48.555[0;39m [32mDEBUG[0;39m [35m1312[0;39m [2m---[0;39m [2m[           main][0;39m [36mc.b.m.e.s.MybatisSqlSessionFactoryBean  [0;39m [2m:[0;39m Parsed mapper file: 'file [/Users/<USER>/WorkSpace/sanyth-xuegong-5/sanyth-xuegong-5-server/xggl-api/target/classes/com/sanythadmin/project/exam/mapper/xml/ExamPaperQuestionsMapper.xml]'
[2m2025-08-04 17:02:48.572[0;39m [32mDEBUG[0;39m [35m1312[0;39m [2m---[0;39m [2m[           main][0;39m [36mc.b.m.e.s.MybatisSqlSessionFactoryBean  [0;39m [2m:[0;39m Parsed mapper file: 'file [/Users/<USER>/WorkSpace/sanyth-xuegong-5/sanyth-xuegong-5-server/xggl-api/target/classes/com/sanythadmin/project/exam/mapper/xml/ExamPaperQuestionsOptionsMapper.xml]'
[2m2025-08-04 17:02:48.586[0;39m [32mDEBUG[0;39m [35m1312[0;39m [2m---[0;39m [2m[           main][0;39m [36mc.b.m.e.s.MybatisSqlSessionFactoryBean  [0;39m [2m:[0;39m Parsed mapper file: 'file [/Users/<USER>/WorkSpace/sanyth-xuegong-5/sanyth-xuegong-5-server/xggl-api/target/classes/com/sanythadmin/project/exam/mapper/xml/ExamPaperQuestionsRuleMapper.xml]'
[2m2025-08-04 17:02:48.601[0;39m [32mDEBUG[0;39m [35m1312[0;39m [2m---[0;39m [2m[           main][0;39m [36mc.b.m.e.s.MybatisSqlSessionFactoryBean  [0;39m [2m:[0;39m Parsed mapper file: 'file [/Users/<USER>/WorkSpace/sanyth-xuegong-5/sanyth-xuegong-5-server/xggl-api/target/classes/com/sanythadmin/project/exam/mapper/xml/ExamQuestionsClassMapper.xml]'
[2m2025-08-04 17:02:48.617[0;39m [32mDEBUG[0;39m [35m1312[0;39m [2m---[0;39m [2m[           main][0;39m [36mc.b.m.e.s.MybatisSqlSessionFactoryBean  [0;39m [2m:[0;39m Parsed mapper file: 'file [/Users/<USER>/WorkSpace/sanyth-xuegong-5/sanyth-xuegong-5-server/xggl-api/target/classes/com/sanythadmin/project/exam/mapper/xml/ExamQuestionsMapper.xml]'
[2m2025-08-04 17:02:48.633[0;39m [32mDEBUG[0;39m [35m1312[0;39m [2m---[0;39m [2m[           main][0;39m [36mc.b.m.e.s.MybatisSqlSessionFactoryBean  [0;39m [2m:[0;39m Parsed mapper file: 'file [/Users/<USER>/WorkSpace/sanyth-xuegong-5/sanyth-xuegong-5-server/xggl-api/target/classes/com/sanythadmin/project/exam/mapper/xml/ExamQuestionsOptionsMapper.xml]'
[2m2025-08-04 17:02:48.648[0;39m [32mDEBUG[0;39m [35m1312[0;39m [2m---[0;39m [2m[           main][0;39m [36mc.b.m.e.s.MybatisSqlSessionFactoryBean  [0;39m [2m:[0;39m Parsed mapper file: 'file [/Users/<USER>/WorkSpace/sanyth-xuegong-5/sanyth-xuegong-5-server/xggl-api/target/classes/com/sanythadmin/project/exam/mapper/xml/ExamRecordMapper.xml]'
[2m2025-08-04 17:02:48.673[0;39m [32mDEBUG[0;39m [35m1312[0;39m [2m---[0;39m [2m[           main][0;39m [36mc.b.m.e.s.MybatisSqlSessionFactoryBean  [0;39m [2m:[0;39m Parsed mapper file: 'file [/Users/<USER>/WorkSpace/sanyth-xuegong-5/sanyth-xuegong-5-server/xggl-api/target/classes/com/sanythadmin/project/form/mapper/xml/FormApplicationInfoMapper.xml]'
[2m2025-08-04 17:02:48.692[0;39m [32mDEBUG[0;39m [35m1312[0;39m [2m---[0;39m [2m[           main][0;39m [36mc.b.m.e.s.MybatisSqlSessionFactoryBean  [0;39m [2m:[0;39m Parsed mapper file: 'file [/Users/<USER>/WorkSpace/sanyth-xuegong-5/sanyth-xuegong-5-server/xggl-api/target/classes/com/sanythadmin/project/form/mapper/xml/FormApplicationListInfoMapper.xml]'
[2m2025-08-04 17:02:48.711[0;39m [32mDEBUG[0;39m [35m1312[0;39m [2m---[0;39m [2m[           main][0;39m [36mc.b.m.e.s.MybatisSqlSessionFactoryBean  [0;39m [2m:[0;39m Parsed mapper file: 'file [/Users/<USER>/WorkSpace/sanyth-xuegong-5/sanyth-xuegong-5-server/xggl-api/target/classes/com/sanythadmin/project/form/mapper/xml/FormApprovalNodeMapper.xml]'
[2m2025-08-04 17:02:48.729[0;39m [32mDEBUG[0;39m [35m1312[0;39m [2m---[0;39m [2m[           main][0;39m [36mc.b.m.e.s.MybatisSqlSessionFactoryBean  [0;39m [2m:[0;39m Parsed mapper file: 'file [/Users/<USER>/WorkSpace/sanyth-xuegong-5/sanyth-xuegong-5-server/xggl-api/target/classes/com/sanythadmin/project/form/mapper/xml/FormApprovalNodeRecordMapper.xml]'
[2m2025-08-04 17:02:48.748[0;39m [32mDEBUG[0;39m [35m1312[0;39m [2m---[0;39m [2m[           main][0;39m [36mc.b.m.e.s.MybatisSqlSessionFactoryBean  [0;39m [2m:[0;39m Parsed mapper file: 'file [/Users/<USER>/WorkSpace/sanyth-xuegong-5/sanyth-xuegong-5-server/xggl-api/target/classes/com/sanythadmin/project/form/mapper/xml/FormApprovalResultMapper.xml]'
[2m2025-08-04 17:02:48.766[0;39m [32mDEBUG[0;39m [35m1312[0;39m [2m---[0;39m [2m[           main][0;39m [36mc.b.m.e.s.MybatisSqlSessionFactoryBean  [0;39m [2m:[0;39m Parsed mapper file: 'file [/Users/<USER>/WorkSpace/sanyth-xuegong-5/sanyth-xuegong-5-server/xggl-api/target/classes/com/sanythadmin/project/form/mapper/xml/FormCustomFieldMapper.xml]'
[2m2025-08-04 17:02:48.784[0;39m [32mDEBUG[0;39m [35m1312[0;39m [2m---[0;39m [2m[           main][0;39m [36mc.b.m.e.s.MybatisSqlSessionFactoryBean  [0;39m [2m:[0;39m Parsed mapper file: 'file [/Users/<USER>/WorkSpace/sanyth-xuegong-5/sanyth-xuegong-5-server/xggl-api/target/classes/com/sanythadmin/project/form/mapper/xml/FormGroupMapper.xml]'
[2m2025-08-04 17:02:48.807[0;39m [32mDEBUG[0;39m [35m1312[0;39m [2m---[0;39m [2m[           main][0;39m [36mc.b.m.e.s.MybatisSqlSessionFactoryBean  [0;39m [2m:[0;39m Parsed mapper file: 'file [/Users/<USER>/WorkSpace/sanyth-xuegong-5/sanyth-xuegong-5-server/xggl-api/target/classes/com/sanythadmin/project/form/mapper/xml/FormLimitQuotaMapper.xml]'
[2m2025-08-04 17:02:48.830[0;39m [32mDEBUG[0;39m [35m1312[0;39m [2m---[0;39m [2m[           main][0;39m [36mc.b.m.e.s.MybatisSqlSessionFactoryBean  [0;39m [2m:[0;39m Parsed mapper file: 'file [/Users/<USER>/WorkSpace/sanyth-xuegong-5/sanyth-xuegong-5-server/xggl-api/target/classes/com/sanythadmin/project/form/mapper/xml/FormProjectMapper.xml]'
[2m2025-08-04 17:02:48.846[0;39m [32mDEBUG[0;39m [35m1312[0;39m [2m---[0;39m [2m[           main][0;39m [36mc.b.m.e.s.MybatisSqlSessionFactoryBean  [0;39m [2m:[0;39m Parsed mapper file: 'file [/Users/<USER>/WorkSpace/sanyth-xuegong-5/sanyth-xuegong-5-server/xggl-api/target/classes/com/sanythadmin/project/form/mapper/xml/FormProjectSpecialListMapper.xml]'
[2m2025-08-04 17:02:48.863[0;39m [32mDEBUG[0;39m [35m1312[0;39m [2m---[0;39m [2m[           main][0;39m [36mc.b.m.e.s.MybatisSqlSessionFactoryBean  [0;39m [2m:[0;39m Parsed mapper file: 'file [/Users/<USER>/WorkSpace/sanyth-xuegong-5/sanyth-xuegong-5-server/xggl-api/target/classes/com/sanythadmin/project/form/mapper/xml/FormProjectTemplateFieldMapper.xml]'
[2m2025-08-04 17:02:48.881[0;39m [32mDEBUG[0;39m [35m1312[0;39m [2m---[0;39m [2m[           main][0;39m [36mc.b.m.e.s.MybatisSqlSessionFactoryBean  [0;39m [2m:[0;39m Parsed mapper file: 'file [/Users/<USER>/WorkSpace/sanyth-xuegong-5/sanyth-xuegong-5-server/xggl-api/target/classes/com/sanythadmin/project/form/mapper/xml/FormPublicityContentMapper.xml]'
[2m2025-08-04 17:02:48.898[0;39m [32mDEBUG[0;39m [35m1312[0;39m [2m---[0;39m [2m[           main][0;39m [36mc.b.m.e.s.MybatisSqlSessionFactoryBean  [0;39m [2m:[0;39m Parsed mapper file: 'file [/Users/<USER>/WorkSpace/sanyth-xuegong-5/sanyth-xuegong-5-server/xggl-api/target/classes/com/sanythadmin/project/form/mapper/xml/FormPublicityFeedbackMapper.xml]'
[2m2025-08-04 17:02:48.917[0;39m [32mDEBUG[0;39m [35m1312[0;39m [2m---[0;39m [2m[           main][0;39m [36mc.b.m.e.s.MybatisSqlSessionFactoryBean  [0;39m [2m:[0;39m Parsed mapper file: 'file [/Users/<USER>/WorkSpace/sanyth-xuegong-5/sanyth-xuegong-5-server/xggl-api/target/classes/com/sanythadmin/project/form/mapper/xml/FormPublicityItemMapper.xml]'
[2m2025-08-04 17:02:48.933[0;39m [32mDEBUG[0;39m [35m1312[0;39m [2m---[0;39m [2m[           main][0;39m [36mc.b.m.e.s.MybatisSqlSessionFactoryBean  [0;39m [2m:[0;39m Parsed mapper file: 'file [/Users/<USER>/WorkSpace/sanyth-xuegong-5/sanyth-xuegong-5-server/xggl-api/target/classes/com/sanythadmin/project/form/mapper/xml/FormRestrictMapper.xml]'
[2m2025-08-04 17:02:48.951[0;39m [32mDEBUG[0;39m [35m1312[0;39m [2m---[0;39m [2m[           main][0;39m [36mc.b.m.e.s.MybatisSqlSessionFactoryBean  [0;39m [2m:[0;39m Parsed mapper file: 'file [/Users/<USER>/WorkSpace/sanyth-xuegong-5/sanyth-xuegong-5-server/xggl-api/target/classes/com/sanythadmin/project/form/mapper/xml/FormTemplateFieldLinkMapper.xml]'
[2m2025-08-04 17:02:48.969[0;39m [32mDEBUG[0;39m [35m1312[0;39m [2m---[0;39m [2m[           main][0;39m [36mc.b.m.e.s.MybatisSqlSessionFactoryBean  [0;39m [2m:[0;39m Parsed mapper file: 'file [/Users/<USER>/WorkSpace/sanyth-xuegong-5/sanyth-xuegong-5-server/xggl-api/target/classes/com/sanythadmin/project/form/mapper/xml/FormTemplateFieldMapper.xml]'
[2m2025-08-04 17:02:48.984[0;39m [32mDEBUG[0;39m [35m1312[0;39m [2m---[0;39m [2m[           main][0;39m [36mc.b.m.e.s.MybatisSqlSessionFactoryBean  [0;39m [2m:[0;39m Parsed mapper file: 'file [/Users/<USER>/WorkSpace/sanyth-xuegong-5/sanyth-xuegong-5-server/xggl-api/target/classes/com/sanythadmin/project/form/mapper/xml/FormTypeMapper.xml]'
[2m2025-08-04 17:02:49.003[0;39m [32mDEBUG[0;39m [35m1312[0;39m [2m---[0;39m [2m[           main][0;39m [36mc.b.m.e.s.MybatisSqlSessionFactoryBean  [0;39m [2m:[0;39m Parsed mapper file: 'file [/Users/<USER>/WorkSpace/sanyth-xuegong-5/sanyth-xuegong-5-server/xggl-api/target/classes/com/sanythadmin/project/mentalHealth/mapper/xml/XljkAppointmentInfoLogMapper.xml]'
[2m2025-08-04 17:02:49.022[0;39m [32mDEBUG[0;39m [35m1312[0;39m [2m---[0;39m [2m[           main][0;39m [36mc.b.m.e.s.MybatisSqlSessionFactoryBean  [0;39m [2m:[0;39m Parsed mapper file: 'file [/Users/<USER>/WorkSpace/sanyth-xuegong-5/sanyth-xuegong-5-server/xggl-api/target/classes/com/sanythadmin/project/mentalHealth/mapper/xml/XljkAppointmentInfoMapper.xml]'
[2m2025-08-04 17:02:49.037[0;39m [32mDEBUG[0;39m [35m1312[0;39m [2m---[0;39m [2m[           main][0;39m [36mc.b.m.e.s.MybatisSqlSessionFactoryBean  [0;39m [2m:[0;39m Parsed mapper file: 'file [/Users/<USER>/WorkSpace/sanyth-xuegong-5/sanyth-xuegong-5-server/xggl-api/target/classes/com/sanythadmin/project/mentalHealth/mapper/xml/XljkConsultantMapper.xml]'
[2m2025-08-04 17:02:49.055[0;39m [32mDEBUG[0;39m [35m1312[0;39m [2m---[0;39m [2m[           main][0;39m [36mc.b.m.e.s.MybatisSqlSessionFactoryBean  [0;39m [2m:[0;39m Parsed mapper file: 'file [/Users/<USER>/WorkSpace/sanyth-xuegong-5/sanyth-xuegong-5-server/xggl-api/target/classes/com/sanythadmin/project/mentalHealth/mapper/xml/XljkCounselingRecordMapper.xml]'
[2m2025-08-04 17:02:49.073[0;39m [32mDEBUG[0;39m [35m1312[0;39m [2m---[0;39m [2m[           main][0;39m [36mc.b.m.e.s.MybatisSqlSessionFactoryBean  [0;39m [2m:[0;39m Parsed mapper file: 'file [/Users/<USER>/WorkSpace/sanyth-xuegong-5/sanyth-xuegong-5-server/xggl-api/target/classes/com/sanythadmin/project/mentalHealth/mapper/xml/XljkCounselorScheduleMapper.xml]'
[2m2025-08-04 17:02:49.099[0;39m [32mDEBUG[0;39m [35m1312[0;39m [2m---[0;39m [2m[           main][0;39m [36mc.b.m.e.s.MybatisSqlSessionFactoryBean  [0;39m [2m:[0;39m Parsed mapper file: 'file [/Users/<USER>/WorkSpace/sanyth-xuegong-5/sanyth-xuegong-5-server/xggl-api/target/classes/com/sanythadmin/project/mentalHealth/mapper/xml/XljkCrisisReportApprovalNodeMapper.xml]'
[2m2025-08-04 17:02:49.122[0;39m [32mDEBUG[0;39m [35m1312[0;39m [2m---[0;39m [2m[           main][0;39m [36mc.b.m.e.s.MybatisSqlSessionFactoryBean  [0;39m [2m:[0;39m Parsed mapper file: 'file [/Users/<USER>/WorkSpace/sanyth-xuegong-5/sanyth-xuegong-5-server/xggl-api/target/classes/com/sanythadmin/project/mentalHealth/mapper/xml/XljkCrisisReportMapper.xml]'
[2m2025-08-04 17:02:49.136[0;39m [32mDEBUG[0;39m [35m1312[0;39m [2m---[0;39m [2m[           main][0;39m [36mc.b.m.e.s.MybatisSqlSessionFactoryBean  [0;39m [2m:[0;39m Parsed mapper file: 'file [/Users/<USER>/WorkSpace/sanyth-xuegong-5/sanyth-xuegong-5-server/xggl-api/target/classes/com/sanythadmin/project/mentalHealth/mapper/xml/XljkCrisisReportNodeMapper.xml]'
[2m2025-08-04 17:02:49.157[0;39m [32mDEBUG[0;39m [35m1312[0;39m [2m---[0;39m [2m[           main][0;39m [36mc.b.m.e.s.MybatisSqlSessionFactoryBean  [0;39m [2m:[0;39m Parsed mapper file: 'file [/Users/<USER>/WorkSpace/sanyth-xuegong-5/sanyth-xuegong-5-server/xggl-api/target/classes/com/sanythadmin/project/mentalHealth/mapper/xml/XljkCrisisRepositoryMapper.xml]'
[2m2025-08-04 17:02:49.176[0;39m [32mDEBUG[0;39m [35m1312[0;39m [2m---[0;39m [2m[           main][0;39m [36mc.b.m.e.s.MybatisSqlSessionFactoryBean  [0;39m [2m:[0;39m Parsed mapper file: 'file [/Users/<USER>/WorkSpace/sanyth-xuegong-5/sanyth-xuegong-5-server/xggl-api/target/classes/com/sanythadmin/project/mentalHealth/mapper/xml/XljkFollowupRecordMapper.xml]'
[2m2025-08-04 17:02:49.193[0;39m [32mDEBUG[0;39m [35m1312[0;39m [2m---[0;39m [2m[           main][0;39m [36mc.b.m.e.s.MybatisSqlSessionFactoryBean  [0;39m [2m:[0;39m Parsed mapper file: 'file [/Users/<USER>/WorkSpace/sanyth-xuegong-5/sanyth-xuegong-5-server/xggl-api/target/classes/com/sanythadmin/project/mentalHealth/mapper/xml/XljkInterpretationMapper.xml]'
[2m2025-08-04 17:02:49.208[0;39m [32mDEBUG[0;39m [35m1312[0;39m [2m---[0;39m [2m[           main][0;39m [36mc.b.m.e.s.MybatisSqlSessionFactoryBean  [0;39m [2m:[0;39m Parsed mapper file: 'file [/Users/<USER>/WorkSpace/sanyth-xuegong-5/sanyth-xuegong-5-server/xggl-api/target/classes/com/sanythadmin/project/mentalHealth/mapper/xml/XljkParamMapper.xml]'
[2m2025-08-04 17:02:49.224[0;39m [32mDEBUG[0;39m [35m1312[0;39m [2m---[0;39m [2m[           main][0;39m [36mc.b.m.e.s.MybatisSqlSessionFactoryBean  [0;39m [2m:[0;39m Parsed mapper file: 'file [/Users/<USER>/WorkSpace/sanyth-xuegong-5/sanyth-xuegong-5-server/xggl-api/target/classes/com/sanythadmin/project/mentalHealth/mapper/xml/XljkSurveyAnswerInfoMapper.xml]'
[2m2025-08-04 17:02:49.243[0;39m [32mDEBUG[0;39m [35m1312[0;39m [2m---[0;39m [2m[           main][0;39m [36mc.b.m.e.s.MybatisSqlSessionFactoryBean  [0;39m [2m:[0;39m Parsed mapper file: 'file [/Users/<USER>/WorkSpace/sanyth-xuegong-5/sanyth-xuegong-5-server/xggl-api/target/classes/com/sanythadmin/project/mentalHealth/mapper/xml/XljkSurveyAnswerMapper.xml]'
[2m2025-08-04 17:02:49.258[0;39m [32mDEBUG[0;39m [35m1312[0;39m [2m---[0;39m [2m[           main][0;39m [36mc.b.m.e.s.MybatisSqlSessionFactoryBean  [0;39m [2m:[0;39m Parsed mapper file: 'file [/Users/<USER>/WorkSpace/sanyth-xuegong-5/sanyth-xuegong-5-server/xggl-api/target/classes/com/sanythadmin/project/mentalHealth/mapper/xml/XljkSurveyInterpretationMapper.xml]'
[2m2025-08-04 17:02:49.272[0;39m [32mDEBUG[0;39m [35m1312[0;39m [2m---[0;39m [2m[           main][0;39m [36mc.b.m.e.s.MybatisSqlSessionFactoryBean  [0;39m [2m:[0;39m Parsed mapper file: 'file [/Users/<USER>/WorkSpace/sanyth-xuegong-5/sanyth-xuegong-5-server/xggl-api/target/classes/com/sanythadmin/project/mentalHealth/mapper/xml/XljkSurveyMapper.xml]'
[2m2025-08-04 17:02:49.288[0;39m [32mDEBUG[0;39m [35m1312[0;39m [2m---[0;39m [2m[           main][0;39m [36mc.b.m.e.s.MybatisSqlSessionFactoryBean  [0;39m [2m:[0;39m Parsed mapper file: 'file [/Users/<USER>/WorkSpace/sanyth-xuegong-5/sanyth-xuegong-5-server/xggl-api/target/classes/com/sanythadmin/project/mentalHealth/mapper/xml/XljkSurveyReportMapper.xml]'
[2m2025-08-04 17:02:49.309[0;39m [32mDEBUG[0;39m [35m1312[0;39m [2m---[0;39m [2m[           main][0;39m [36mc.b.m.e.s.MybatisSqlSessionFactoryBean  [0;39m [2m:[0;39m Parsed mapper file: 'file [/Users/<USER>/WorkSpace/sanyth-xuegong-5/sanyth-xuegong-5-server/xggl-api/target/classes/com/sanythadmin/project/resume/mapper/xml/ResumeDataConfigMapper.xml]'
[2m2025-08-04 17:02:49.326[0;39m [32mDEBUG[0;39m [35m1312[0;39m [2m---[0;39m [2m[           main][0;39m [36mc.b.m.e.s.MybatisSqlSessionFactoryBean  [0;39m [2m:[0;39m Parsed mapper file: 'file [/Users/<USER>/WorkSpace/sanyth-xuegong-5/sanyth-xuegong-5-server/xggl-api/target/classes/com/sanythadmin/project/resume/mapper/xml/ResumeTemplateMapper.xml]'
[2m2025-08-04 17:02:49.345[0;39m [32mDEBUG[0;39m [35m1312[0;39m [2m---[0;39m [2m[           main][0;39m [36mc.b.m.e.s.MybatisSqlSessionFactoryBean  [0;39m [2m:[0;39m Parsed mapper file: 'file [/Users/<USER>/WorkSpace/sanyth-xuegong-5/sanyth-xuegong-5-server/xggl-api/target/classes/com/sanythadmin/project/resume/mapper/xml/ResumeTemplateTypeMapper.xml]'
[2m2025-08-04 17:02:49.364[0;39m [32mDEBUG[0;39m [35m1312[0;39m [2m---[0;39m [2m[           main][0;39m [36mc.b.m.e.s.MybatisSqlSessionFactoryBean  [0;39m [2m:[0;39m Parsed mapper file: 'file [/Users/<USER>/WorkSpace/sanyth-xuegong-5/sanyth-xuegong-5-server/xggl-api/target/classes/com/sanythadmin/project/resume/mapper/xml/UserResumeMapper.xml]'
[2m2025-08-04 17:02:49.392[0;39m [32mDEBUG[0;39m [35m1312[0;39m [2m---[0;39m [2m[           main][0;39m [36mc.b.m.e.s.MybatisSqlSessionFactoryBean  [0;39m [2m:[0;39m Parsed mapper file: 'file [/Users/<USER>/WorkSpace/sanyth-xuegong-5/sanyth-xuegong-5-server/xggl-api/target/classes/com/sanythadmin/project/score/mapper/xml/CourseMarkMapper.xml]'
[2m2025-08-04 17:02:49.408[0;39m [32mDEBUG[0;39m [35m1312[0;39m [2m---[0;39m [2m[           main][0;39m [36mc.b.m.e.s.MybatisSqlSessionFactoryBean  [0;39m [2m:[0;39m Parsed mapper file: 'file [/Users/<USER>/WorkSpace/sanyth-xuegong-5/sanyth-xuegong-5-server/xggl-api/target/classes/com/sanythadmin/project/score/mapper/xml/CoursePropertiesPercentMapper.xml]'
[2m2025-08-04 17:02:49.428[0;39m [32mDEBUG[0;39m [35m1312[0;39m [2m---[0;39m [2m[           main][0;39m [36mc.b.m.e.s.MybatisSqlSessionFactoryBean  [0;39m [2m:[0;39m Parsed mapper file: 'file [/Users/<USER>/WorkSpace/sanyth-xuegong-5/sanyth-xuegong-5-server/xggl-api/target/classes/com/sanythadmin/project/score/mapper/xml/CourseScoreItemMapper.xml]'
[2m2025-08-04 17:02:49.451[0;39m [32mDEBUG[0;39m [35m1312[0;39m [2m---[0;39m [2m[           main][0;39m [36mc.b.m.e.s.MybatisSqlSessionFactoryBean  [0;39m [2m:[0;39m Parsed mapper file: 'file [/Users/<USER>/WorkSpace/sanyth-xuegong-5/sanyth-xuegong-5-server/xggl-api/target/classes/com/sanythadmin/project/score/mapper/xml/CourseScoreResultMapper.xml]'
[2m2025-08-04 17:02:49.468[0;39m [32mDEBUG[0;39m [35m1312[0;39m [2m---[0;39m [2m[           main][0;39m [36mc.b.m.e.s.MybatisSqlSessionFactoryBean  [0;39m [2m:[0;39m Parsed mapper file: 'file [/Users/<USER>/WorkSpace/sanyth-xuegong-5/sanyth-xuegong-5-server/xggl-api/target/classes/com/sanythadmin/project/score/mapper/xml/ScoreLycjMapper.xml]'
[2m2025-08-04 17:02:49.485[0;39m [32mDEBUG[0;39m [35m1312[0;39m [2m---[0;39m [2m[           main][0;39m [36mc.b.m.e.s.MybatisSqlSessionFactoryBean  [0;39m [2m:[0;39m Parsed mapper file: 'file [/Users/<USER>/WorkSpace/sanyth-xuegong-5/sanyth-xuegong-5-server/xggl-api/target/classes/com/sanythadmin/project/score/mapper/xml/ScoreMycjMapper.xml]'
[2m2025-08-04 17:02:49.503[0;39m [32mDEBUG[0;39m [35m1312[0;39m [2m---[0;39m [2m[           main][0;39m [36mc.b.m.e.s.MybatisSqlSessionFactoryBean  [0;39m [2m:[0;39m Parsed mapper file: 'file [/Users/<USER>/WorkSpace/sanyth-xuegong-5/sanyth-xuegong-5-server/xggl-api/target/classes/com/sanythadmin/project/score/mapper/xml/ScoreTycjMapper.xml]'
[2m2025-08-04 17:02:49.520[0;39m [32mDEBUG[0;39m [35m1312[0;39m [2m---[0;39m [2m[           main][0;39m [36mc.b.m.e.s.MybatisSqlSessionFactoryBean  [0;39m [2m:[0;39m Parsed mapper file: 'file [/Users/<USER>/WorkSpace/sanyth-xuegong-5/sanyth-xuegong-5-server/xggl-api/target/classes/com/sanythadmin/project/score/mapper/xml/ScoreTyhdcyjfMapper.xml]'
[2m2025-08-04 17:02:49.538[0;39m [32mDEBUG[0;39m [35m1312[0;39m [2m---[0;39m [2m[           main][0;39m [36mc.b.m.e.s.MybatisSqlSessionFactoryBean  [0;39m [2m:[0;39m Parsed mapper file: 'file [/Users/<USER>/WorkSpace/sanyth-xuegong-5/sanyth-xuegong-5-server/xggl-api/target/classes/com/sanythadmin/project/score/mapper/xml/ScoreTzjkcsMapper.xml]'
[2m2025-08-04 17:02:49.556[0;39m [32mDEBUG[0;39m [35m1312[0;39m [2m---[0;39m [2m[           main][0;39m [36mc.b.m.e.s.MybatisSqlSessionFactoryBean  [0;39m [2m:[0;39m Parsed mapper file: 'file [/Users/<USER>/WorkSpace/sanyth-xuegong-5/sanyth-xuegong-5-server/xggl-api/target/classes/com/sanythadmin/project/selector/mapper/xml/SelectorAccountMapper.xml]'
[2m2025-08-04 17:02:49.574[0;39m [32mDEBUG[0;39m [35m1312[0;39m [2m---[0;39m [2m[           main][0;39m [36mc.b.m.e.s.MybatisSqlSessionFactoryBean  [0;39m [2m:[0;39m Parsed mapper file: 'file [/Users/<USER>/WorkSpace/sanyth-xuegong-5/sanyth-xuegong-5-server/xggl-api/target/classes/com/sanythadmin/project/selector/mapper/xml/SelectorConfigMapper.xml]'
[2m2025-08-04 17:02:49.592[0;39m [32mDEBUG[0;39m [35m1312[0;39m [2m---[0;39m [2m[           main][0;39m [36mc.b.m.e.s.MybatisSqlSessionFactoryBean  [0;39m [2m:[0;39m Parsed mapper file: 'file [/Users/<USER>/WorkSpace/sanyth-xuegong-5/sanyth-xuegong-5-server/xggl-api/target/classes/com/sanythadmin/project/selector/mapper/xml/SelectorDataMapper.xml]'
[2m2025-08-04 17:02:49.610[0;39m [32mDEBUG[0;39m [35m1312[0;39m [2m---[0;39m [2m[           main][0;39m [36mc.b.m.e.s.MybatisSqlSessionFactoryBean  [0;39m [2m:[0;39m Parsed mapper file: 'file [/Users/<USER>/WorkSpace/sanyth-xuegong-5/sanyth-xuegong-5-server/xggl-api/target/classes/com/sanythadmin/project/survey/mapper/xml/SurveyAnswerInfoMapper.xml]'
[2m2025-08-04 17:02:49.625[0;39m [32mDEBUG[0;39m [35m1312[0;39m [2m---[0;39m [2m[           main][0;39m [36mc.b.m.e.s.MybatisSqlSessionFactoryBean  [0;39m [2m:[0;39m Parsed mapper file: 'file [/Users/<USER>/WorkSpace/sanyth-xuegong-5/sanyth-xuegong-5-server/xggl-api/target/classes/com/sanythadmin/project/survey/mapper/xml/SurveyAnswerMapper.xml]'
[2m2025-08-04 17:02:49.639[0;39m [32mDEBUG[0;39m [35m1312[0;39m [2m---[0;39m [2m[           main][0;39m [36mc.b.m.e.s.MybatisSqlSessionFactoryBean  [0;39m [2m:[0;39m Parsed mapper file: 'file [/Users/<USER>/WorkSpace/sanyth-xuegong-5/sanyth-xuegong-5-server/xggl-api/target/classes/com/sanythadmin/project/survey/mapper/xml/SurveyClassMapper.xml]'
[2m2025-08-04 17:02:49.664[0;39m [32mDEBUG[0;39m [35m1312[0;39m [2m---[0;39m [2m[           main][0;39m [36mc.b.m.e.s.MybatisSqlSessionFactoryBean  [0;39m [2m:[0;39m Parsed mapper file: 'file [/Users/<USER>/WorkSpace/sanyth-xuegong-5/sanyth-xuegong-5-server/xggl-api/target/classes/com/sanythadmin/project/survey/mapper/xml/SurveyMapper.xml]'
[2m2025-08-04 17:02:49.682[0;39m [32mDEBUG[0;39m [35m1312[0;39m [2m---[0;39m [2m[           main][0;39m [36mc.b.m.e.s.MybatisSqlSessionFactoryBean  [0;39m [2m:[0;39m Parsed mapper file: 'file [/Users/<USER>/WorkSpace/sanyth-xuegong-5/sanyth-xuegong-5-server/xggl-api/target/classes/com/sanythadmin/project/survey/mapper/xml/SurveyPquestionsMapper.xml]'
[2m2025-08-04 17:02:49.697[0;39m [32mDEBUG[0;39m [35m1312[0;39m [2m---[0;39m [2m[           main][0;39m [36mc.b.m.e.s.MybatisSqlSessionFactoryBean  [0;39m [2m:[0;39m Parsed mapper file: 'file [/Users/<USER>/WorkSpace/sanyth-xuegong-5/sanyth-xuegong-5-server/xggl-api/target/classes/com/sanythadmin/project/survey/mapper/xml/SurveyPquestionsOptionsMapper.xml]'
[2m2025-08-04 17:02:49.713[0;39m [32mDEBUG[0;39m [35m1312[0;39m [2m---[0;39m [2m[           main][0;39m [36mc.b.m.e.s.MybatisSqlSessionFactoryBean  [0;39m [2m:[0;39m Parsed mapper file: 'file [/Users/<USER>/WorkSpace/sanyth-xuegong-5/sanyth-xuegong-5-server/xggl-api/target/classes/com/sanythadmin/project/survey/mapper/xml/SurveyPquestionsRuleMapper.xml]'
[2m2025-08-04 17:02:49.730[0;39m [32mDEBUG[0;39m [35m1312[0;39m [2m---[0;39m [2m[           main][0;39m [36mc.b.m.e.s.MybatisSqlSessionFactoryBean  [0;39m [2m:[0;39m Parsed mapper file: 'file [/Users/<USER>/WorkSpace/sanyth-xuegong-5/sanyth-xuegong-5-server/xggl-api/target/classes/com/sanythadmin/project/survey/mapper/xml/SurveyQgroupMapper.xml]'
[2m2025-08-04 17:02:49.746[0;39m [32mDEBUG[0;39m [35m1312[0;39m [2m---[0;39m [2m[           main][0;39m [36mc.b.m.e.s.MybatisSqlSessionFactoryBean  [0;39m [2m:[0;39m Parsed mapper file: 'file [/Users/<USER>/WorkSpace/sanyth-xuegong-5/sanyth-xuegong-5-server/xggl-api/target/classes/com/sanythadmin/project/survey/mapper/xml/SurveyQuestionsClassMapper.xml]'
[2m2025-08-04 17:02:49.766[0;39m [32mDEBUG[0;39m [35m1312[0;39m [2m---[0;39m [2m[           main][0;39m [36mc.b.m.e.s.MybatisSqlSessionFactoryBean  [0;39m [2m:[0;39m Parsed mapper file: 'file [/Users/<USER>/WorkSpace/sanyth-xuegong-5/sanyth-xuegong-5-server/xggl-api/target/classes/com/sanythadmin/project/survey/mapper/xml/SurveyQuestionsMapper.xml]'
[2m2025-08-04 17:02:49.781[0;39m [32mDEBUG[0;39m [35m1312[0;39m [2m---[0;39m [2m[           main][0;39m [36mc.b.m.e.s.MybatisSqlSessionFactoryBean  [0;39m [2m:[0;39m Parsed mapper file: 'file [/Users/<USER>/WorkSpace/sanyth-xuegong-5/sanyth-xuegong-5-server/xggl-api/target/classes/com/sanythadmin/project/survey/mapper/xml/SurveyQuestionsOptionsMapper.xml]'
[2m2025-08-04 17:02:49.803[0;39m [32mDEBUG[0;39m [35m1312[0;39m [2m---[0;39m [2m[           main][0;39m [36mc.b.m.e.s.MybatisSqlSessionFactoryBean  [0;39m [2m:[0;39m Parsed mapper file: 'file [/Users/<USER>/WorkSpace/sanyth-xuegong-5/sanyth-xuegong-5-server/xggl-api/target/classes/com/sanythadmin/project/teacherEvaluate/mapper/xml/FdycpAnswerInfoMapper.xml]'
[2m2025-08-04 17:02:49.823[0;39m [32mDEBUG[0;39m [35m1312[0;39m [2m---[0;39m [2m[           main][0;39m [36mc.b.m.e.s.MybatisSqlSessionFactoryBean  [0;39m [2m:[0;39m Parsed mapper file: 'file [/Users/<USER>/WorkSpace/sanyth-xuegong-5/sanyth-xuegong-5-server/xggl-api/target/classes/com/sanythadmin/project/teacherEvaluate/mapper/xml/FdycpAnswerMapper.xml]'
[2m2025-08-04 17:02:49.837[0;39m [32mDEBUG[0;39m [35m1312[0;39m [2m---[0;39m [2m[           main][0;39m [36mc.b.m.e.s.MybatisSqlSessionFactoryBean  [0;39m [2m:[0;39m Parsed mapper file: 'file [/Users/<USER>/WorkSpace/sanyth-xuegong-5/sanyth-xuegong-5-server/xggl-api/target/classes/com/sanythadmin/project/teacherEvaluate/mapper/xml/FdycpGradeMapper.xml]'
[2m2025-08-04 17:02:49.852[0;39m [32mDEBUG[0;39m [35m1312[0;39m [2m---[0;39m [2m[           main][0;39m [36mc.b.m.e.s.MybatisSqlSessionFactoryBean  [0;39m [2m:[0;39m Parsed mapper file: 'file [/Users/<USER>/WorkSpace/sanyth-xuegong-5/sanyth-xuegong-5-server/xggl-api/target/classes/com/sanythadmin/project/teacherEvaluate/mapper/xml/FdycpItemMapper.xml]'
[2m2025-08-04 17:02:49.866[0;39m [32mDEBUG[0;39m [35m1312[0;39m [2m---[0;39m [2m[           main][0;39m [36mc.b.m.e.s.MybatisSqlSessionFactoryBean  [0;39m [2m:[0;39m Parsed mapper file: 'file [/Users/<USER>/WorkSpace/sanyth-xuegong-5/sanyth-xuegong-5-server/xggl-api/target/classes/com/sanythadmin/project/teacherEvaluate/mapper/xml/FdycpItemStaffMapper.xml]'
[2m2025-08-04 17:02:49.880[0;39m [32mDEBUG[0;39m [35m1312[0;39m [2m---[0;39m [2m[           main][0;39m [36mc.b.m.e.s.MybatisSqlSessionFactoryBean  [0;39m [2m:[0;39m Parsed mapper file: 'file [/Users/<USER>/WorkSpace/sanyth-xuegong-5/sanyth-xuegong-5-server/xggl-api/target/classes/com/sanythadmin/project/teacherEvaluate/mapper/xml/FdycpResultMapper.xml]'
[2m2025-08-04 17:02:49.896[0;39m [32mDEBUG[0;39m [35m1312[0;39m [2m---[0;39m [2m[           main][0;39m [36mc.b.m.e.s.MybatisSqlSessionFactoryBean  [0;39m [2m:[0;39m Parsed mapper file: 'file [/Users/<USER>/WorkSpace/sanyth-xuegong-5/sanyth-xuegong-5-server/xggl-api/target/classes/com/sanythadmin/project/teacherEvaluate/mapper/xml/FdycpSurveyItemMapper.xml]'
[2m2025-08-04 17:02:49.910[0;39m [32mDEBUG[0;39m [35m1312[0;39m [2m---[0;39m [2m[           main][0;39m [36mc.b.m.e.s.MybatisSqlSessionFactoryBean  [0;39m [2m:[0;39m Parsed mapper file: 'file [/Users/<USER>/WorkSpace/sanyth-xuegong-5/sanyth-xuegong-5-server/xggl-api/target/classes/com/sanythadmin/project/teacherEvaluate/mapper/xml/FdycpSurveyItemStaffMapper.xml]'
[2m2025-08-04 17:02:49.938[0;39m [32mDEBUG[0;39m [35m1312[0;39m [2m---[0;39m [2m[           main][0;39m [36mc.b.m.e.s.MybatisSqlSessionFactoryBean  [0;39m [2m:[0;39m Parsed mapper file: 'file [/Users/<USER>/WorkSpace/sanyth-xuegong-5/sanyth-xuegong-5-server/xggl-api/target/classes/com/sanythadmin/project/teacherWork/mapper/xml/FdyWorkItemFieldMapper.xml]'
[2m2025-08-04 17:02:49.954[0;39m [32mDEBUG[0;39m [35m1312[0;39m [2m---[0;39m [2m[           main][0;39m [36mc.b.m.e.s.MybatisSqlSessionFactoryBean  [0;39m [2m:[0;39m Parsed mapper file: 'file [/Users/<USER>/WorkSpace/sanyth-xuegong-5/sanyth-xuegong-5-server/xggl-api/target/classes/com/sanythadmin/project/teacherWork/mapper/xml/FdyWorkItemMapper.xml]'
[2m2025-08-04 17:02:49.976[0;39m [32mDEBUG[0;39m [35m1312[0;39m [2m---[0;39m [2m[           main][0;39m [36mc.b.m.e.s.MybatisSqlSessionFactoryBean  [0;39m [2m:[0;39m Parsed mapper file: 'file [/Users/<USER>/WorkSpace/sanyth-xuegong-5/sanyth-xuegong-5-server/xggl-api/target/classes/com/sanythadmin/project/teacherWork/mapper/xml/FdyWorkItemRecordMapper.xml]'
[2m2025-08-04 17:02:49.995[0;39m [32mDEBUG[0;39m [35m1312[0;39m [2m---[0;39m [2m[           main][0;39m [36mc.b.m.e.s.MybatisSqlSessionFactoryBean  [0;39m [2m:[0;39m Parsed mapper file: 'file [/Users/<USER>/WorkSpace/sanyth-xuegong-5/sanyth-xuegong-5-server/xggl-api/target/classes/com/sanythadmin/project/userInfo/mapper/xml/QuickSearchMapper.xml]'
[2m2025-08-04 17:02:50.016[0;39m [32mDEBUG[0;39m [35m1312[0;39m [2m---[0;39m [2m[           main][0;39m [36mc.b.m.e.s.MybatisSqlSessionFactoryBean  [0;39m [2m:[0;39m Parsed mapper file: 'file [/Users/<USER>/WorkSpace/sanyth-xuegong-5/sanyth-xuegong-5-server/xggl-api/target/classes/com/sanythadmin/project/userInfo/mapper/xml/StudentRecordChangeMapper.xml]'
[2m2025-08-04 17:02:50.032[0;39m [32mDEBUG[0;39m [35m1312[0;39m [2m---[0;39m [2m[           main][0;39m [36mc.b.m.e.s.MybatisSqlSessionFactoryBean  [0;39m [2m:[0;39m Parsed mapper file: 'file [/Users/<USER>/WorkSpace/sanyth-xuegong-5/sanyth-xuegong-5-server/xggl-api/target/classes/com/sanythadmin/project/userInfo/mapper/xml/UserDataScopeMapper.xml]'
[2m2025-08-04 17:02:50.050[0;39m [32mDEBUG[0;39m [35m1312[0;39m [2m---[0;39m [2m[           main][0;39m [36mc.b.m.e.s.MybatisSqlSessionFactoryBean  [0;39m [2m:[0;39m Parsed mapper file: 'file [/Users/<USER>/WorkSpace/sanyth-xuegong-5/sanyth-xuegong-5-server/xggl-api/target/classes/com/sanythadmin/project/userInfo/mapper/xml/UserListInfoMapper.xml]'
[2m2025-08-04 17:02:50.069[0;39m [32mDEBUG[0;39m [35m1312[0;39m [2m---[0;39m [2m[           main][0;39m [36mc.b.m.e.s.MybatisSqlSessionFactoryBean  [0;39m [2m:[0;39m Parsed mapper file: 'file [/Users/<USER>/WorkSpace/sanyth-xuegong-5/sanyth-xuegong-5-server/xggl-api/target/classes/com/sanythadmin/project/workflow/mapper/xml/WorkflowApprovalNodeMapper.xml]'
[2m2025-08-04 17:02:50.085[0;39m [32mDEBUG[0;39m [35m1312[0;39m [2m---[0;39m [2m[           main][0;39m [36mc.b.m.e.s.MybatisSqlSessionFactoryBean  [0;39m [2m:[0;39m Parsed mapper file: 'file [/Users/<USER>/WorkSpace/sanyth-xuegong-5/sanyth-xuegong-5-server/xggl-api/target/classes/com/sanythadmin/project/workflow/mapper/xml/WorkflowApprovalNodeRecordMapper.xml]'
[2m2025-08-04 17:02:50.103[0;39m [32mDEBUG[0;39m [35m1312[0;39m [2m---[0;39m [2m[           main][0;39m [36mc.b.m.e.s.MybatisSqlSessionFactoryBean  [0;39m [2m:[0;39m Parsed mapper file: 'file [/Users/<USER>/WorkSpace/sanyth-xuegong-5/sanyth-xuegong-5-server/xggl-api/target/classes/com/sanythadmin/project/workflow/mapper/xml/WorkflowConditionDetailMapper.xml]'
[2m2025-08-04 17:02:50.117[0;39m [32mDEBUG[0;39m [35m1312[0;39m [2m---[0;39m [2m[           main][0;39m [36mc.b.m.e.s.MybatisSqlSessionFactoryBean  [0;39m [2m:[0;39m Parsed mapper file: 'file [/Users/<USER>/WorkSpace/sanyth-xuegong-5/sanyth-xuegong-5-server/xggl-api/target/classes/com/sanythadmin/project/workflow/mapper/xml/WorkflowConditionMapper.xml]'
[2m2025-08-04 17:02:50.135[0;39m [32mDEBUG[0;39m [35m1312[0;39m [2m---[0;39m [2m[           main][0;39m [36mc.b.m.e.s.MybatisSqlSessionFactoryBean  [0;39m [2m:[0;39m Parsed mapper file: 'file [/Users/<USER>/WorkSpace/sanyth-xuegong-5/sanyth-xuegong-5-server/xggl-api/target/classes/com/sanythadmin/project/workflow/mapper/xml/WorkflowEventMapper.xml]'
[2m2025-08-04 17:02:50.150[0;39m [32mDEBUG[0;39m [35m1312[0;39m [2m---[0;39m [2m[           main][0;39m [36mc.b.m.e.s.MybatisSqlSessionFactoryBean  [0;39m [2m:[0;39m Parsed mapper file: 'file [/Users/<USER>/WorkSpace/sanyth-xuegong-5/sanyth-xuegong-5-server/xggl-api/target/classes/com/sanythadmin/project/workflow/mapper/xml/WorkflowMapper.xml]'
[2m2025-08-04 17:02:50.169[0;39m [32mDEBUG[0;39m [35m1312[0;39m [2m---[0;39m [2m[           main][0;39m [36mc.b.m.e.s.MybatisSqlSessionFactoryBean  [0;39m [2m:[0;39m Parsed mapper file: 'file [/Users/<USER>/WorkSpace/sanyth-xuegong-5/sanyth-xuegong-5-server/xggl-api/target/classes/com/sanythadmin/project/workflow/mapper/xml/WorkflowNodeApproverMapper.xml]'
[2m2025-08-04 17:02:50.187[0;39m [32mDEBUG[0;39m [35m1312[0;39m [2m---[0;39m [2m[           main][0;39m [36mc.b.m.e.s.MybatisSqlSessionFactoryBean  [0;39m [2m:[0;39m Parsed mapper file: 'file [/Users/<USER>/WorkSpace/sanyth-xuegong-5/sanyth-xuegong-5-server/xggl-api/target/classes/com/sanythadmin/project/workflow/mapper/xml/WorkflowNodeFormMapper.xml]'
[2m2025-08-04 17:02:50.214[0;39m [32mDEBUG[0;39m [35m1312[0;39m [2m---[0;39m [2m[           main][0;39m [36mc.b.m.e.s.MybatisSqlSessionFactoryBean  [0;39m [2m:[0;39m Parsed mapper file: 'file [/Users/<USER>/WorkSpace/sanyth-xuegong-5/sanyth-xuegong-5-server/xggl-api/target/classes/com/sanythadmin/project/workflow/mapper/xml/WorkflowNodeMapper.xml]'
[2m2025-08-04 17:02:50.247[0;39m [32mDEBUG[0;39m [35m1312[0;39m [2m---[0;39m [2m[           main][0;39m [36mc.b.m.e.s.MybatisSqlSessionFactoryBean  [0;39m [2m:[0;39m Parsed mapper file: 'file [/Users/<USER>/WorkSpace/sanyth-xuegong-5/sanyth-xuegong-5-server/xggl-api/target/classes/com/sanythadmin/project/workflow/mapper/xml/WorkflowNodeStateMapper.xml]'
[2m2025-08-04 17:02:50.270[0;39m [32mDEBUG[0;39m [35m1312[0;39m [2m---[0;39m [2m[           main][0;39m [36mc.b.m.e.s.MybatisSqlSessionFactoryBean  [0;39m [2m:[0;39m Parsed mapper file: 'file [/Users/<USER>/WorkSpace/sanyth-xuegong-5/sanyth-xuegong-5-server/xggl-api/target/classes/com/sanythadmin/project/workstudy/mapper/xml/QgzxEmployerMapper.xml]'
[2m2025-08-04 17:02:50.293[0;39m [32mDEBUG[0;39m [35m1312[0;39m [2m---[0;39m [2m[           main][0;39m [36mc.b.m.e.s.MybatisSqlSessionFactoryBean  [0;39m [2m:[0;39m Parsed mapper file: 'file [/Users/<USER>/WorkSpace/sanyth-xuegong-5/sanyth-xuegong-5-server/xggl-api/target/classes/com/sanythadmin/project/workstudy/mapper/xml/QgzxInterviewRecordMapper.xml]'
[2m2025-08-04 17:02:50.308[0;39m [32mDEBUG[0;39m [35m1312[0;39m [2m---[0;39m [2m[           main][0;39m [36mc.b.m.e.s.MybatisSqlSessionFactoryBean  [0;39m [2m:[0;39m Parsed mapper file: 'file [/Users/<USER>/WorkSpace/sanyth-xuegong-5/sanyth-xuegong-5-server/xggl-api/target/classes/com/sanythadmin/project/workstudy/mapper/xml/QgzxJobTypeMapper.xml]'
[2m2025-08-04 17:02:50.324[0;39m [32mDEBUG[0;39m [35m1312[0;39m [2m---[0;39m [2m[           main][0;39m [36mc.b.m.e.s.MybatisSqlSessionFactoryBean  [0;39m [2m:[0;39m Parsed mapper file: 'file [/Users/<USER>/WorkSpace/sanyth-xuegong-5/sanyth-xuegong-5-server/xggl-api/target/classes/com/sanythadmin/project/workstudy/mapper/xml/QgzxKsbMapper.xml]'
[2m2025-08-04 17:02:50.340[0;39m [32mDEBUG[0;39m [35m1312[0;39m [2m---[0;39m [2m[           main][0;39m [36mc.b.m.e.s.MybatisSqlSessionFactoryBean  [0;39m [2m:[0;39m Parsed mapper file: 'file [/Users/<USER>/WorkSpace/sanyth-xuegong-5/sanyth-xuegong-5-server/xggl-api/target/classes/com/sanythadmin/project/workstudy/mapper/xml/QgzxStudentsClassTimeMapper.xml]'
[2m2025-08-04 17:02:50.351[0;39m [32mDEBUG[0;39m [35m1312[0;39m [2m---[0;39m [2m[           main][0;39m [36mc.b.mybatisplus.core.toolkit.Sequence   [0;39m [2m:[0;39m Initialization Sequence datacenterId:0 workerId:19
[2m2025-08-04 17:02:50.692[0;39m [32m INFO[0;39m [35m1312[0;39m [2m---[0;39m [2m[           main][0;39m [36morg.redisson.Version                    [0;39m [2m:[0;39m Redisson 3.36.0
[2m2025-08-04 17:02:50.775[0;39m [31mERROR[0;39m [35m1312[0;39m [2m---[0;39m [2m[           main][0;39m [36mi.n.r.d.DnsServerAddressStreamProviders [0;39m [2m:[0;39m Unable to load io.netty.resolver.dns.macos.MacOSDnsServerAddressStreamProvider, fallback to system defaults. This may result in incorrect DNS resolutions on MacOS. Check whether you have a dependency on 'io.netty:netty-resolver-dns-native-macos'. Use DEBUG level to see the full stack: java.lang.UnsatisfiedLinkError: failed to load the required native library
[2m2025-08-04 17:02:51.168[0;39m [32m INFO[0;39m [35m1312[0;39m [2m---[0;39m [2m[isson-netty-1-6][0;39m [36mo.redisson.connection.ConnectionsHolder [0;39m [2m:[0;39m 1 connections initialized for *************/*************:6380
[2m2025-08-04 17:02:58.832[0;39m [32m INFO[0;39m [35m1312[0;39m [2m---[0;39m [2m[sson-netty-1-19][0;39m [36mo.redisson.connection.ConnectionsHolder [0;39m [2m:[0;39m 24 connections initialized for *************/*************:6380
[2m2025-08-04 17:02:59.824[0;39m [32mDEBUG[0;39m [35m1312[0;39m [2m---[0;39m [2m[           main][0;39m [36mc.s.c.c.s.JwtAuthenticationFilter       [0;39m [2m:[0;39m Filter 'jwtAuthenticationFilter' configured for use
[2m2025-08-04 17:03:00.988[0;39m [32m INFO[0;39m [35m1312[0;39m [2m---[0;39m [2m[           main][0;39m [36mo.hibernate.jpa.internal.util.LogHelper [0;39m [2m:[0;39m HHH000204: Processing PersistenceUnitInfo [name: default]
[2m2025-08-04 17:03:01.142[0;39m [32m INFO[0;39m [35m1312[0;39m [2m---[0;39m [2m[           main][0;39m [36morg.hibernate.Version                   [0;39m [2m:[0;39m HHH000412: Hibernate ORM core version 6.6.18.Final
[2m2025-08-04 17:03:01.397[0;39m [32m INFO[0;39m [35m1312[0;39m [2m---[0;39m [2m[           main][0;39m [36mo.h.c.internal.RegionFactoryInitiator   [0;39m [2m:[0;39m HHH000026: Second-level cache disabled
[2m2025-08-04 17:03:01.941[0;39m [32m INFO[0;39m [35m1312[0;39m [2m---[0;39m [2m[           main][0;39m [36mo.s.o.j.p.SpringPersistenceUnitInfo     [0;39m [2m:[0;39m No LoadTimeWeaver setup: ignoring JPA class transformer
[2m2025-08-04 17:03:02.410[0;39m [32m INFO[0;39m [35m1312[0;39m [2m---[0;39m [2m[           main][0;39m [36mcom.alibaba.druid.pool.DruidDataSource  [0;39m [2m:[0;39m {dataSource-1} inited
[2m2025-08-04 17:03:06.919[0;39m [33m WARN[0;39m [35m1312[0;39m [2m---[0;39m [2m[           main][0;39m [36morg.hibernate.orm.deprecation           [0;39m [2m:[0;39m HHH90000025: OracleDialect does not need to be specified explicitly using 'hibernate.dialect' (remove the property setting and it will be selected by default)
[2m2025-08-04 17:03:08.455[0;39m [32m INFO[0;39m [35m1312[0;39m [2m---[0;39m [2m[           main][0;39m [36morg.hibernate.orm.connections.pooling   [0;39m [2m:[0;39m HHH10001005: Database info:
	Database JDBC URL [Connecting through datasource '{
	CreateTime:"2025-08-04 17:02:46",
	ActiveCount:0,
	PoolingCount:1,
	CreateCount:1,
	DestroyCount:0,
	CloseCount:1,
	ConnectCount:1,
	Connections:[
		{ID:1642433157, ConnectTime:"2025-08-04 17:03:06", UseCount:1, LastActiveTime:"2025-08-04 17:03:08"}
	]
}']
	Database driver: undefined/unknown
	Database version: 19.3
	Autocommit mode: undefined/unknown
	Isolation level: undefined/unknown
	Minimum pool size: undefined/unknown
	Maximum pool size: undefined/unknown
[2m2025-08-04 17:03:15.979[0;39m [32m INFO[0;39m [35m1312[0;39m [2m---[0;39m [2m[           main][0;39m [36mo.h.e.t.j.p.i.JtaPlatformInitiator      [0;39m [2m:[0;39m HHH000489: No JTA platform available (set 'hibernate.transaction.jta.platform' to enable JTA platform integration)
[2m2025-08-04 17:03:15.995[0;39m [32m INFO[0;39m [35m1312[0;39m [2m---[0;39m [2m[           main][0;39m [36mj.LocalContainerEntityManagerFactoryBean[0;39m [2m:[0;39m Initialized JPA EntityManagerFactory for persistence unit 'default'
[2m2025-08-04 17:03:16.705[0;39m [32m INFO[0;39m [35m1312[0;39m [2m---[0;39m [2m[           main][0;39m [36mo.s.d.j.r.query.QueryEnhancerFactory    [0;39m [2m:[0;39m JSqlParser is in classpath; If applicable, JSqlParser will be used.
[2m2025-08-04 17:03:16.705[0;39m [32m INFO[0;39m [35m1312[0;39m [2m---[0;39m [2m[           main][0;39m [36mo.s.d.j.r.query.QueryEnhancerFactory    [0;39m [2m:[0;39m Hibernate is in classpath; If applicable, HQL parser will be used.
[2m2025-08-04 17:03:27.419[0;39m [33m WARN[0;39m [35m1312[0;39m [2m---[0;39m [2m[           main][0;39m [36m.s.s.UserDetailsServiceAutoConfiguration[0;39m [2m:[0;39m 

Using generated security password: 90b34f6e-65aa-4b28-b317-cca7defb18bb

This generated password is for development use only. Your security configuration must be updated before running your application in production.

[2m2025-08-04 17:03:27.434[0;39m [32m INFO[0;39m [35m1312[0;39m [2m---[0;39m [2m[           main][0;39m [36mr$InitializeUserDetailsManagerConfigurer[0;39m [2m:[0;39m Global AuthenticationManager configured with UserDetailsService bean with name inMemoryUserDetailsManager
[2m2025-08-04 17:03:28.050[0;39m [32m INFO[0;39m [35m1312[0;39m [2m---[0;39m [2m[           main][0;39m [36mctiveUserDetailsServiceAutoConfiguration[0;39m [2m:[0;39m 

Using generated security password: 90b34f6e-65aa-4b28-b317-cca7defb18bb

[2m2025-08-04 17:03:28.343[0;39m [32m INFO[0;39m [35m1312[0;39m [2m---[0;39m [2m[           main][0;39m [36mo.a.coyote.http11.Http11NioProtocol     [0;39m [2m:[0;39m Starting ProtocolHandler ["http-nio-8082"]
[2m2025-08-04 17:03:28.392[0;39m [32m INFO[0;39m [35m1312[0;39m [2m---[0;39m [2m[           main][0;39m [36mo.s.b.w.embedded.tomcat.TomcatWebServer [0;39m [2m:[0;39m Tomcat started on port 8082 (http) with context path '/'
[2m2025-08-04 17:03:28.415[0;39m [32m INFO[0;39m [35m1312[0;39m [2m---[0;39m [2m[           main][0;39m [36mcom.sanythadmin.AdminApplication        [0;39m [2m:[0;39m Started AdminApplication in 46.984 seconds (process running for 48.985)
[2m2025-08-04 17:03:28.420[0;39m [32mDEBUG[0;39m [35m1312[0;39m [2m---[0;39m [2m[           main][0;39m [36mc.b.m.a.DdlApplicationRunner            [0;39m [2m:[0;39m   ...  DDL start create  ...  
[2m2025-08-04 17:03:28.421[0;39m [32mDEBUG[0;39m [35m1312[0;39m [2m---[0;39m [2m[           main][0;39m [36mc.b.m.a.DdlApplicationRunner            [0;39m [2m:[0;39m   ...  DDL end create  ...  
[2m2025-08-04 17:03:33.760[0;39m [32mDEBUG[0;39m [35m1312[0;39m [2m---[0;39m [2m[           main][0;39m [36m.c.c.m.i.CustomDataPermissionInterceptor[0;39m [2m:[0;39m original SQL: SELECT COUNT( * ) AS total FROM  SYT_SYS_JWT
[2m2025-08-04 17:03:33.809[0;39m [32mDEBUG[0;39m [35m1312[0;39m [2m---[0;39m [2m[           main][0;39m [36m.c.c.m.i.CustomDataPermissionInterceptor[0;39m [2m:[0;39m SQL to parse, SQL: SELECT COUNT( * ) AS total FROM  SYT_SYS_JWT
[2m2025-08-04 17:03:33.813[0;39m [32mDEBUG[0;39m [35m1312[0;39m [2m---[0;39m [2m[           main][0;39m [36m.c.c.m.i.CustomDataPermissionInterceptor[0;39m [2m:[0;39m parse the finished SQL: SELECT COUNT(*) AS total FROM SYT_SYS_JWT
[2m2025-08-04 17:03:33.829[0;39m [32mDEBUG[0;39m [35m1312[0;39m [2m---[0;39m [2m[           main][0;39m [36mc.s.c.s.mapper.SysJwtMapper.selectCount [0;39m [2m:[0;39m ==>  Preparing: SELECT COUNT(*) AS total FROM SYT_SYS_JWT
[2m2025-08-04 17:03:33.873[0;39m [32mDEBUG[0;39m [35m1312[0;39m [2m---[0;39m [2m[           main][0;39m [36mc.s.c.s.mapper.SysJwtMapper.selectCount [0;39m [2m:[0;39m ==> Parameters: 
[2m2025-08-04 17:03:33.963[0;39m [32mDEBUG[0;39m [35m1312[0;39m [2m---[0;39m [2m[           main][0;39m [36mc.s.c.s.mapper.SysJwtMapper.selectCount [0;39m [2m:[0;39m <==      Total: 1
[2m2025-08-04 17:03:55.510[0;39m [32m INFO[0;39m [35m1312[0;39m [2m---[0;39m [2m[nio-8082-exec-1][0;39m [36mo.a.c.c.C.[Tomcat].[localhost].[/]      [0;39m [2m:[0;39m Initializing Spring DispatcherServlet 'dispatcherServlet'
[2m2025-08-04 17:03:55.513[0;39m [32m INFO[0;39m [35m1312[0;39m [2m---[0;39m [2m[nio-8082-exec-1][0;39m [36mo.s.web.servlet.DispatcherServlet       [0;39m [2m:[0;39m Initializing Servlet 'dispatcherServlet'
[2m2025-08-04 17:03:55.536[0;39m [32m INFO[0;39m [35m1312[0;39m [2m---[0;39m [2m[nio-8082-exec-1][0;39m [36mo.s.web.servlet.DispatcherServlet       [0;39m [2m:[0;39m Completed initialization in 23 ms
[2m2025-08-04 17:03:55.703[0;39m [32mDEBUG[0;39m [35m1312[0;39m [2m---[0;39m [2m[nio-8082-exec-1][0;39m [36m.c.c.m.i.CustomDataPermissionInterceptor[0;39m [2m:[0;39m original SQL: SELECT    ID,PARAM_NAME,PARAM_VALUE,PARAM_MODE,DESCRIPTION,ATTACHMENT,create_time    FROM  SYT_SYS_PARAM         WHERE  (param_mode = ?) ORDER BY create_time DESC
[2m2025-08-04 17:03:55.713[0;39m [32mDEBUG[0;39m [35m1312[0;39m [2m---[0;39m [2m[nio-8082-exec-1][0;39m [36m.c.c.m.i.CustomDataPermissionInterceptor[0;39m [2m:[0;39m SQL to parse, SQL: SELECT    ID,PARAM_NAME,PARAM_VALUE,PARAM_MODE,DESCRIPTION,ATTACHMENT,create_time    FROM  SYT_SYS_PARAM         WHERE  (param_mode = ?) ORDER BY create_time DESC
[2m2025-08-04 17:03:55.716[0;39m [32mDEBUG[0;39m [35m1312[0;39m [2m---[0;39m [2m[nio-8082-exec-1][0;39m [36m.c.c.m.i.CustomDataPermissionInterceptor[0;39m [2m:[0;39m parse the finished SQL: SELECT ID, PARAM_NAME, PARAM_VALUE, PARAM_MODE, DESCRIPTION, ATTACHMENT, create_time FROM SYT_SYS_PARAM WHERE (param_mode = ?) ORDER BY create_time DESC
[2m2025-08-04 17:03:55.718[0;39m [32mDEBUG[0;39m [35m1312[0;39m [2m---[0;39m [2m[nio-8082-exec-1][0;39m [36mc.s.c.s.m.SysParamMapper.selectList     [0;39m [2m:[0;39m ==>  Preparing: SELECT ID, PARAM_NAME, PARAM_VALUE, PARAM_MODE, DESCRIPTION, ATTACHMENT, create_time FROM SYT_SYS_PARAM WHERE (param_mode = ?) ORDER BY create_time DESC
[2m2025-08-04 17:03:55.720[0;39m [32mDEBUG[0;39m [35m1312[0;39m [2m---[0;39m [2m[nio-8082-exec-1][0;39m [36mc.s.c.s.m.SysParamMapper.selectList     [0;39m [2m:[0;39m ==> Parameters: system-info(String)
[2m2025-08-04 17:03:55.816[0;39m [32mDEBUG[0;39m [35m1312[0;39m [2m---[0;39m [2m[nio-8082-exec-1][0;39m [36mc.s.c.s.m.SysParamMapper.selectList     [0;39m [2m:[0;39m <==      Total: 0
[2m2025-08-04 17:03:57.301[0;39m [32mDEBUG[0;39m [35m1312[0;39m [2m---[0;39m [2m[nio-8082-exec-5][0;39m [36m.c.c.m.i.CustomDataPermissionInterceptor[0;39m [2m:[0;39m original SQL: SELECT     t.XGH,t.BZ1,t.BZ2,t.BZ3,t.BZ4,t.BZ5,t.BZ6,t.BZ7,t.BZ8,t.BZ9,t.BZ10,t.BZ11,t.BZ12,t.BZ13,t.BZ14,t.BZ15,t.BZ16,t.BZ17,t.BZ18,t.BZ19,t.BZ20,t.BZ21,t.BZ22,t.BZ23,t.BZ24,t.BZ25,t.BZ26,t.BZ27,t.BZ28,t.BZ29,t.BZ30,t.BZ31,t.BZ32,t.BZ33,t.BZ34,t.BZ35,t.BZ36,t.BZ37,t.BZ38,t.BZ39,t.BZ40,t.BZ41,t.BZ42,t.BZ43,t.BZ44,t.BZ45,t.BZ46,t.BZ47,t.BZ48,t.BZ49,t.BZ50,t.BZ51,t.BZ52,t.BZ53,t.BZ54,t.BZ55,t.BZ56,t.BZ57,t.BZ58,t.BZ59,t.BZ60,t.BZ61,t.BZ62,t.BZ63,t.BZ64,t.BZ65,t.BZ66,t.BZ67,t.BZ68,t.BZ69,t.BZ70,t.BZ71,t.BZ72,t.BZ73,t.BZ74,t.BZ75,t.BZ76,t.BZ77,t.BZ78,t.BZ79,t.BZ80,t.BZ81,t.BZ82,t.BZ83,t.BZ84,t.BZ85,t.BZ86,t.BZ87,t.BZ88,t.BZ89,t.BZ90,t.BZ91,t.BZ92,t.BZ93,t.BZ94,t.BZ95,t.BZ96,t.BZ97,t.BZ98,t.BZ99,t.BZ100,t.XM,t.XB,t.SJH,t.CSRQ,t.JG,t.MZMC,t.ZZMMMC,t.XQMC,t.ZJHM,t.ZJLX,t.XSLB,t.XZLX,t.RYZTID,t.USER_TYPE,t.PHOTO,t2.ID,t2.PYCCID,t2.NJID,t2.XYID,t2.ZYID,t2.BJID,t2.XGH AS joina_XGH   FROM SYT_USER_INFO  t    LEFT JOIN SYT_CODE_XSZT t1 ON (t1.ID = t.RYZTID) LEFT JOIN SYT_USER_ORG_MAP t2 ON (t2.XGH = t.XGH)     WHERE   (t.xgh IN (?) AND t2.xgh IN (?)) ORDER BY t.xgh DESC
[2m2025-08-04 17:03:57.334[0;39m [32mDEBUG[0;39m [35m1312[0;39m [2m---[0;39m [2m[nio-8082-exec-5][0;39m [36m.c.c.m.i.CustomDataPermissionInterceptor[0;39m [2m:[0;39m SQL to parse, SQL: SELECT     t.XGH,t.BZ1,t.BZ2,t.BZ3,t.BZ4,t.BZ5,t.BZ6,t.BZ7,t.BZ8,t.BZ9,t.BZ10,t.BZ11,t.BZ12,t.BZ13,t.BZ14,t.BZ15,t.BZ16,t.BZ17,t.BZ18,t.BZ19,t.BZ20,t.BZ21,t.BZ22,t.BZ23,t.BZ24,t.BZ25,t.BZ26,t.BZ27,t.BZ28,t.BZ29,t.BZ30,t.BZ31,t.BZ32,t.BZ33,t.BZ34,t.BZ35,t.BZ36,t.BZ37,t.BZ38,t.BZ39,t.BZ40,t.BZ41,t.BZ42,t.BZ43,t.BZ44,t.BZ45,t.BZ46,t.BZ47,t.BZ48,t.BZ49,t.BZ50,t.BZ51,t.BZ52,t.BZ53,t.BZ54,t.BZ55,t.BZ56,t.BZ57,t.BZ58,t.BZ59,t.BZ60,t.BZ61,t.BZ62,t.BZ63,t.BZ64,t.BZ65,t.BZ66,t.BZ67,t.BZ68,t.BZ69,t.BZ70,t.BZ71,t.BZ72,t.BZ73,t.BZ74,t.BZ75,t.BZ76,t.BZ77,t.BZ78,t.BZ79,t.BZ80,t.BZ81,t.BZ82,t.BZ83,t.BZ84,t.BZ85,t.BZ86,t.BZ87,t.BZ88,t.BZ89,t.BZ90,t.BZ91,t.BZ92,t.BZ93,t.BZ94,t.BZ95,t.BZ96,t.BZ97,t.BZ98,t.BZ99,t.BZ100,t.XM,t.XB,t.SJH,t.CSRQ,t.JG,t.MZMC,t.ZZMMMC,t.XQMC,t.ZJHM,t.ZJLX,t.XSLB,t.XZLX,t.RYZTID,t.USER_TYPE,t.PHOTO,t2.ID,t2.PYCCID,t2.NJID,t2.XYID,t2.ZYID,t2.BJID,t2.XGH AS joina_XGH   FROM SYT_USER_INFO  t    LEFT JOIN SYT_CODE_XSZT t1 ON (t1.ID = t.RYZTID) LEFT JOIN SYT_USER_ORG_MAP t2 ON (t2.XGH = t.XGH)     WHERE   (t.xgh IN (?) AND t2.xgh IN (?)) ORDER BY t.xgh DESC
[2m2025-08-04 17:03:57.338[0;39m [32mDEBUG[0;39m [35m1312[0;39m [2m---[0;39m [2m[nio-8082-exec-5][0;39m [36m.c.c.m.i.CustomDataPermissionInterceptor[0;39m [2m:[0;39m parse the finished SQL: SELECT t.XGH, t.BZ1, t.BZ2, t.BZ3, t.BZ4, t.BZ5, t.BZ6, t.BZ7, t.BZ8, t.BZ9, t.BZ10, t.BZ11, t.BZ12, t.BZ13, t.BZ14, t.BZ15, t.BZ16, t.BZ17, t.BZ18, t.BZ19, t.BZ20, t.BZ21, t.BZ22, t.BZ23, t.BZ24, t.BZ25, t.BZ26, t.BZ27, t.BZ28, t.BZ29, t.BZ30, t.BZ31, t.BZ32, t.BZ33, t.BZ34, t.BZ35, t.BZ36, t.BZ37, t.BZ38, t.BZ39, t.BZ40, t.BZ41, t.BZ42, t.BZ43, t.BZ44, t.BZ45, t.BZ46, t.BZ47, t.BZ48, t.BZ49, t.BZ50, t.BZ51, t.BZ52, t.BZ53, t.BZ54, t.BZ55, t.BZ56, t.BZ57, t.BZ58, t.BZ59, t.BZ60, t.BZ61, t.BZ62, t.BZ63, t.BZ64, t.BZ65, t.BZ66, t.BZ67, t.BZ68, t.BZ69, t.BZ70, t.BZ71, t.BZ72, t.BZ73, t.BZ74, t.BZ75, t.BZ76, t.BZ77, t.BZ78, t.BZ79, t.BZ80, t.BZ81, t.BZ82, t.BZ83, t.BZ84, t.BZ85, t.BZ86, t.BZ87, t.BZ88, t.BZ89, t.BZ90, t.BZ91, t.BZ92, t.BZ93, t.BZ94, t.BZ95, t.BZ96, t.BZ97, t.BZ98, t.BZ99, t.BZ100, t.XM, t.XB, t.SJH, t.CSRQ, t.JG, t.MZMC, t.ZZMMMC, t.XQMC, t.ZJHM, t.ZJLX, t.XSLB, t.XZLX, t.RYZTID, t.USER_TYPE, t.PHOTO, t2.ID, t2.PYCCID, t2.NJID, t2.XYID, t2.ZYID, t2.BJID, t2.XGH AS joina_XGH FROM SYT_USER_INFO t LEFT JOIN SYT_CODE_XSZT t1 ON (t1.ID = t.RYZTID) LEFT JOIN SYT_USER_ORG_MAP t2 ON (t2.XGH = t.XGH) WHERE (t.xgh IN (?) AND t2.xgh IN (?)) ORDER BY t.xgh DESC
[2m2025-08-04 17:03:57.342[0;39m [32mDEBUG[0;39m [35m1312[0;39m [2m---[0;39m [2m[nio-8082-exec-5][0;39m [36mc.s.c.s.m.UserInfoMapper.selectJoinList [0;39m [2m:[0;39m ==>  Preparing: SELECT t.XGH, t.BZ1, t.BZ2, t.BZ3, t.BZ4, t.BZ5, t.BZ6, t.BZ7, t.BZ8, t.BZ9, t.BZ10, t.BZ11, t.BZ12, t.BZ13, t.BZ14, t.BZ15, t.BZ16, t.BZ17, t.BZ18, t.BZ19, t.BZ20, t.BZ21, t.BZ22, t.BZ23, t.BZ24, t.BZ25, t.BZ26, t.BZ27, t.BZ28, t.BZ29, t.BZ30, t.BZ31, t.BZ32, t.BZ33, t.BZ34, t.BZ35, t.BZ36, t.BZ37, t.BZ38, t.BZ39, t.BZ40, t.BZ41, t.BZ42, t.BZ43, t.BZ44, t.BZ45, t.BZ46, t.BZ47, t.BZ48, t.BZ49, t.BZ50, t.BZ51, t.BZ52, t.BZ53, t.BZ54, t.BZ55, t.BZ56, t.BZ57, t.BZ58, t.BZ59, t.BZ60, t.BZ61, t.BZ62, t.BZ63, t.BZ64, t.BZ65, t.BZ66, t.BZ67, t.BZ68, t.BZ69, t.BZ70, t.BZ71, t.BZ72, t.BZ73, t.BZ74, t.BZ75, t.BZ76, t.BZ77, t.BZ78, t.BZ79, t.BZ80, t.BZ81, t.BZ82, t.BZ83, t.BZ84, t.BZ85, t.BZ86, t.BZ87, t.BZ88, t.BZ89, t.BZ90, t.BZ91, t.BZ92, t.BZ93, t.BZ94, t.BZ95, t.BZ96, t.BZ97, t.BZ98, t.BZ99, t.BZ100, t.XM, t.XB, t.SJH, t.CSRQ, t.JG, t.MZMC, t.ZZMMMC, t.XQMC, t.ZJHM, t.ZJLX, t.XSLB, t.XZLX, t.RYZTID, t.USER_TYPE, t.PHOTO, t2.ID, t2.PYCCID, t2.NJID, t2.XYID, t2.ZYID, t2.BJID, t2.XGH AS joina_XGH FROM SYT_USER_INFO t LEFT JOIN SYT_CODE_XSZT t1 ON (t1.ID = t.RYZTID) LEFT JOIN SYT_USER_ORG_MAP t2 ON (t2.XGH = t.XGH) WHERE (t.xgh IN (?) AND t2.xgh IN (?)) ORDER BY t.xgh DESC
[2m2025-08-04 17:03:57.342[0;39m [32mDEBUG[0;39m [35m1312[0;39m [2m---[0;39m [2m[nio-8082-exec-5][0;39m [36mc.s.c.s.m.UserInfoMapper.selectJoinList [0;39m [2m:[0;39m ==> Parameters: admin(String), admin(String)
[2m2025-08-04 17:03:57.448[0;39m [32mDEBUG[0;39m [35m1312[0;39m [2m---[0;39m [2m[nio-8082-exec-5][0;39m [36mc.s.c.s.m.UserInfoMapper.selectJoinList [0;39m [2m:[0;39m <==      Total: 1
[2m2025-08-04 17:04:04.937[0;39m [32mDEBUG[0;39m [35m1312[0;39m [2m---[0;39m [2m[nio-8082-exec-8][0;39m [36m.c.c.m.i.CustomDataPermissionInterceptor[0;39m [2m:[0;39m original SQL: SELECT    ID,CODE,NAME,SORT,TEXT_FIELD,VALUE_FIELD    FROM  SYT_CODE_TYPE              ORDER BY sort DESC
[2m2025-08-04 17:04:04.944[0;39m [32mDEBUG[0;39m [35m1312[0;39m [2m---[0;39m [2m[nio-8082-exec-8][0;39m [36m.c.c.m.i.CustomDataPermissionInterceptor[0;39m [2m:[0;39m SQL to parse, SQL: SELECT    ID,CODE,NAME,SORT,TEXT_FIELD,VALUE_FIELD    FROM  SYT_CODE_TYPE              ORDER BY sort DESC
[2m2025-08-04 17:04:04.947[0;39m [32mDEBUG[0;39m [35m1312[0;39m [2m---[0;39m [2m[nio-8082-exec-8][0;39m [36m.c.c.m.i.CustomDataPermissionInterceptor[0;39m [2m:[0;39m parse the finished SQL: SELECT ID, CODE, NAME, SORT, TEXT_FIELD, VALUE_FIELD FROM SYT_CODE_TYPE ORDER BY sort DESC
[2m2025-08-04 17:04:04.948[0;39m [32mDEBUG[0;39m [35m1312[0;39m [2m---[0;39m [2m[nio-8082-exec-8][0;39m [36mc.s.p.c.m.CodeTypeMapper.selectList     [0;39m [2m:[0;39m ==>  Preparing: SELECT ID, CODE, NAME, SORT, TEXT_FIELD, VALUE_FIELD FROM SYT_CODE_TYPE ORDER BY sort DESC
[2m2025-08-04 17:04:04.949[0;39m [32mDEBUG[0;39m [35m1312[0;39m [2m---[0;39m [2m[nio-8082-exec-8][0;39m [36mc.s.p.c.m.CodeTypeMapper.selectList     [0;39m [2m:[0;39m ==> Parameters: 
[2m2025-08-04 17:04:04.976[0;39m [32mDEBUG[0;39m [35m1312[0;39m [2m---[0;39m [2m[nio-8082-exec-4][0;39m [36m.c.c.m.i.CustomDataPermissionInterceptor[0;39m [2m:[0;39m original SQL: SELECT    ID,GROUP_NAME,SHOW_FLAG,ACTIVE_FLAG,ADD_FLAG,MIN_NUM,MAX_NUM,LIST_FLAG,MODIFY_FLAG,ICON,EMPTY_DATA_HIDE,SORT,USER_TYPE,LIST_GROUP_ID    FROM  SYT_DICTIONARY_GROUP         WHERE  (user_type = ?) ORDER BY sort ASC
[2m2025-08-04 17:04:04.983[0;39m [32mDEBUG[0;39m [35m1312[0;39m [2m---[0;39m [2m[nio-8082-exec-4][0;39m [36m.c.c.m.i.CustomDataPermissionInterceptor[0;39m [2m:[0;39m SQL to parse, SQL: SELECT    ID,GROUP_NAME,SHOW_FLAG,ACTIVE_FLAG,ADD_FLAG,MIN_NUM,MAX_NUM,LIST_FLAG,MODIFY_FLAG,ICON,EMPTY_DATA_HIDE,SORT,USER_TYPE,LIST_GROUP_ID    FROM  SYT_DICTIONARY_GROUP         WHERE  (user_type = ?) ORDER BY sort ASC
[2m2025-08-04 17:04:04.986[0;39m [32mDEBUG[0;39m [35m1312[0;39m [2m---[0;39m [2m[nio-8082-exec-4][0;39m [36m.c.c.m.i.CustomDataPermissionInterceptor[0;39m [2m:[0;39m parse the finished SQL: SELECT ID, GROUP_NAME, SHOW_FLAG, ACTIVE_FLAG, ADD_FLAG, MIN_NUM, MAX_NUM, LIST_FLAG, MODIFY_FLAG, ICON, EMPTY_DATA_HIDE, SORT, USER_TYPE, LIST_GROUP_ID FROM SYT_DICTIONARY_GROUP WHERE (user_type = ?) ORDER BY sort ASC
[2m2025-08-04 17:04:05.010[0;39m [32mDEBUG[0;39m [35m1312[0;39m [2m---[0;39m [2m[nio-8082-exec-3][0;39m [36m.c.c.m.i.CustomDataPermissionInterceptor[0;39m [2m:[0;39m original SQL: SELECT    ID,GROUP_NAME,LOAD_DATA_URL,OPERATION_DATA_URL,DELETE_DATA_URL,DICTIONARY_FIELD_URL    FROM  SYT_LIST_GROUP_CONFIG
[2m2025-08-04 17:04:05.016[0;39m [32mDEBUG[0;39m [35m1312[0;39m [2m---[0;39m [2m[nio-8082-exec-3][0;39m [36m.c.c.m.i.CustomDataPermissionInterceptor[0;39m [2m:[0;39m SQL to parse, SQL: SELECT    ID,GROUP_NAME,LOAD_DATA_URL,OPERATION_DATA_URL,DELETE_DATA_URL,DICTIONARY_FIELD_URL    FROM  SYT_LIST_GROUP_CONFIG
[2m2025-08-04 17:04:05.018[0;39m [32mDEBUG[0;39m [35m1312[0;39m [2m---[0;39m [2m[nio-8082-exec-3][0;39m [36m.c.c.m.i.CustomDataPermissionInterceptor[0;39m [2m:[0;39m parse the finished SQL: SELECT ID, GROUP_NAME, LOAD_DATA_URL, OPERATION_DATA_URL, DELETE_DATA_URL, DICTIONARY_FIELD_URL FROM SYT_LIST_GROUP_CONFIG
[2m2025-08-04 17:04:05.378[0;39m [32mDEBUG[0;39m [35m1312[0;39m [2m---[0;39m [2m[nio-8082-exec-8][0;39m [36mc.s.p.c.m.CodeTypeMapper.selectList     [0;39m [2m:[0;39m <==      Total: 37
[2m2025-08-04 17:04:05.379[0;39m [32mDEBUG[0;39m [35m1312[0;39m [2m---[0;39m [2m[nio-8082-exec-4][0;39m [36mc.s.p.d.m.D.selectList                  [0;39m [2m:[0;39m ==>  Preparing: SELECT ID, GROUP_NAME, SHOW_FLAG, ACTIVE_FLAG, ADD_FLAG, MIN_NUM, MAX_NUM, LIST_FLAG, MODIFY_FLAG, ICON, EMPTY_DATA_HIDE, SORT, USER_TYPE, LIST_GROUP_ID FROM SYT_DICTIONARY_GROUP WHERE (user_type = ?) ORDER BY sort ASC
[2m2025-08-04 17:04:05.382[0;39m [32mDEBUG[0;39m [35m1312[0;39m [2m---[0;39m [2m[nio-8082-exec-4][0;39m [36mc.s.p.d.m.D.selectList                  [0;39m [2m:[0;39m ==> Parameters: 2(Integer)
[2m2025-08-04 17:04:05.466[0;39m [32mDEBUG[0;39m [35m1312[0;39m [2m---[0;39m [2m[nio-8082-exec-4][0;39m [36mc.s.p.d.m.D.selectList                  [0;39m [2m:[0;39m <==      Total: 5
[2m2025-08-04 17:04:05.466[0;39m [32mDEBUG[0;39m [35m1312[0;39m [2m---[0;39m [2m[nio-8082-exec-3][0;39m [36mc.s.p.d.m.L.selectList                  [0;39m [2m:[0;39m ==>  Preparing: SELECT ID, GROUP_NAME, LOAD_DATA_URL, OPERATION_DATA_URL, DELETE_DATA_URL, DICTIONARY_FIELD_URL FROM SYT_LIST_GROUP_CONFIG
[2m2025-08-04 17:04:05.467[0;39m [32mDEBUG[0;39m [35m1312[0;39m [2m---[0;39m [2m[nio-8082-exec-3][0;39m [36mc.s.p.d.m.L.selectList                  [0;39m [2m:[0;39m ==> Parameters: 
[2m2025-08-04 17:04:05.570[0;39m [32mDEBUG[0;39m [35m1312[0;39m [2m---[0;39m [2m[nio-8082-exec-3][0;39m [36mc.s.p.d.m.L.selectList                  [0;39m [2m:[0;39m <==      Total: 1
[2m2025-08-04 17:04:05.749[0;39m [32mDEBUG[0;39m [35m1312[0;39m [2m---[0;39m [2m[nio-8082-exec-7][0;39m [36m.c.c.m.i.CustomDataPermissionInterceptor[0;39m [2m:[0;39m original SQL: SELECT    ID,CODE,NAME,SORT,DESCRIPTION,CODE_TYPE    FROM  SYT_CODE_COMMON         WHERE  (code_type IN (?)) ORDER BY sort ASC
[2m2025-08-04 17:04:05.754[0;39m [32mDEBUG[0;39m [35m1312[0;39m [2m---[0;39m [2m[nio-8082-exec-7][0;39m [36m.c.c.m.i.CustomDataPermissionInterceptor[0;39m [2m:[0;39m SQL to parse, SQL: SELECT    ID,CODE,NAME,SORT,DESCRIPTION,CODE_TYPE    FROM  SYT_CODE_COMMON         WHERE  (code_type IN (?)) ORDER BY sort ASC
[2m2025-08-04 17:04:05.756[0;39m [32mDEBUG[0;39m [35m1312[0;39m [2m---[0;39m [2m[nio-8082-exec-7][0;39m [36m.c.c.m.i.CustomDataPermissionInterceptor[0;39m [2m:[0;39m parse the finished SQL: SELECT ID, CODE, NAME, SORT, DESCRIPTION, CODE_TYPE FROM SYT_CODE_COMMON WHERE (code_type IN (?)) ORDER BY sort ASC
[2m2025-08-04 17:04:05.757[0;39m [32mDEBUG[0;39m [35m1312[0;39m [2m---[0;39m [2m[nio-8082-exec-7][0;39m [36mc.s.p.c.m.CodeCommonMapper.selectList   [0;39m [2m:[0;39m ==>  Preparing: SELECT ID, CODE, NAME, SORT, DESCRIPTION, CODE_TYPE FROM SYT_CODE_COMMON WHERE (code_type IN (?)) ORDER BY sort ASC
[2m2025-08-04 17:04:05.757[0;39m [32mDEBUG[0;39m [35m1312[0;39m [2m---[0;39m [2m[nio-8082-exec-7][0;39m [36mc.s.p.c.m.CodeCommonMapper.selectList   [0;39m [2m:[0;39m ==> Parameters: listDicUrl(String)
[2m2025-08-04 17:04:05.846[0;39m [32mDEBUG[0;39m [35m1312[0;39m [2m---[0;39m [2m[nio-8082-exec-7][0;39m [36mc.s.p.c.m.CodeCommonMapper.selectList   [0;39m [2m:[0;39m <==      Total: 0
[2m2025-08-04 17:04:06.718[0;39m [32mDEBUG[0;39m [35m1312[0;39m [2m---[0;39m [2m[nio-8082-exec-6][0;39m [36m.c.c.m.i.CustomDataPermissionInterceptor[0;39m [2m:[0;39m original SQL: SELECT    ID,GROUP_NAME,SHOW_FLAG,ACTIVE_FLAG,ADD_FLAG,MIN_NUM,MAX_NUM,LIST_FLAG,MODIFY_FLAG,ICON,EMPTY_DATA_HIDE,SORT,USER_TYPE,LIST_GROUP_ID    FROM  SYT_DICTIONARY_GROUP         WHERE  (user_type = ?)
[2m2025-08-04 17:04:06.724[0;39m [32mDEBUG[0;39m [35m1312[0;39m [2m---[0;39m [2m[nio-8082-exec-6][0;39m [36m.c.c.m.i.CustomDataPermissionInterceptor[0;39m [2m:[0;39m SQL to parse, SQL: SELECT    ID,GROUP_NAME,SHOW_FLAG,ACTIVE_FLAG,ADD_FLAG,MIN_NUM,MAX_NUM,LIST_FLAG,MODIFY_FLAG,ICON,EMPTY_DATA_HIDE,SORT,USER_TYPE,LIST_GROUP_ID    FROM  SYT_DICTIONARY_GROUP         WHERE  (user_type = ?)
[2m2025-08-04 17:04:06.727[0;39m [32mDEBUG[0;39m [35m1312[0;39m [2m---[0;39m [2m[nio-8082-exec-6][0;39m [36m.c.c.m.i.CustomDataPermissionInterceptor[0;39m [2m:[0;39m parse the finished SQL: SELECT ID, GROUP_NAME, SHOW_FLAG, ACTIVE_FLAG, ADD_FLAG, MIN_NUM, MAX_NUM, LIST_FLAG, MODIFY_FLAG, ICON, EMPTY_DATA_HIDE, SORT, USER_TYPE, LIST_GROUP_ID FROM SYT_DICTIONARY_GROUP WHERE (user_type = ?)
[2m2025-08-04 17:04:06.738[0;39m [32mDEBUG[0;39m [35m1312[0;39m [2m---[0;39m [2m[nio-8082-exec-6][0;39m [36mc.s.p.d.m.D.selectList_mpCount          [0;39m [2m:[0;39m ==>  Preparing: SELECT COUNT(*) AS total FROM SYT_DICTIONARY_GROUP WHERE (user_type = ?)
[2m2025-08-04 17:04:06.738[0;39m [32mDEBUG[0;39m [35m1312[0;39m [2m---[0;39m [2m[nio-8082-exec-6][0;39m [36mc.s.p.d.m.D.selectList_mpCount          [0;39m [2m:[0;39m ==> Parameters: 2(Integer)
[2m2025-08-04 17:04:06.789[0;39m [32mDEBUG[0;39m [35m1312[0;39m [2m---[0;39m [2m[nio-8082-exec-2][0;39m [36m.c.c.m.i.CustomDataPermissionInterceptor[0;39m [2m:[0;39m original SQL: SELECT    ID,CODE,NAME,SORT,TEXT_FIELD,VALUE_FIELD    FROM  SYT_CODE_TYPE              ORDER BY sort DESC
[2m2025-08-04 17:04:06.794[0;39m [32mDEBUG[0;39m [35m1312[0;39m [2m---[0;39m [2m[nio-8082-exec-2][0;39m [36m.c.c.m.i.CustomDataPermissionInterceptor[0;39m [2m:[0;39m SQL to parse, SQL: SELECT    ID,CODE,NAME,SORT,TEXT_FIELD,VALUE_FIELD    FROM  SYT_CODE_TYPE              ORDER BY sort DESC
[2m2025-08-04 17:04:06.796[0;39m [32mDEBUG[0;39m [35m1312[0;39m [2m---[0;39m [2m[nio-8082-exec-2][0;39m [36m.c.c.m.i.CustomDataPermissionInterceptor[0;39m [2m:[0;39m parse the finished SQL: SELECT ID, CODE, NAME, SORT, TEXT_FIELD, VALUE_FIELD FROM SYT_CODE_TYPE ORDER BY sort DESC
[2m2025-08-04 17:04:06.797[0;39m [32mDEBUG[0;39m [35m1312[0;39m [2m---[0;39m [2m[nio-8082-exec-2][0;39m [36mc.s.p.c.m.CodeTypeMapper.selectList     [0;39m [2m:[0;39m ==>  Preparing: SELECT ID, CODE, NAME, SORT, TEXT_FIELD, VALUE_FIELD FROM SYT_CODE_TYPE ORDER BY sort DESC
[2m2025-08-04 17:04:06.797[0;39m [32mDEBUG[0;39m [35m1312[0;39m [2m---[0;39m [2m[nio-8082-exec-2][0;39m [36mc.s.p.c.m.CodeTypeMapper.selectList     [0;39m [2m:[0;39m ==> Parameters: 
[2m2025-08-04 17:04:06.821[0;39m [32mDEBUG[0;39m [35m1312[0;39m [2m---[0;39m [2m[nio-8082-exec-6][0;39m [36mc.s.p.d.m.D.selectList_mpCount          [0;39m [2m:[0;39m <==      Total: 1
[2m2025-08-04 17:04:06.840[0;39m [32mDEBUG[0;39m [35m1312[0;39m [2m---[0;39m [2m[nio-8082-exec-6][0;39m [36mc.s.p.d.m.D.selectList                  [0;39m [2m:[0;39m ==>  Preparing: SELECT * FROM ( SELECT TMP.*, ROWNUM ROW_ID FROM ( SELECT ID, GROUP_NAME, SHOW_FLAG, ACTIVE_FLAG, ADD_FLAG, MIN_NUM, MAX_NUM, LIST_FLAG, MODIFY_FLAG, ICON, EMPTY_DATA_HIDE, SORT, USER_TYPE, LIST_GROUP_ID FROM SYT_DICTIONARY_GROUP WHERE (user_type = ?) ORDER BY sort ASC ) TMP WHERE ROWNUM <=?) WHERE ROW_ID > ?
[2m2025-08-04 17:04:06.841[0;39m [32mDEBUG[0;39m [35m1312[0;39m [2m---[0;39m [2m[nio-8082-exec-6][0;39m [36mc.s.p.d.m.D.selectList                  [0;39m [2m:[0;39m ==> Parameters: 2(Integer), 10(Long), 0(Long)
[2m2025-08-04 17:04:06.884[0;39m [32mDEBUG[0;39m [35m1312[0;39m [2m---[0;39m [2m[io-8082-exec-10][0;39m [36m.c.c.m.i.CustomDataPermissionInterceptor[0;39m [2m:[0;39m original SQL: SELECT    ID,GROUP_NAME,LOAD_DATA_URL,OPERATION_DATA_URL,DELETE_DATA_URL,DICTIONARY_FIELD_URL    FROM  SYT_LIST_GROUP_CONFIG
[2m2025-08-04 17:04:06.884[0;39m [32mDEBUG[0;39m [35m1312[0;39m [2m---[0;39m [2m[nio-8082-exec-9][0;39m [36m.c.c.m.i.CustomDataPermissionInterceptor[0;39m [2m:[0;39m original SQL: SELECT    ID,GROUP_NAME,SHOW_FLAG,ACTIVE_FLAG,ADD_FLAG,MIN_NUM,MAX_NUM,LIST_FLAG,MODIFY_FLAG,ICON,EMPTY_DATA_HIDE,SORT,USER_TYPE,LIST_GROUP_ID    FROM  SYT_DICTIONARY_GROUP         WHERE  (user_type = ?) ORDER BY sort ASC
[2m2025-08-04 17:04:06.893[0;39m [32mDEBUG[0;39m [35m1312[0;39m [2m---[0;39m [2m[io-8082-exec-10][0;39m [36m.c.c.m.i.CustomDataPermissionInterceptor[0;39m [2m:[0;39m SQL to parse, SQL: SELECT    ID,GROUP_NAME,LOAD_DATA_URL,OPERATION_DATA_URL,DELETE_DATA_URL,DICTIONARY_FIELD_URL    FROM  SYT_LIST_GROUP_CONFIG
[2m2025-08-04 17:04:06.893[0;39m [32mDEBUG[0;39m [35m1312[0;39m [2m---[0;39m [2m[io-8082-exec-10][0;39m [36m.c.c.m.i.CustomDataPermissionInterceptor[0;39m [2m:[0;39m parse the finished SQL: SELECT ID, GROUP_NAME, LOAD_DATA_URL, OPERATION_DATA_URL, DELETE_DATA_URL, DICTIONARY_FIELD_URL FROM SYT_LIST_GROUP_CONFIG
[2m2025-08-04 17:04:06.897[0;39m [32mDEBUG[0;39m [35m1312[0;39m [2m---[0;39m [2m[nio-8082-exec-9][0;39m [36m.c.c.m.i.CustomDataPermissionInterceptor[0;39m [2m:[0;39m SQL to parse, SQL: SELECT    ID,GROUP_NAME,SHOW_FLAG,ACTIVE_FLAG,ADD_FLAG,MIN_NUM,MAX_NUM,LIST_FLAG,MODIFY_FLAG,ICON,EMPTY_DATA_HIDE,SORT,USER_TYPE,LIST_GROUP_ID    FROM  SYT_DICTIONARY_GROUP         WHERE  (user_type = ?) ORDER BY sort ASC
[2m2025-08-04 17:04:06.899[0;39m [32mDEBUG[0;39m [35m1312[0;39m [2m---[0;39m [2m[nio-8082-exec-9][0;39m [36m.c.c.m.i.CustomDataPermissionInterceptor[0;39m [2m:[0;39m parse the finished SQL: SELECT ID, GROUP_NAME, SHOW_FLAG, ACTIVE_FLAG, ADD_FLAG, MIN_NUM, MAX_NUM, LIST_FLAG, MODIFY_FLAG, ICON, EMPTY_DATA_HIDE, SORT, USER_TYPE, LIST_GROUP_ID FROM SYT_DICTIONARY_GROUP WHERE (user_type = ?) ORDER BY sort ASC
[2m2025-08-04 17:04:06.922[0;39m [32mDEBUG[0;39m [35m1312[0;39m [2m---[0;39m [2m[nio-8082-exec-6][0;39m [36mc.s.p.d.m.D.selectList                  [0;39m [2m:[0;39m <==      Total: 5
[2m2025-08-04 17:04:06.923[0;39m [32mDEBUG[0;39m [35m1312[0;39m [2m---[0;39m [2m[io-8082-exec-10][0;39m [36mc.s.p.d.m.L.selectList                  [0;39m [2m:[0;39m ==>  Preparing: SELECT ID, GROUP_NAME, LOAD_DATA_URL, OPERATION_DATA_URL, DELETE_DATA_URL, DICTIONARY_FIELD_URL FROM SYT_LIST_GROUP_CONFIG
[2m2025-08-04 17:04:06.923[0;39m [32mDEBUG[0;39m [35m1312[0;39m [2m---[0;39m [2m[io-8082-exec-10][0;39m [36mc.s.p.d.m.L.selectList                  [0;39m [2m:[0;39m ==> Parameters: 
[2m2025-08-04 17:04:06.926[0;39m [32mDEBUG[0;39m [35m1312[0;39m [2m---[0;39m [2m[nio-8082-exec-6][0;39m [36m.c.c.m.i.CustomDataPermissionInterceptor[0;39m [2m:[0;39m original SQL: SELECT    ID,GROUP_ID,ROLE_ID    FROM  SYT_DICTIONARY_GROUP_HIDE_ROLE         WHERE  (GROUP_ID = ?)
[2m2025-08-04 17:04:06.932[0;39m [32mDEBUG[0;39m [35m1312[0;39m [2m---[0;39m [2m[nio-8082-exec-6][0;39m [36m.c.c.m.i.CustomDataPermissionInterceptor[0;39m [2m:[0;39m SQL to parse, SQL: SELECT    ID,GROUP_ID,ROLE_ID    FROM  SYT_DICTIONARY_GROUP_HIDE_ROLE         WHERE  (GROUP_ID = ?)
[2m2025-08-04 17:04:06.934[0;39m [32mDEBUG[0;39m [35m1312[0;39m [2m---[0;39m [2m[nio-8082-exec-6][0;39m [36m.c.c.m.i.CustomDataPermissionInterceptor[0;39m [2m:[0;39m parse the finished SQL: SELECT ID, GROUP_ID, ROLE_ID FROM SYT_DICTIONARY_GROUP_HIDE_ROLE WHERE (GROUP_ID = ?)
[2m2025-08-04 17:04:06.996[0;39m [32mDEBUG[0;39m [35m1312[0;39m [2m---[0;39m [2m[io-8082-exec-10][0;39m [36mc.s.p.d.m.L.selectList                  [0;39m [2m:[0;39m <==      Total: 1
[2m2025-08-04 17:04:06.996[0;39m [32mDEBUG[0;39m [35m1312[0;39m [2m---[0;39m [2m[nio-8082-exec-9][0;39m [36mc.s.p.d.m.D.selectList                  [0;39m [2m:[0;39m ==>  Preparing: SELECT ID, GROUP_NAME, SHOW_FLAG, ACTIVE_FLAG, ADD_FLAG, MIN_NUM, MAX_NUM, LIST_FLAG, MODIFY_FLAG, ICON, EMPTY_DATA_HIDE, SORT, USER_TYPE, LIST_GROUP_ID FROM SYT_DICTIONARY_GROUP WHERE (user_type = ?) ORDER BY sort ASC
[2m2025-08-04 17:04:06.997[0;39m [32mDEBUG[0;39m [35m1312[0;39m [2m---[0;39m [2m[nio-8082-exec-9][0;39m [36mc.s.p.d.m.D.selectList                  [0;39m [2m:[0;39m ==> Parameters: 2(Integer)
[2m2025-08-04 17:04:07.075[0;39m [32mDEBUG[0;39m [35m1312[0;39m [2m---[0;39m [2m[nio-8082-exec-9][0;39m [36mc.s.p.d.m.D.selectList                  [0;39m [2m:[0;39m <==      Total: 5
[2m2025-08-04 17:04:07.075[0;39m [32mDEBUG[0;39m [35m1312[0;39m [2m---[0;39m [2m[nio-8082-exec-6][0;39m [36mc.s.p.d.m.D.selectList                  [0;39m [2m:[0;39m ==>  Preparing: SELECT ID, GROUP_ID, ROLE_ID FROM SYT_DICTIONARY_GROUP_HIDE_ROLE WHERE (GROUP_ID = ?)
[2m2025-08-04 17:04:07.075[0;39m [32mDEBUG[0;39m [35m1312[0;39m [2m---[0;39m [2m[nio-8082-exec-6][0;39m [36mc.s.p.d.m.D.selectList                  [0;39m [2m:[0;39m ==> Parameters: 7d6988d4498a122de59589537bb45cee(String)
[2m2025-08-04 17:04:07.143[0;39m [32mDEBUG[0;39m [35m1312[0;39m [2m---[0;39m [2m[nio-8082-exec-6][0;39m [36mc.s.p.d.m.D.selectList                  [0;39m [2m:[0;39m <==      Total: 0
[2m2025-08-04 17:04:07.145[0;39m [32mDEBUG[0;39m [35m1312[0;39m [2m---[0;39m [2m[nio-8082-exec-6][0;39m [36m.c.c.m.i.CustomDataPermissionInterceptor[0;39m [2m:[0;39m original SQL: SELECT    ID,GROUP_ID,EDU_LEVEL_ID    FROM  SYT_DICTIONARY_GROUP_EDU_LEVEL         WHERE  (GROUP_ID = ?)
[2m2025-08-04 17:04:07.154[0;39m [32mDEBUG[0;39m [35m1312[0;39m [2m---[0;39m [2m[nio-8082-exec-6][0;39m [36m.c.c.m.i.CustomDataPermissionInterceptor[0;39m [2m:[0;39m SQL to parse, SQL: SELECT    ID,GROUP_ID,EDU_LEVEL_ID    FROM  SYT_DICTIONARY_GROUP_EDU_LEVEL         WHERE  (GROUP_ID = ?)
[2m2025-08-04 17:04:07.156[0;39m [32mDEBUG[0;39m [35m1312[0;39m [2m---[0;39m [2m[nio-8082-exec-6][0;39m [36m.c.c.m.i.CustomDataPermissionInterceptor[0;39m [2m:[0;39m parse the finished SQL: SELECT ID, GROUP_ID, EDU_LEVEL_ID FROM SYT_DICTIONARY_GROUP_EDU_LEVEL WHERE (GROUP_ID = ?)
[2m2025-08-04 17:04:07.156[0;39m [32mDEBUG[0;39m [35m1312[0;39m [2m---[0;39m [2m[nio-8082-exec-6][0;39m [36mc.s.p.d.m.D.selectList                  [0;39m [2m:[0;39m ==>  Preparing: SELECT ID, GROUP_ID, EDU_LEVEL_ID FROM SYT_DICTIONARY_GROUP_EDU_LEVEL WHERE (GROUP_ID = ?)
[2m2025-08-04 17:04:07.157[0;39m [32mDEBUG[0;39m [35m1312[0;39m [2m---[0;39m [2m[nio-8082-exec-6][0;39m [36mc.s.p.d.m.D.selectList                  [0;39m [2m:[0;39m ==> Parameters: 7d6988d4498a122de59589537bb45cee(String)
[2m2025-08-04 17:04:07.235[0;39m [32mDEBUG[0;39m [35m1312[0;39m [2m---[0;39m [2m[nio-8082-exec-6][0;39m [36mc.s.p.d.m.D.selectList                  [0;39m [2m:[0;39m <==      Total: 0
[2m2025-08-04 17:04:07.236[0;39m [32mDEBUG[0;39m [35m1312[0;39m [2m---[0;39m [2m[nio-8082-exec-6][0;39m [36m.c.c.m.i.CustomDataPermissionInterceptor[0;39m [2m:[0;39m original SQL: SELECT    ID,GROUP_ID,ROLE_ID    FROM  SYT_DICTIONARY_GROUP_HIDE_ROLE         WHERE  (GROUP_ID = ?)
[2m2025-08-04 17:04:07.241[0;39m [32mDEBUG[0;39m [35m1312[0;39m [2m---[0;39m [2m[nio-8082-exec-6][0;39m [36m.c.c.m.i.CustomDataPermissionInterceptor[0;39m [2m:[0;39m SQL to parse, SQL: SELECT    ID,GROUP_ID,ROLE_ID    FROM  SYT_DICTIONARY_GROUP_HIDE_ROLE         WHERE  (GROUP_ID = ?)
[2m2025-08-04 17:04:07.243[0;39m [32mDEBUG[0;39m [35m1312[0;39m [2m---[0;39m [2m[nio-8082-exec-6][0;39m [36m.c.c.m.i.CustomDataPermissionInterceptor[0;39m [2m:[0;39m parse the finished SQL: SELECT ID, GROUP_ID, ROLE_ID FROM SYT_DICTIONARY_GROUP_HIDE_ROLE WHERE (GROUP_ID = ?)
[2m2025-08-04 17:04:07.243[0;39m [32mDEBUG[0;39m [35m1312[0;39m [2m---[0;39m [2m[nio-8082-exec-6][0;39m [36mc.s.p.d.m.D.selectList                  [0;39m [2m:[0;39m ==>  Preparing: SELECT ID, GROUP_ID, ROLE_ID FROM SYT_DICTIONARY_GROUP_HIDE_ROLE WHERE (GROUP_ID = ?)
[2m2025-08-04 17:04:07.243[0;39m [32mDEBUG[0;39m [35m1312[0;39m [2m---[0;39m [2m[nio-8082-exec-6][0;39m [36mc.s.p.d.m.D.selectList                  [0;39m [2m:[0;39m ==> Parameters: e0594c19ea31172b0ce7a37d3381d5de(String)
[2m2025-08-04 17:04:07.318[0;39m [32mDEBUG[0;39m [35m1312[0;39m [2m---[0;39m [2m[nio-8082-exec-6][0;39m [36mc.s.p.d.m.D.selectList                  [0;39m [2m:[0;39m <==      Total: 0
[2m2025-08-04 17:04:07.318[0;39m [32mDEBUG[0;39m [35m1312[0;39m [2m---[0;39m [2m[nio-8082-exec-6][0;39m [36m.c.c.m.i.CustomDataPermissionInterceptor[0;39m [2m:[0;39m original SQL: SELECT    ID,GROUP_ID,EDU_LEVEL_ID    FROM  SYT_DICTIONARY_GROUP_EDU_LEVEL         WHERE  (GROUP_ID = ?)
[2m2025-08-04 17:04:07.323[0;39m [32mDEBUG[0;39m [35m1312[0;39m [2m---[0;39m [2m[nio-8082-exec-6][0;39m [36m.c.c.m.i.CustomDataPermissionInterceptor[0;39m [2m:[0;39m SQL to parse, SQL: SELECT    ID,GROUP_ID,EDU_LEVEL_ID    FROM  SYT_DICTIONARY_GROUP_EDU_LEVEL         WHERE  (GROUP_ID = ?)
[2m2025-08-04 17:04:07.325[0;39m [32mDEBUG[0;39m [35m1312[0;39m [2m---[0;39m [2m[nio-8082-exec-6][0;39m [36m.c.c.m.i.CustomDataPermissionInterceptor[0;39m [2m:[0;39m parse the finished SQL: SELECT ID, GROUP_ID, EDU_LEVEL_ID FROM SYT_DICTIONARY_GROUP_EDU_LEVEL WHERE (GROUP_ID = ?)
[2m2025-08-04 17:04:07.327[0;39m [32mDEBUG[0;39m [35m1312[0;39m [2m---[0;39m [2m[nio-8082-exec-6][0;39m [36mc.s.p.d.m.D.selectList                  [0;39m [2m:[0;39m ==>  Preparing: SELECT ID, GROUP_ID, EDU_LEVEL_ID FROM SYT_DICTIONARY_GROUP_EDU_LEVEL WHERE (GROUP_ID = ?)
[2m2025-08-04 17:04:07.327[0;39m [32mDEBUG[0;39m [35m1312[0;39m [2m---[0;39m [2m[nio-8082-exec-6][0;39m [36mc.s.p.d.m.D.selectList                  [0;39m [2m:[0;39m ==> Parameters: e0594c19ea31172b0ce7a37d3381d5de(String)
[2m2025-08-04 17:04:07.397[0;39m [32mDEBUG[0;39m [35m1312[0;39m [2m---[0;39m [2m[nio-8082-exec-6][0;39m [36mc.s.p.d.m.D.selectList                  [0;39m [2m:[0;39m <==      Total: 0
[2m2025-08-04 17:04:07.398[0;39m [32mDEBUG[0;39m [35m1312[0;39m [2m---[0;39m [2m[nio-8082-exec-6][0;39m [36m.c.c.m.i.CustomDataPermissionInterceptor[0;39m [2m:[0;39m original SQL: SELECT    ID,GROUP_ID,ROLE_ID    FROM  SYT_DICTIONARY_GROUP_HIDE_ROLE         WHERE  (GROUP_ID = ?)
[2m2025-08-04 17:04:07.403[0;39m [32mDEBUG[0;39m [35m1312[0;39m [2m---[0;39m [2m[nio-8082-exec-6][0;39m [36m.c.c.m.i.CustomDataPermissionInterceptor[0;39m [2m:[0;39m SQL to parse, SQL: SELECT    ID,GROUP_ID,ROLE_ID    FROM  SYT_DICTIONARY_GROUP_HIDE_ROLE         WHERE  (GROUP_ID = ?)
[2m2025-08-04 17:04:07.404[0;39m [32mDEBUG[0;39m [35m1312[0;39m [2m---[0;39m [2m[nio-8082-exec-6][0;39m [36m.c.c.m.i.CustomDataPermissionInterceptor[0;39m [2m:[0;39m parse the finished SQL: SELECT ID, GROUP_ID, ROLE_ID FROM SYT_DICTIONARY_GROUP_HIDE_ROLE WHERE (GROUP_ID = ?)
[2m2025-08-04 17:04:07.405[0;39m [32mDEBUG[0;39m [35m1312[0;39m [2m---[0;39m [2m[nio-8082-exec-6][0;39m [36mc.s.p.d.m.D.selectList                  [0;39m [2m:[0;39m ==>  Preparing: SELECT ID, GROUP_ID, ROLE_ID FROM SYT_DICTIONARY_GROUP_HIDE_ROLE WHERE (GROUP_ID = ?)
[2m2025-08-04 17:04:07.405[0;39m [32mDEBUG[0;39m [35m1312[0;39m [2m---[0;39m [2m[nio-8082-exec-6][0;39m [36mc.s.p.d.m.D.selectList                  [0;39m [2m:[0;39m ==> Parameters: 3d308b159e369a31fa7035c7a38a11be(String)
[2m2025-08-04 17:04:07.477[0;39m [32mDEBUG[0;39m [35m1312[0;39m [2m---[0;39m [2m[nio-8082-exec-2][0;39m [36mc.s.p.c.m.CodeTypeMapper.selectList     [0;39m [2m:[0;39m <==      Total: 37
[2m2025-08-04 17:04:07.486[0;39m [32mDEBUG[0;39m [35m1312[0;39m [2m---[0;39m [2m[nio-8082-exec-6][0;39m [36mc.s.p.d.m.D.selectList                  [0;39m [2m:[0;39m <==      Total: 0
[2m2025-08-04 17:04:07.488[0;39m [32mDEBUG[0;39m [35m1312[0;39m [2m---[0;39m [2m[nio-8082-exec-6][0;39m [36m.c.c.m.i.CustomDataPermissionInterceptor[0;39m [2m:[0;39m original SQL: SELECT    ID,GROUP_ID,EDU_LEVEL_ID    FROM  SYT_DICTIONARY_GROUP_EDU_LEVEL         WHERE  (GROUP_ID = ?)
[2m2025-08-04 17:04:07.494[0;39m [32mDEBUG[0;39m [35m1312[0;39m [2m---[0;39m [2m[nio-8082-exec-6][0;39m [36m.c.c.m.i.CustomDataPermissionInterceptor[0;39m [2m:[0;39m SQL to parse, SQL: SELECT    ID,GROUP_ID,EDU_LEVEL_ID    FROM  SYT_DICTIONARY_GROUP_EDU_LEVEL         WHERE  (GROUP_ID = ?)
[2m2025-08-04 17:04:07.496[0;39m [32mDEBUG[0;39m [35m1312[0;39m [2m---[0;39m [2m[nio-8082-exec-6][0;39m [36m.c.c.m.i.CustomDataPermissionInterceptor[0;39m [2m:[0;39m parse the finished SQL: SELECT ID, GROUP_ID, EDU_LEVEL_ID FROM SYT_DICTIONARY_GROUP_EDU_LEVEL WHERE (GROUP_ID = ?)
[2m2025-08-04 17:04:07.496[0;39m [32mDEBUG[0;39m [35m1312[0;39m [2m---[0;39m [2m[nio-8082-exec-6][0;39m [36mc.s.p.d.m.D.selectList                  [0;39m [2m:[0;39m ==>  Preparing: SELECT ID, GROUP_ID, EDU_LEVEL_ID FROM SYT_DICTIONARY_GROUP_EDU_LEVEL WHERE (GROUP_ID = ?)
[2m2025-08-04 17:04:07.496[0;39m [32mDEBUG[0;39m [35m1312[0;39m [2m---[0;39m [2m[nio-8082-exec-6][0;39m [36mc.s.p.d.m.D.selectList                  [0;39m [2m:[0;39m ==> Parameters: 3d308b159e369a31fa7035c7a38a11be(String)
[2m2025-08-04 17:04:07.601[0;39m [32mDEBUG[0;39m [35m1312[0;39m [2m---[0;39m [2m[nio-8082-exec-6][0;39m [36mc.s.p.d.m.D.selectList                  [0;39m [2m:[0;39m <==      Total: 0
[2m2025-08-04 17:04:07.603[0;39m [32mDEBUG[0;39m [35m1312[0;39m [2m---[0;39m [2m[nio-8082-exec-6][0;39m [36m.c.c.m.i.CustomDataPermissionInterceptor[0;39m [2m:[0;39m original SQL: SELECT    ID,GROUP_ID,ROLE_ID    FROM  SYT_DICTIONARY_GROUP_HIDE_ROLE         WHERE  (GROUP_ID = ?)
[2m2025-08-04 17:04:07.612[0;39m [32mDEBUG[0;39m [35m1312[0;39m [2m---[0;39m [2m[nio-8082-exec-6][0;39m [36m.c.c.m.i.CustomDataPermissionInterceptor[0;39m [2m:[0;39m SQL to parse, SQL: SELECT    ID,GROUP_ID,ROLE_ID    FROM  SYT_DICTIONARY_GROUP_HIDE_ROLE         WHERE  (GROUP_ID = ?)
[2m2025-08-04 17:04:07.616[0;39m [32mDEBUG[0;39m [35m1312[0;39m [2m---[0;39m [2m[nio-8082-exec-6][0;39m [36m.c.c.m.i.CustomDataPermissionInterceptor[0;39m [2m:[0;39m parse the finished SQL: SELECT ID, GROUP_ID, ROLE_ID FROM SYT_DICTIONARY_GROUP_HIDE_ROLE WHERE (GROUP_ID = ?)
[2m2025-08-04 17:04:07.616[0;39m [32mDEBUG[0;39m [35m1312[0;39m [2m---[0;39m [2m[nio-8082-exec-6][0;39m [36mc.s.p.d.m.D.selectList                  [0;39m [2m:[0;39m ==>  Preparing: SELECT ID, GROUP_ID, ROLE_ID FROM SYT_DICTIONARY_GROUP_HIDE_ROLE WHERE (GROUP_ID = ?)
[2m2025-08-04 17:04:07.617[0;39m [32mDEBUG[0;39m [35m1312[0;39m [2m---[0;39m [2m[nio-8082-exec-6][0;39m [36mc.s.p.d.m.D.selectList                  [0;39m [2m:[0;39m ==> Parameters: d8ae5ab2384304bc3cbe2c9cf1512bd3(String)
[2m2025-08-04 17:04:07.701[0;39m [32mDEBUG[0;39m [35m1312[0;39m [2m---[0;39m [2m[nio-8082-exec-6][0;39m [36mc.s.p.d.m.D.selectList                  [0;39m [2m:[0;39m <==      Total: 0
[2m2025-08-04 17:04:07.705[0;39m [32mDEBUG[0;39m [35m1312[0;39m [2m---[0;39m [2m[nio-8082-exec-6][0;39m [36m.c.c.m.i.CustomDataPermissionInterceptor[0;39m [2m:[0;39m original SQL: SELECT    ID,GROUP_ID,EDU_LEVEL_ID    FROM  SYT_DICTIONARY_GROUP_EDU_LEVEL         WHERE  (GROUP_ID = ?)
[2m2025-08-04 17:04:07.716[0;39m [32mDEBUG[0;39m [35m1312[0;39m [2m---[0;39m [2m[nio-8082-exec-6][0;39m [36m.c.c.m.i.CustomDataPermissionInterceptor[0;39m [2m:[0;39m SQL to parse, SQL: SELECT    ID,GROUP_ID,EDU_LEVEL_ID    FROM  SYT_DICTIONARY_GROUP_EDU_LEVEL         WHERE  (GROUP_ID = ?)
[2m2025-08-04 17:04:07.719[0;39m [32mDEBUG[0;39m [35m1312[0;39m [2m---[0;39m [2m[nio-8082-exec-6][0;39m [36m.c.c.m.i.CustomDataPermissionInterceptor[0;39m [2m:[0;39m parse the finished SQL: SELECT ID, GROUP_ID, EDU_LEVEL_ID FROM SYT_DICTIONARY_GROUP_EDU_LEVEL WHERE (GROUP_ID = ?)
[2m2025-08-04 17:04:07.720[0;39m [32mDEBUG[0;39m [35m1312[0;39m [2m---[0;39m [2m[nio-8082-exec-6][0;39m [36mc.s.p.d.m.D.selectList                  [0;39m [2m:[0;39m ==>  Preparing: SELECT ID, GROUP_ID, EDU_LEVEL_ID FROM SYT_DICTIONARY_GROUP_EDU_LEVEL WHERE (GROUP_ID = ?)
[2m2025-08-04 17:04:07.720[0;39m [32mDEBUG[0;39m [35m1312[0;39m [2m---[0;39m [2m[nio-8082-exec-6][0;39m [36mc.s.p.d.m.D.selectList                  [0;39m [2m:[0;39m ==> Parameters: d8ae5ab2384304bc3cbe2c9cf1512bd3(String)
[2m2025-08-04 17:04:07.794[0;39m [32mDEBUG[0;39m [35m1312[0;39m [2m---[0;39m [2m[nio-8082-exec-6][0;39m [36mc.s.p.d.m.D.selectList                  [0;39m [2m:[0;39m <==      Total: 0
[2m2025-08-04 17:04:07.803[0;39m [32mDEBUG[0;39m [35m1312[0;39m [2m---[0;39m [2m[nio-8082-exec-6][0;39m [36m.c.c.m.i.CustomDataPermissionInterceptor[0;39m [2m:[0;39m original SQL: SELECT    ID,GROUP_ID,ROLE_ID    FROM  SYT_DICTIONARY_GROUP_HIDE_ROLE         WHERE  (GROUP_ID = ?)
[2m2025-08-04 17:04:07.810[0;39m [32mDEBUG[0;39m [35m1312[0;39m [2m---[0;39m [2m[nio-8082-exec-6][0;39m [36m.c.c.m.i.CustomDataPermissionInterceptor[0;39m [2m:[0;39m SQL to parse, SQL: SELECT    ID,GROUP_ID,ROLE_ID    FROM  SYT_DICTIONARY_GROUP_HIDE_ROLE         WHERE  (GROUP_ID = ?)
[2m2025-08-04 17:04:07.812[0;39m [32mDEBUG[0;39m [35m1312[0;39m [2m---[0;39m [2m[nio-8082-exec-6][0;39m [36m.c.c.m.i.CustomDataPermissionInterceptor[0;39m [2m:[0;39m parse the finished SQL: SELECT ID, GROUP_ID, ROLE_ID FROM SYT_DICTIONARY_GROUP_HIDE_ROLE WHERE (GROUP_ID = ?)
[2m2025-08-04 17:04:07.813[0;39m [32mDEBUG[0;39m [35m1312[0;39m [2m---[0;39m [2m[nio-8082-exec-6][0;39m [36mc.s.p.d.m.D.selectList                  [0;39m [2m:[0;39m ==>  Preparing: SELECT ID, GROUP_ID, ROLE_ID FROM SYT_DICTIONARY_GROUP_HIDE_ROLE WHERE (GROUP_ID = ?)
[2m2025-08-04 17:04:07.813[0;39m [32mDEBUG[0;39m [35m1312[0;39m [2m---[0;39m [2m[nio-8082-exec-6][0;39m [36mc.s.p.d.m.D.selectList                  [0;39m [2m:[0;39m ==> Parameters: e60c2aa27ff4813382f2d008c17aed97(String)
[2m2025-08-04 17:04:07.890[0;39m [32mDEBUG[0;39m [35m1312[0;39m [2m---[0;39m [2m[nio-8082-exec-6][0;39m [36mc.s.p.d.m.D.selectList                  [0;39m [2m:[0;39m <==      Total: 0
[2m2025-08-04 17:04:07.893[0;39m [32mDEBUG[0;39m [35m1312[0;39m [2m---[0;39m [2m[nio-8082-exec-6][0;39m [36m.c.c.m.i.CustomDataPermissionInterceptor[0;39m [2m:[0;39m original SQL: SELECT    ID,GROUP_ID,EDU_LEVEL_ID    FROM  SYT_DICTIONARY_GROUP_EDU_LEVEL         WHERE  (GROUP_ID = ?)
[2m2025-08-04 17:04:07.909[0;39m [32mDEBUG[0;39m [35m1312[0;39m [2m---[0;39m [2m[nio-8082-exec-6][0;39m [36m.c.c.m.i.CustomDataPermissionInterceptor[0;39m [2m:[0;39m SQL to parse, SQL: SELECT    ID,GROUP_ID,EDU_LEVEL_ID    FROM  SYT_DICTIONARY_GROUP_EDU_LEVEL         WHERE  (GROUP_ID = ?)
[2m2025-08-04 17:04:07.911[0;39m [32mDEBUG[0;39m [35m1312[0;39m [2m---[0;39m [2m[nio-8082-exec-6][0;39m [36m.c.c.m.i.CustomDataPermissionInterceptor[0;39m [2m:[0;39m parse the finished SQL: SELECT ID, GROUP_ID, EDU_LEVEL_ID FROM SYT_DICTIONARY_GROUP_EDU_LEVEL WHERE (GROUP_ID = ?)
[2m2025-08-04 17:04:07.912[0;39m [32mDEBUG[0;39m [35m1312[0;39m [2m---[0;39m [2m[nio-8082-exec-6][0;39m [36mc.s.p.d.m.D.selectList                  [0;39m [2m:[0;39m ==>  Preparing: SELECT ID, GROUP_ID, EDU_LEVEL_ID FROM SYT_DICTIONARY_GROUP_EDU_LEVEL WHERE (GROUP_ID = ?)
[2m2025-08-04 17:04:07.912[0;39m [32mDEBUG[0;39m [35m1312[0;39m [2m---[0;39m [2m[nio-8082-exec-6][0;39m [36mc.s.p.d.m.D.selectList                  [0;39m [2m:[0;39m ==> Parameters: e60c2aa27ff4813382f2d008c17aed97(String)
[2m2025-08-04 17:04:07.984[0;39m [32mDEBUG[0;39m [35m1312[0;39m [2m---[0;39m [2m[nio-8082-exec-6][0;39m [36mc.s.p.d.m.D.selectList                  [0;39m [2m:[0;39m <==      Total: 0
[2m2025-08-04 17:04:15.626[0;39m [32mDEBUG[0;39m [35m1312[0;39m [2m---[0;39m [2m[nio-8082-exec-8][0;39m [36m.c.c.m.i.CustomDataPermissionInterceptor[0;39m [2m:[0;39m original SQL: SELECT    ID,NAME,PROJECT_ID,YEAR,CREATE_TIME,MODULE_CODE    FROM  SYT_WORKFLOW         WHERE  (project_id IS NULL AND module_code LIKE ?)
[2m2025-08-04 17:04:15.649[0;39m [32mDEBUG[0;39m [35m1312[0;39m [2m---[0;39m [2m[nio-8082-exec-8][0;39m [36m.c.c.m.i.CustomDataPermissionInterceptor[0;39m [2m:[0;39m SQL to parse, SQL: SELECT    ID,NAME,PROJECT_ID,YEAR,CREATE_TIME,MODULE_CODE    FROM  SYT_WORKFLOW         WHERE  (project_id IS NULL AND module_code LIKE ?)
[2m2025-08-04 17:04:15.654[0;39m [32mDEBUG[0;39m [35m1312[0;39m [2m---[0;39m [2m[nio-8082-exec-8][0;39m [36m.c.c.m.i.CustomDataPermissionInterceptor[0;39m [2m:[0;39m parse the finished SQL: SELECT ID, NAME, PROJECT_ID, YEAR, CREATE_TIME, MODULE_CODE FROM SYT_WORKFLOW WHERE (project_id IS NULL AND module_code LIKE ?)
[2m2025-08-04 17:04:15.667[0;39m [32mDEBUG[0;39m [35m1312[0;39m [2m---[0;39m [2m[nio-8082-exec-8][0;39m [36mc.s.p.w.m.W.selectList_mpCount          [0;39m [2m:[0;39m ==>  Preparing: SELECT COUNT(*) AS total FROM SYT_WORKFLOW WHERE (project_id IS NULL AND module_code LIKE ?)
[2m2025-08-04 17:04:15.673[0;39m [32mDEBUG[0;39m [35m1312[0;39m [2m---[0;39m [2m[nio-8082-exec-8][0;39m [36mc.s.p.w.m.W.selectList_mpCount          [0;39m [2m:[0;39m ==> Parameters: qgzx%(String)
[2m2025-08-04 17:04:18.600[0;39m [32mDEBUG[0;39m [35m1312[0;39m [2m---[0;39m [2m[nio-8082-exec-8][0;39m [36mc.s.p.w.m.W.selectList_mpCount          [0;39m [2m:[0;39m <==      Total: 1
[2m2025-08-04 17:04:18.609[0;39m [32mDEBUG[0;39m [35m1312[0;39m [2m---[0;39m [2m[nio-8082-exec-8][0;39m [36mc.s.p.w.m.WorkflowMapper.selectList     [0;39m [2m:[0;39m ==>  Preparing: SELECT * FROM ( SELECT TMP.*, ROWNUM ROW_ID FROM ( SELECT ID, NAME, PROJECT_ID, YEAR, CREATE_TIME, MODULE_CODE FROM SYT_WORKFLOW WHERE (project_id IS NULL AND module_code LIKE ?) ORDER BY create_time ASC ) TMP WHERE ROWNUM <=?) WHERE ROW_ID > ?
[2m2025-08-04 17:04:18.611[0;39m [32mDEBUG[0;39m [35m1312[0;39m [2m---[0;39m [2m[nio-8082-exec-8][0;39m [36mc.s.p.w.m.WorkflowMapper.selectList     [0;39m [2m:[0;39m ==> Parameters: qgzx%(String), 10(Long), 0(Long)
[2m2025-08-04 17:04:19.201[0;39m [32mDEBUG[0;39m [35m1312[0;39m [2m---[0;39m [2m[nio-8082-exec-8][0;39m [36mc.s.p.w.m.WorkflowMapper.selectList     [0;39m [2m:[0;39m <==      Total: 4
[2m2025-08-04 17:04:29.098[0;39m [32mDEBUG[0;39m [35m1312[0;39m [2m---[0;39m [2m[nio-8082-exec-4][0;39m [36m.c.c.m.i.CustomDataPermissionInterceptor[0;39m [2m:[0;39m original SQL: SELECT    ID,NAME,YZGBC,YZDGS,KYGGWS,KSQSL,SFQYKCHLX    FROM  SYT_QGZX_JOB_TYPE
[2m2025-08-04 17:04:29.111[0;39m [32mDEBUG[0;39m [35m1312[0;39m [2m---[0;39m [2m[nio-8082-exec-4][0;39m [36m.c.c.m.i.CustomDataPermissionInterceptor[0;39m [2m:[0;39m SQL to parse, SQL: SELECT    ID,NAME,YZGBC,YZDGS,KYGGWS,KSQSL,SFQYKCHLX    FROM  SYT_QGZX_JOB_TYPE
[2m2025-08-04 17:04:29.114[0;39m [32mDEBUG[0;39m [35m1312[0;39m [2m---[0;39m [2m[nio-8082-exec-4][0;39m [36m.c.c.m.i.CustomDataPermissionInterceptor[0;39m [2m:[0;39m parse the finished SQL: SELECT ID, NAME, YZGBC, YZDGS, KYGGWS, KSQSL, SFQYKCHLX FROM SYT_QGZX_JOB_TYPE
[2m2025-08-04 17:04:29.124[0;39m [32mDEBUG[0;39m [35m1312[0;39m [2m---[0;39m [2m[nio-8082-exec-4][0;39m [36mc.s.p.w.m.Q.selectList_mpCount          [0;39m [2m:[0;39m ==>  Preparing: SELECT COUNT(*) AS total FROM SYT_QGZX_JOB_TYPE
[2m2025-08-04 17:04:29.125[0;39m [32mDEBUG[0;39m [35m1312[0;39m [2m---[0;39m [2m[nio-8082-exec-4][0;39m [36mc.s.p.w.m.Q.selectList_mpCount          [0;39m [2m:[0;39m ==> Parameters: 
[2m2025-08-04 17:04:29.224[0;39m [32mDEBUG[0;39m [35m1312[0;39m [2m---[0;39m [2m[nio-8082-exec-4][0;39m [36mc.s.p.w.m.Q.selectList_mpCount          [0;39m [2m:[0;39m <==      Total: 1
[2m2025-08-04 17:04:29.224[0;39m [32mDEBUG[0;39m [35m1312[0;39m [2m---[0;39m [2m[nio-8082-exec-4][0;39m [36mc.s.p.w.m.QgzxJobTypeMapper.selectList  [0;39m [2m:[0;39m ==>  Preparing: SELECT * FROM ( SELECT TMP.*, ROWNUM ROW_ID FROM ( SELECT ID, NAME, YZGBC, YZDGS, KYGGWS, KSQSL, SFQYKCHLX FROM SYT_QGZX_JOB_TYPE ) TMP WHERE ROWNUM <=?) WHERE ROW_ID > ?
[2m2025-08-04 17:04:29.225[0;39m [32mDEBUG[0;39m [35m1312[0;39m [2m---[0;39m [2m[nio-8082-exec-4][0;39m [36mc.s.p.w.m.QgzxJobTypeMapper.selectList  [0;39m [2m:[0;39m ==> Parameters: 10(Long), 0(Long)
[2m2025-08-04 17:04:29.341[0;39m [32mDEBUG[0;39m [35m1312[0;39m [2m---[0;39m [2m[nio-8082-exec-4][0;39m [36mc.s.p.w.m.QgzxJobTypeMapper.selectList  [0;39m [2m:[0;39m <==      Total: 4
[2m2025-08-04 17:06:54.721[0;39m [32mDEBUG[0;39m [35m1312[0;39m [2m---[0;39m [2m[nio-8082-exec-3][0;39m [36m.c.c.m.i.CustomDataPermissionInterceptor[0;39m [2m:[0;39m original SQL: SELECT    ID,NAME,YZGBC,YZDGS,KYGGWS,KSQSL,SFQYKCHLX    FROM  SYT_QGZX_JOB_TYPE
[2m2025-08-04 17:06:54.743[0;39m [32mDEBUG[0;39m [35m1312[0;39m [2m---[0;39m [2m[nio-8082-exec-3][0;39m [36m.c.c.m.i.CustomDataPermissionInterceptor[0;39m [2m:[0;39m SQL to parse, SQL: SELECT    ID,NAME,YZGBC,YZDGS,KYGGWS,KSQSL,SFQYKCHLX    FROM  SYT_QGZX_JOB_TYPE
[2m2025-08-04 17:06:54.745[0;39m [32mDEBUG[0;39m [35m1312[0;39m [2m---[0;39m [2m[nio-8082-exec-3][0;39m [36m.c.c.m.i.CustomDataPermissionInterceptor[0;39m [2m:[0;39m parse the finished SQL: SELECT ID, NAME, YZGBC, YZDGS, KYGGWS, KSQSL, SFQYKCHLX FROM SYT_QGZX_JOB_TYPE
[2m2025-08-04 17:06:54.876[0;39m [32mDEBUG[0;39m [35m1312[0;39m [2m---[0;39m [2m[nio-8082-exec-3][0;39m [36mc.s.p.w.m.Q.selectList_mpCount          [0;39m [2m:[0;39m ==>  Preparing: SELECT COUNT(*) AS total FROM SYT_QGZX_JOB_TYPE
[2m2025-08-04 17:06:54.877[0;39m [32mDEBUG[0;39m [35m1312[0;39m [2m---[0;39m [2m[nio-8082-exec-3][0;39m [36mc.s.p.w.m.Q.selectList_mpCount          [0;39m [2m:[0;39m ==> Parameters: 
[2m2025-08-04 17:06:54.963[0;39m [32mDEBUG[0;39m [35m1312[0;39m [2m---[0;39m [2m[nio-8082-exec-3][0;39m [36mc.s.p.w.m.Q.selectList_mpCount          [0;39m [2m:[0;39m <==      Total: 1
[2m2025-08-04 17:06:54.963[0;39m [32mDEBUG[0;39m [35m1312[0;39m [2m---[0;39m [2m[nio-8082-exec-3][0;39m [36mc.s.p.w.m.QgzxJobTypeMapper.selectList  [0;39m [2m:[0;39m ==>  Preparing: SELECT * FROM ( SELECT TMP.*, ROWNUM ROW_ID FROM ( SELECT ID, NAME, YZGBC, YZDGS, KYGGWS, KSQSL, SFQYKCHLX FROM SYT_QGZX_JOB_TYPE ) TMP WHERE ROWNUM <=?) WHERE ROW_ID > ?
[2m2025-08-04 17:06:54.964[0;39m [32mDEBUG[0;39m [35m1312[0;39m [2m---[0;39m [2m[nio-8082-exec-3][0;39m [36mc.s.p.w.m.QgzxJobTypeMapper.selectList  [0;39m [2m:[0;39m ==> Parameters: 10(Long), 0(Long)
[2m2025-08-04 17:06:55.052[0;39m [32mDEBUG[0;39m [35m1312[0;39m [2m---[0;39m [2m[nio-8082-exec-3][0;39m [36mc.s.p.w.m.QgzxJobTypeMapper.selectList  [0;39m [2m:[0;39m <==      Total: 4
[2m2025-08-04 17:19:47.041[0;39m [32mDEBUG[0;39m [35m1312[0;39m [2m---[0;39m [2m[nio-8082-exec-7][0;39m [36m.c.c.m.i.CustomDataPermissionInterceptor[0;39m [2m:[0;39m original SQL: SELECT    ID,PARAM_NAME,PARAM_VALUE,PARAM_MODE,DESCRIPTION,ATTACHMENT,create_time    FROM  SYT_SYS_PARAM         WHERE  (param_mode = ?) ORDER BY create_time DESC
[2m2025-08-04 17:19:47.070[0;39m [32mDEBUG[0;39m [35m1312[0;39m [2m---[0;39m [2m[nio-8082-exec-7][0;39m [36m.c.c.m.i.CustomDataPermissionInterceptor[0;39m [2m:[0;39m SQL to parse, SQL: SELECT    ID,PARAM_NAME,PARAM_VALUE,PARAM_MODE,DESCRIPTION,ATTACHMENT,create_time    FROM  SYT_SYS_PARAM         WHERE  (param_mode = ?) ORDER BY create_time DESC
[2m2025-08-04 17:19:47.073[0;39m [32mDEBUG[0;39m [35m1312[0;39m [2m---[0;39m [2m[nio-8082-exec-7][0;39m [36m.c.c.m.i.CustomDataPermissionInterceptor[0;39m [2m:[0;39m parse the finished SQL: SELECT ID, PARAM_NAME, PARAM_VALUE, PARAM_MODE, DESCRIPTION, ATTACHMENT, create_time FROM SYT_SYS_PARAM WHERE (param_mode = ?) ORDER BY create_time DESC
[2m2025-08-04 17:19:47.146[0;39m [32mDEBUG[0;39m [35m1312[0;39m [2m---[0;39m [2m[nio-8082-exec-7][0;39m [36mc.s.c.s.m.SysParamMapper.selectList     [0;39m [2m:[0;39m ==>  Preparing: SELECT ID, PARAM_NAME, PARAM_VALUE, PARAM_MODE, DESCRIPTION, ATTACHMENT, create_time FROM SYT_SYS_PARAM WHERE (param_mode = ?) ORDER BY create_time DESC
[2m2025-08-04 17:19:47.153[0;39m [32mDEBUG[0;39m [35m1312[0;39m [2m---[0;39m [2m[nio-8082-exec-7][0;39m [36mc.s.c.s.m.SysParamMapper.selectList     [0;39m [2m:[0;39m ==> Parameters: system-info(String)
[2m2025-08-04 17:19:47.233[0;39m [32mDEBUG[0;39m [35m1312[0;39m [2m---[0;39m [2m[nio-8082-exec-7][0;39m [36mc.s.c.s.m.SysParamMapper.selectList     [0;39m [2m:[0;39m <==      Total: 0
[2m2025-08-04 17:19:48.157[0;39m [32mDEBUG[0;39m [35m1312[0;39m [2m---[0;39m [2m[nio-8082-exec-1][0;39m [36m.c.c.m.i.CustomDataPermissionInterceptor[0;39m [2m:[0;39m original SQL: SELECT     t.XGH,t.BZ1,t.BZ2,t.BZ3,t.BZ4,t.BZ5,t.BZ6,t.BZ7,t.BZ8,t.BZ9,t.BZ10,t.BZ11,t.BZ12,t.BZ13,t.BZ14,t.BZ15,t.BZ16,t.BZ17,t.BZ18,t.BZ19,t.BZ20,t.BZ21,t.BZ22,t.BZ23,t.BZ24,t.BZ25,t.BZ26,t.BZ27,t.BZ28,t.BZ29,t.BZ30,t.BZ31,t.BZ32,t.BZ33,t.BZ34,t.BZ35,t.BZ36,t.BZ37,t.BZ38,t.BZ39,t.BZ40,t.BZ41,t.BZ42,t.BZ43,t.BZ44,t.BZ45,t.BZ46,t.BZ47,t.BZ48,t.BZ49,t.BZ50,t.BZ51,t.BZ52,t.BZ53,t.BZ54,t.BZ55,t.BZ56,t.BZ57,t.BZ58,t.BZ59,t.BZ60,t.BZ61,t.BZ62,t.BZ63,t.BZ64,t.BZ65,t.BZ66,t.BZ67,t.BZ68,t.BZ69,t.BZ70,t.BZ71,t.BZ72,t.BZ73,t.BZ74,t.BZ75,t.BZ76,t.BZ77,t.BZ78,t.BZ79,t.BZ80,t.BZ81,t.BZ82,t.BZ83,t.BZ84,t.BZ85,t.BZ86,t.BZ87,t.BZ88,t.BZ89,t.BZ90,t.BZ91,t.BZ92,t.BZ93,t.BZ94,t.BZ95,t.BZ96,t.BZ97,t.BZ98,t.BZ99,t.BZ100,t.XM,t.XB,t.SJH,t.CSRQ,t.JG,t.MZMC,t.ZZMMMC,t.XQMC,t.ZJHM,t.ZJLX,t.XSLB,t.XZLX,t.RYZTID,t.USER_TYPE,t.PHOTO,t2.ID,t2.PYCCID,t2.NJID,t2.XYID,t2.ZYID,t2.BJID,t2.XGH AS joina_XGH   FROM SYT_USER_INFO  t    LEFT JOIN SYT_CODE_XSZT t1 ON (t1.ID = t.RYZTID) LEFT JOIN SYT_USER_ORG_MAP t2 ON (t2.XGH = t.XGH)     WHERE   (t.xgh IN (?) AND t2.xgh IN (?)) ORDER BY t.xgh DESC
[2m2025-08-04 17:19:48.187[0;39m [32mDEBUG[0;39m [35m1312[0;39m [2m---[0;39m [2m[nio-8082-exec-1][0;39m [36m.c.c.m.i.CustomDataPermissionInterceptor[0;39m [2m:[0;39m SQL to parse, SQL: SELECT     t.XGH,t.BZ1,t.BZ2,t.BZ3,t.BZ4,t.BZ5,t.BZ6,t.BZ7,t.BZ8,t.BZ9,t.BZ10,t.BZ11,t.BZ12,t.BZ13,t.BZ14,t.BZ15,t.BZ16,t.BZ17,t.BZ18,t.BZ19,t.BZ20,t.BZ21,t.BZ22,t.BZ23,t.BZ24,t.BZ25,t.BZ26,t.BZ27,t.BZ28,t.BZ29,t.BZ30,t.BZ31,t.BZ32,t.BZ33,t.BZ34,t.BZ35,t.BZ36,t.BZ37,t.BZ38,t.BZ39,t.BZ40,t.BZ41,t.BZ42,t.BZ43,t.BZ44,t.BZ45,t.BZ46,t.BZ47,t.BZ48,t.BZ49,t.BZ50,t.BZ51,t.BZ52,t.BZ53,t.BZ54,t.BZ55,t.BZ56,t.BZ57,t.BZ58,t.BZ59,t.BZ60,t.BZ61,t.BZ62,t.BZ63,t.BZ64,t.BZ65,t.BZ66,t.BZ67,t.BZ68,t.BZ69,t.BZ70,t.BZ71,t.BZ72,t.BZ73,t.BZ74,t.BZ75,t.BZ76,t.BZ77,t.BZ78,t.BZ79,t.BZ80,t.BZ81,t.BZ82,t.BZ83,t.BZ84,t.BZ85,t.BZ86,t.BZ87,t.BZ88,t.BZ89,t.BZ90,t.BZ91,t.BZ92,t.BZ93,t.BZ94,t.BZ95,t.BZ96,t.BZ97,t.BZ98,t.BZ99,t.BZ100,t.XM,t.XB,t.SJH,t.CSRQ,t.JG,t.MZMC,t.ZZMMMC,t.XQMC,t.ZJHM,t.ZJLX,t.XSLB,t.XZLX,t.RYZTID,t.USER_TYPE,t.PHOTO,t2.ID,t2.PYCCID,t2.NJID,t2.XYID,t2.ZYID,t2.BJID,t2.XGH AS joina_XGH   FROM SYT_USER_INFO  t    LEFT JOIN SYT_CODE_XSZT t1 ON (t1.ID = t.RYZTID) LEFT JOIN SYT_USER_ORG_MAP t2 ON (t2.XGH = t.XGH)     WHERE   (t.xgh IN (?) AND t2.xgh IN (?)) ORDER BY t.xgh DESC
[2m2025-08-04 17:19:48.190[0;39m [32mDEBUG[0;39m [35m1312[0;39m [2m---[0;39m [2m[nio-8082-exec-1][0;39m [36m.c.c.m.i.CustomDataPermissionInterceptor[0;39m [2m:[0;39m parse the finished SQL: SELECT t.XGH, t.BZ1, t.BZ2, t.BZ3, t.BZ4, t.BZ5, t.BZ6, t.BZ7, t.BZ8, t.BZ9, t.BZ10, t.BZ11, t.BZ12, t.BZ13, t.BZ14, t.BZ15, t.BZ16, t.BZ17, t.BZ18, t.BZ19, t.BZ20, t.BZ21, t.BZ22, t.BZ23, t.BZ24, t.BZ25, t.BZ26, t.BZ27, t.BZ28, t.BZ29, t.BZ30, t.BZ31, t.BZ32, t.BZ33, t.BZ34, t.BZ35, t.BZ36, t.BZ37, t.BZ38, t.BZ39, t.BZ40, t.BZ41, t.BZ42, t.BZ43, t.BZ44, t.BZ45, t.BZ46, t.BZ47, t.BZ48, t.BZ49, t.BZ50, t.BZ51, t.BZ52, t.BZ53, t.BZ54, t.BZ55, t.BZ56, t.BZ57, t.BZ58, t.BZ59, t.BZ60, t.BZ61, t.BZ62, t.BZ63, t.BZ64, t.BZ65, t.BZ66, t.BZ67, t.BZ68, t.BZ69, t.BZ70, t.BZ71, t.BZ72, t.BZ73, t.BZ74, t.BZ75, t.BZ76, t.BZ77, t.BZ78, t.BZ79, t.BZ80, t.BZ81, t.BZ82, t.BZ83, t.BZ84, t.BZ85, t.BZ86, t.BZ87, t.BZ88, t.BZ89, t.BZ90, t.BZ91, t.BZ92, t.BZ93, t.BZ94, t.BZ95, t.BZ96, t.BZ97, t.BZ98, t.BZ99, t.BZ100, t.XM, t.XB, t.SJH, t.CSRQ, t.JG, t.MZMC, t.ZZMMMC, t.XQMC, t.ZJHM, t.ZJLX, t.XSLB, t.XZLX, t.RYZTID, t.USER_TYPE, t.PHOTO, t2.ID, t2.PYCCID, t2.NJID, t2.XYID, t2.ZYID, t2.BJID, t2.XGH AS joina_XGH FROM SYT_USER_INFO t LEFT JOIN SYT_CODE_XSZT t1 ON (t1.ID = t.RYZTID) LEFT JOIN SYT_USER_ORG_MAP t2 ON (t2.XGH = t.XGH) WHERE (t.xgh IN (?) AND t2.xgh IN (?)) ORDER BY t.xgh DESC
[2m2025-08-04 17:19:48.191[0;39m [32mDEBUG[0;39m [35m1312[0;39m [2m---[0;39m [2m[nio-8082-exec-1][0;39m [36mc.s.c.s.m.UserInfoMapper.selectJoinList [0;39m [2m:[0;39m ==>  Preparing: SELECT t.XGH, t.BZ1, t.BZ2, t.BZ3, t.BZ4, t.BZ5, t.BZ6, t.BZ7, t.BZ8, t.BZ9, t.BZ10, t.BZ11, t.BZ12, t.BZ13, t.BZ14, t.BZ15, t.BZ16, t.BZ17, t.BZ18, t.BZ19, t.BZ20, t.BZ21, t.BZ22, t.BZ23, t.BZ24, t.BZ25, t.BZ26, t.BZ27, t.BZ28, t.BZ29, t.BZ30, t.BZ31, t.BZ32, t.BZ33, t.BZ34, t.BZ35, t.BZ36, t.BZ37, t.BZ38, t.BZ39, t.BZ40, t.BZ41, t.BZ42, t.BZ43, t.BZ44, t.BZ45, t.BZ46, t.BZ47, t.BZ48, t.BZ49, t.BZ50, t.BZ51, t.BZ52, t.BZ53, t.BZ54, t.BZ55, t.BZ56, t.BZ57, t.BZ58, t.BZ59, t.BZ60, t.BZ61, t.BZ62, t.BZ63, t.BZ64, t.BZ65, t.BZ66, t.BZ67, t.BZ68, t.BZ69, t.BZ70, t.BZ71, t.BZ72, t.BZ73, t.BZ74, t.BZ75, t.BZ76, t.BZ77, t.BZ78, t.BZ79, t.BZ80, t.BZ81, t.BZ82, t.BZ83, t.BZ84, t.BZ85, t.BZ86, t.BZ87, t.BZ88, t.BZ89, t.BZ90, t.BZ91, t.BZ92, t.BZ93, t.BZ94, t.BZ95, t.BZ96, t.BZ97, t.BZ98, t.BZ99, t.BZ100, t.XM, t.XB, t.SJH, t.CSRQ, t.JG, t.MZMC, t.ZZMMMC, t.XQMC, t.ZJHM, t.ZJLX, t.XSLB, t.XZLX, t.RYZTID, t.USER_TYPE, t.PHOTO, t2.ID, t2.PYCCID, t2.NJID, t2.XYID, t2.ZYID, t2.BJID, t2.XGH AS joina_XGH FROM SYT_USER_INFO t LEFT JOIN SYT_CODE_XSZT t1 ON (t1.ID = t.RYZTID) LEFT JOIN SYT_USER_ORG_MAP t2 ON (t2.XGH = t.XGH) WHERE (t.xgh IN (?) AND t2.xgh IN (?)) ORDER BY t.xgh DESC
[2m2025-08-04 17:19:48.191[0;39m [32mDEBUG[0;39m [35m1312[0;39m [2m---[0;39m [2m[nio-8082-exec-1][0;39m [36mc.s.c.s.m.UserInfoMapper.selectJoinList [0;39m [2m:[0;39m ==> Parameters: admin(String), admin(String)
[2m2025-08-04 17:19:48.281[0;39m [32mDEBUG[0;39m [35m1312[0;39m [2m---[0;39m [2m[nio-8082-exec-1][0;39m [36mc.s.c.s.m.UserInfoMapper.selectJoinList [0;39m [2m:[0;39m <==      Total: 1
[2m2025-08-04 17:19:50.330[0;39m [32mDEBUG[0;39m [35m1312[0;39m [2m---[0;39m [2m[io-8082-exec-10][0;39m [36m.c.c.m.i.CustomDataPermissionInterceptor[0;39m [2m:[0;39m original SQL: SELECT    ID,NAME,YZGBC,YZDGS,KYGGWS,KSQSL,SFQYKCHLX    FROM  SYT_QGZX_JOB_TYPE
[2m2025-08-04 17:19:50.338[0;39m [32mDEBUG[0;39m [35m1312[0;39m [2m---[0;39m [2m[io-8082-exec-10][0;39m [36m.c.c.m.i.CustomDataPermissionInterceptor[0;39m [2m:[0;39m SQL to parse, SQL: SELECT    ID,NAME,YZGBC,YZDGS,KYGGWS,KSQSL,SFQYKCHLX    FROM  SYT_QGZX_JOB_TYPE
[2m2025-08-04 17:19:50.340[0;39m [32mDEBUG[0;39m [35m1312[0;39m [2m---[0;39m [2m[io-8082-exec-10][0;39m [36m.c.c.m.i.CustomDataPermissionInterceptor[0;39m [2m:[0;39m parse the finished SQL: SELECT ID, NAME, YZGBC, YZDGS, KYGGWS, KSQSL, SFQYKCHLX FROM SYT_QGZX_JOB_TYPE
[2m2025-08-04 17:19:50.351[0;39m [32mDEBUG[0;39m [35m1312[0;39m [2m---[0;39m [2m[io-8082-exec-10][0;39m [36mc.s.p.w.m.Q.selectList_mpCount          [0;39m [2m:[0;39m ==>  Preparing: SELECT COUNT(*) AS total FROM SYT_QGZX_JOB_TYPE
[2m2025-08-04 17:19:50.351[0;39m [32mDEBUG[0;39m [35m1312[0;39m [2m---[0;39m [2m[io-8082-exec-10][0;39m [36mc.s.p.w.m.Q.selectList_mpCount          [0;39m [2m:[0;39m ==> Parameters: 
[2m2025-08-04 17:19:51.026[0;39m [32mDEBUG[0;39m [35m1312[0;39m [2m---[0;39m [2m[io-8082-exec-10][0;39m [36mc.s.p.w.m.Q.selectList_mpCount          [0;39m [2m:[0;39m <==      Total: 1
[2m2025-08-04 17:19:51.028[0;39m [32mDEBUG[0;39m [35m1312[0;39m [2m---[0;39m [2m[io-8082-exec-10][0;39m [36mc.s.p.w.m.QgzxJobTypeMapper.selectList  [0;39m [2m:[0;39m ==>  Preparing: SELECT * FROM ( SELECT TMP.*, ROWNUM ROW_ID FROM ( SELECT ID, NAME, YZGBC, YZDGS, KYGGWS, KSQSL, SFQYKCHLX FROM SYT_QGZX_JOB_TYPE ) TMP WHERE ROWNUM <=?) WHERE ROW_ID > ?
[2m2025-08-04 17:19:51.029[0;39m [32mDEBUG[0;39m [35m1312[0;39m [2m---[0;39m [2m[io-8082-exec-10][0;39m [36mc.s.p.w.m.QgzxJobTypeMapper.selectList  [0;39m [2m:[0;39m ==> Parameters: 10(Long), 0(Long)
[2m2025-08-04 17:19:51.153[0;39m [32mDEBUG[0;39m [35m1312[0;39m [2m---[0;39m [2m[io-8082-exec-10][0;39m [36mc.s.p.w.m.QgzxJobTypeMapper.selectList  [0;39m [2m:[0;39m <==      Total: 4
[2m2025-08-04 17:20:33.785[0;39m [32mDEBUG[0;39m [35m1312[0;39m [2m---[0;39m [2m[nio-8082-exec-9][0;39m [36m.c.c.m.i.CustomDataPermissionInterceptor[0;39m [2m:[0;39m original SQL: SELECT    ID,NAME,PROJECT_ID,YEAR,CREATE_TIME,MODULE_CODE    FROM  SYT_WORKFLOW         WHERE  (project_id IS NULL AND module_code IS NULL)
[2m2025-08-04 17:20:33.800[0;39m [32mDEBUG[0;39m [35m1312[0;39m [2m---[0;39m [2m[nio-8082-exec-9][0;39m [36m.c.c.m.i.CustomDataPermissionInterceptor[0;39m [2m:[0;39m SQL to parse, SQL: SELECT    ID,NAME,PROJECT_ID,YEAR,CREATE_TIME,MODULE_CODE    FROM  SYT_WORKFLOW         WHERE  (project_id IS NULL AND module_code IS NULL)
[2m2025-08-04 17:20:33.802[0;39m [32mDEBUG[0;39m [35m1312[0;39m [2m---[0;39m [2m[nio-8082-exec-9][0;39m [36m.c.c.m.i.CustomDataPermissionInterceptor[0;39m [2m:[0;39m parse the finished SQL: SELECT ID, NAME, PROJECT_ID, YEAR, CREATE_TIME, MODULE_CODE FROM SYT_WORKFLOW WHERE (project_id IS NULL AND module_code IS NULL)
[2m2025-08-04 17:20:33.811[0;39m [32mDEBUG[0;39m [35m1312[0;39m [2m---[0;39m [2m[nio-8082-exec-9][0;39m [36mc.s.p.w.m.W.selectList_mpCount          [0;39m [2m:[0;39m ==>  Preparing: SELECT COUNT(*) AS total FROM SYT_WORKFLOW WHERE (project_id IS NULL AND module_code IS NULL)
[2m2025-08-04 17:20:33.813[0;39m [32mDEBUG[0;39m [35m1312[0;39m [2m---[0;39m [2m[nio-8082-exec-9][0;39m [36mc.s.p.w.m.W.selectList_mpCount          [0;39m [2m:[0;39m ==> Parameters: 
[2m2025-08-04 17:20:33.903[0;39m [32mDEBUG[0;39m [35m1312[0;39m [2m---[0;39m [2m[nio-8082-exec-9][0;39m [36mc.s.p.w.m.W.selectList_mpCount          [0;39m [2m:[0;39m <==      Total: 1
[2m2025-08-04 17:20:33.914[0;39m [32mDEBUG[0;39m [35m1312[0;39m [2m---[0;39m [2m[nio-8082-exec-9][0;39m [36mc.s.p.w.m.WorkflowMapper.selectList     [0;39m [2m:[0;39m ==>  Preparing: SELECT * FROM ( SELECT TMP.*, ROWNUM ROW_ID FROM ( SELECT ID, NAME, PROJECT_ID, YEAR, CREATE_TIME, MODULE_CODE FROM SYT_WORKFLOW WHERE (project_id IS NULL AND module_code IS NULL) ORDER BY create_time ASC ) TMP WHERE ROWNUM <=?) WHERE ROW_ID > ?
[2m2025-08-04 17:20:33.914[0;39m [32mDEBUG[0;39m [35m1312[0;39m [2m---[0;39m [2m[nio-8082-exec-9][0;39m [36mc.s.p.w.m.WorkflowMapper.selectList     [0;39m [2m:[0;39m ==> Parameters: 10(Long), 0(Long)
[2m2025-08-04 17:20:34.011[0;39m [32mDEBUG[0;39m [35m1312[0;39m [2m---[0;39m [2m[nio-8082-exec-9][0;39m [36mc.s.p.w.m.WorkflowMapper.selectList     [0;39m [2m:[0;39m <==      Total: 3
[2m2025-08-04 17:20:50.492[0;39m [32mDEBUG[0;39m [35m1312[0;39m [2m---[0;39m [2m[nio-8082-exec-2][0;39m [36m.c.c.m.i.CustomDataPermissionInterceptor[0;39m [2m:[0;39m original SQL: SELECT    ID,PARAM_NAME,PARAM_VALUE,PARAM_MODE,DESCRIPTION,ATTACHMENT,create_time    FROM  SYT_SYS_PARAM         WHERE  (param_mode = ?) ORDER BY create_time DESC
[2m2025-08-04 17:20:50.570[0;39m [32mDEBUG[0;39m [35m1312[0;39m [2m---[0;39m [2m[nio-8082-exec-2][0;39m [36m.c.c.m.i.CustomDataPermissionInterceptor[0;39m [2m:[0;39m SQL to parse, SQL: SELECT    ID,PARAM_NAME,PARAM_VALUE,PARAM_MODE,DESCRIPTION,ATTACHMENT,create_time    FROM  SYT_SYS_PARAM         WHERE  (param_mode = ?) ORDER BY create_time DESC
[2m2025-08-04 17:20:50.577[0;39m [32mDEBUG[0;39m [35m1312[0;39m [2m---[0;39m [2m[nio-8082-exec-2][0;39m [36m.c.c.m.i.CustomDataPermissionInterceptor[0;39m [2m:[0;39m parse the finished SQL: SELECT ID, PARAM_NAME, PARAM_VALUE, PARAM_MODE, DESCRIPTION, ATTACHMENT, create_time FROM SYT_SYS_PARAM WHERE (param_mode = ?) ORDER BY create_time DESC
[2m2025-08-04 17:20:50.601[0;39m [32mDEBUG[0;39m [35m1312[0;39m [2m---[0;39m [2m[nio-8082-exec-2][0;39m [36mc.s.c.s.m.SysParamMapper.selectList     [0;39m [2m:[0;39m ==>  Preparing: SELECT ID, PARAM_NAME, PARAM_VALUE, PARAM_MODE, DESCRIPTION, ATTACHMENT, create_time FROM SYT_SYS_PARAM WHERE (param_mode = ?) ORDER BY create_time DESC
[2m2025-08-04 17:20:50.621[0;39m [32mDEBUG[0;39m [35m1312[0;39m [2m---[0;39m [2m[nio-8082-exec-2][0;39m [36mc.s.c.s.m.SysParamMapper.selectList     [0;39m [2m:[0;39m ==> Parameters: system-info(String)
[2m2025-08-04 17:20:52.191[0;39m [32mDEBUG[0;39m [35m1312[0;39m [2m---[0;39m [2m[nio-8082-exec-2][0;39m [36mc.s.c.s.m.SysParamMapper.selectList     [0;39m [2m:[0;39m <==      Total: 0
[2m2025-08-04 17:20:54.646[0;39m [32mDEBUG[0;39m [35m1312[0;39m [2m---[0;39m [2m[nio-8082-exec-6][0;39m [36m.c.c.m.i.CustomDataPermissionInterceptor[0;39m [2m:[0;39m original SQL: SELECT     t.XGH,t.BZ1,t.BZ2,t.BZ3,t.BZ4,t.BZ5,t.BZ6,t.BZ7,t.BZ8,t.BZ9,t.BZ10,t.BZ11,t.BZ12,t.BZ13,t.BZ14,t.BZ15,t.BZ16,t.BZ17,t.BZ18,t.BZ19,t.BZ20,t.BZ21,t.BZ22,t.BZ23,t.BZ24,t.BZ25,t.BZ26,t.BZ27,t.BZ28,t.BZ29,t.BZ30,t.BZ31,t.BZ32,t.BZ33,t.BZ34,t.BZ35,t.BZ36,t.BZ37,t.BZ38,t.BZ39,t.BZ40,t.BZ41,t.BZ42,t.BZ43,t.BZ44,t.BZ45,t.BZ46,t.BZ47,t.BZ48,t.BZ49,t.BZ50,t.BZ51,t.BZ52,t.BZ53,t.BZ54,t.BZ55,t.BZ56,t.BZ57,t.BZ58,t.BZ59,t.BZ60,t.BZ61,t.BZ62,t.BZ63,t.BZ64,t.BZ65,t.BZ66,t.BZ67,t.BZ68,t.BZ69,t.BZ70,t.BZ71,t.BZ72,t.BZ73,t.BZ74,t.BZ75,t.BZ76,t.BZ77,t.BZ78,t.BZ79,t.BZ80,t.BZ81,t.BZ82,t.BZ83,t.BZ84,t.BZ85,t.BZ86,t.BZ87,t.BZ88,t.BZ89,t.BZ90,t.BZ91,t.BZ92,t.BZ93,t.BZ94,t.BZ95,t.BZ96,t.BZ97,t.BZ98,t.BZ99,t.BZ100,t.XM,t.XB,t.SJH,t.CSRQ,t.JG,t.MZMC,t.ZZMMMC,t.XQMC,t.ZJHM,t.ZJLX,t.XSLB,t.XZLX,t.RYZTID,t.USER_TYPE,t.PHOTO,t2.ID,t2.PYCCID,t2.NJID,t2.XYID,t2.ZYID,t2.BJID,t2.XGH AS joina_XGH   FROM SYT_USER_INFO  t    LEFT JOIN SYT_CODE_XSZT t1 ON (t1.ID = t.RYZTID) LEFT JOIN SYT_USER_ORG_MAP t2 ON (t2.XGH = t.XGH)     WHERE   (t.xgh IN (?) AND t2.xgh IN (?)) ORDER BY t.xgh DESC
[2m2025-08-04 17:20:54.697[0;39m [32mDEBUG[0;39m [35m1312[0;39m [2m---[0;39m [2m[nio-8082-exec-6][0;39m [36m.c.c.m.i.CustomDataPermissionInterceptor[0;39m [2m:[0;39m SQL to parse, SQL: SELECT     t.XGH,t.BZ1,t.BZ2,t.BZ3,t.BZ4,t.BZ5,t.BZ6,t.BZ7,t.BZ8,t.BZ9,t.BZ10,t.BZ11,t.BZ12,t.BZ13,t.BZ14,t.BZ15,t.BZ16,t.BZ17,t.BZ18,t.BZ19,t.BZ20,t.BZ21,t.BZ22,t.BZ23,t.BZ24,t.BZ25,t.BZ26,t.BZ27,t.BZ28,t.BZ29,t.BZ30,t.BZ31,t.BZ32,t.BZ33,t.BZ34,t.BZ35,t.BZ36,t.BZ37,t.BZ38,t.BZ39,t.BZ40,t.BZ41,t.BZ42,t.BZ43,t.BZ44,t.BZ45,t.BZ46,t.BZ47,t.BZ48,t.BZ49,t.BZ50,t.BZ51,t.BZ52,t.BZ53,t.BZ54,t.BZ55,t.BZ56,t.BZ57,t.BZ58,t.BZ59,t.BZ60,t.BZ61,t.BZ62,t.BZ63,t.BZ64,t.BZ65,t.BZ66,t.BZ67,t.BZ68,t.BZ69,t.BZ70,t.BZ71,t.BZ72,t.BZ73,t.BZ74,t.BZ75,t.BZ76,t.BZ77,t.BZ78,t.BZ79,t.BZ80,t.BZ81,t.BZ82,t.BZ83,t.BZ84,t.BZ85,t.BZ86,t.BZ87,t.BZ88,t.BZ89,t.BZ90,t.BZ91,t.BZ92,t.BZ93,t.BZ94,t.BZ95,t.BZ96,t.BZ97,t.BZ98,t.BZ99,t.BZ100,t.XM,t.XB,t.SJH,t.CSRQ,t.JG,t.MZMC,t.ZZMMMC,t.XQMC,t.ZJHM,t.ZJLX,t.XSLB,t.XZLX,t.RYZTID,t.USER_TYPE,t.PHOTO,t2.ID,t2.PYCCID,t2.NJID,t2.XYID,t2.ZYID,t2.BJID,t2.XGH AS joina_XGH   FROM SYT_USER_INFO  t    LEFT JOIN SYT_CODE_XSZT t1 ON (t1.ID = t.RYZTID) LEFT JOIN SYT_USER_ORG_MAP t2 ON (t2.XGH = t.XGH)     WHERE   (t.xgh IN (?) AND t2.xgh IN (?)) ORDER BY t.xgh DESC
[2m2025-08-04 17:20:54.703[0;39m [32mDEBUG[0;39m [35m1312[0;39m [2m---[0;39m [2m[nio-8082-exec-6][0;39m [36m.c.c.m.i.CustomDataPermissionInterceptor[0;39m [2m:[0;39m parse the finished SQL: SELECT t.XGH, t.BZ1, t.BZ2, t.BZ3, t.BZ4, t.BZ5, t.BZ6, t.BZ7, t.BZ8, t.BZ9, t.BZ10, t.BZ11, t.BZ12, t.BZ13, t.BZ14, t.BZ15, t.BZ16, t.BZ17, t.BZ18, t.BZ19, t.BZ20, t.BZ21, t.BZ22, t.BZ23, t.BZ24, t.BZ25, t.BZ26, t.BZ27, t.BZ28, t.BZ29, t.BZ30, t.BZ31, t.BZ32, t.BZ33, t.BZ34, t.BZ35, t.BZ36, t.BZ37, t.BZ38, t.BZ39, t.BZ40, t.BZ41, t.BZ42, t.BZ43, t.BZ44, t.BZ45, t.BZ46, t.BZ47, t.BZ48, t.BZ49, t.BZ50, t.BZ51, t.BZ52, t.BZ53, t.BZ54, t.BZ55, t.BZ56, t.BZ57, t.BZ58, t.BZ59, t.BZ60, t.BZ61, t.BZ62, t.BZ63, t.BZ64, t.BZ65, t.BZ66, t.BZ67, t.BZ68, t.BZ69, t.BZ70, t.BZ71, t.BZ72, t.BZ73, t.BZ74, t.BZ75, t.BZ76, t.BZ77, t.BZ78, t.BZ79, t.BZ80, t.BZ81, t.BZ82, t.BZ83, t.BZ84, t.BZ85, t.BZ86, t.BZ87, t.BZ88, t.BZ89, t.BZ90, t.BZ91, t.BZ92, t.BZ93, t.BZ94, t.BZ95, t.BZ96, t.BZ97, t.BZ98, t.BZ99, t.BZ100, t.XM, t.XB, t.SJH, t.CSRQ, t.JG, t.MZMC, t.ZZMMMC, t.XQMC, t.ZJHM, t.ZJLX, t.XSLB, t.XZLX, t.RYZTID, t.USER_TYPE, t.PHOTO, t2.ID, t2.PYCCID, t2.NJID, t2.XYID, t2.ZYID, t2.BJID, t2.XGH AS joina_XGH FROM SYT_USER_INFO t LEFT JOIN SYT_CODE_XSZT t1 ON (t1.ID = t.RYZTID) LEFT JOIN SYT_USER_ORG_MAP t2 ON (t2.XGH = t.XGH) WHERE (t.xgh IN (?) AND t2.xgh IN (?)) ORDER BY t.xgh DESC
[2m2025-08-04 17:20:54.705[0;39m [32mDEBUG[0;39m [35m1312[0;39m [2m---[0;39m [2m[nio-8082-exec-6][0;39m [36mc.s.c.s.m.UserInfoMapper.selectJoinList [0;39m [2m:[0;39m ==>  Preparing: SELECT t.XGH, t.BZ1, t.BZ2, t.BZ3, t.BZ4, t.BZ5, t.BZ6, t.BZ7, t.BZ8, t.BZ9, t.BZ10, t.BZ11, t.BZ12, t.BZ13, t.BZ14, t.BZ15, t.BZ16, t.BZ17, t.BZ18, t.BZ19, t.BZ20, t.BZ21, t.BZ22, t.BZ23, t.BZ24, t.BZ25, t.BZ26, t.BZ27, t.BZ28, t.BZ29, t.BZ30, t.BZ31, t.BZ32, t.BZ33, t.BZ34, t.BZ35, t.BZ36, t.BZ37, t.BZ38, t.BZ39, t.BZ40, t.BZ41, t.BZ42, t.BZ43, t.BZ44, t.BZ45, t.BZ46, t.BZ47, t.BZ48, t.BZ49, t.BZ50, t.BZ51, t.BZ52, t.BZ53, t.BZ54, t.BZ55, t.BZ56, t.BZ57, t.BZ58, t.BZ59, t.BZ60, t.BZ61, t.BZ62, t.BZ63, t.BZ64, t.BZ65, t.BZ66, t.BZ67, t.BZ68, t.BZ69, t.BZ70, t.BZ71, t.BZ72, t.BZ73, t.BZ74, t.BZ75, t.BZ76, t.BZ77, t.BZ78, t.BZ79, t.BZ80, t.BZ81, t.BZ82, t.BZ83, t.BZ84, t.BZ85, t.BZ86, t.BZ87, t.BZ88, t.BZ89, t.BZ90, t.BZ91, t.BZ92, t.BZ93, t.BZ94, t.BZ95, t.BZ96, t.BZ97, t.BZ98, t.BZ99, t.BZ100, t.XM, t.XB, t.SJH, t.CSRQ, t.JG, t.MZMC, t.ZZMMMC, t.XQMC, t.ZJHM, t.ZJLX, t.XSLB, t.XZLX, t.RYZTID, t.USER_TYPE, t.PHOTO, t2.ID, t2.PYCCID, t2.NJID, t2.XYID, t2.ZYID, t2.BJID, t2.XGH AS joina_XGH FROM SYT_USER_INFO t LEFT JOIN SYT_CODE_XSZT t1 ON (t1.ID = t.RYZTID) LEFT JOIN SYT_USER_ORG_MAP t2 ON (t2.XGH = t.XGH) WHERE (t.xgh IN (?) AND t2.xgh IN (?)) ORDER BY t.xgh DESC
[2m2025-08-04 17:20:54.707[0;39m [32mDEBUG[0;39m [35m1312[0;39m [2m---[0;39m [2m[nio-8082-exec-6][0;39m [36mc.s.c.s.m.UserInfoMapper.selectJoinList [0;39m [2m:[0;39m ==> Parameters: admin(String), admin(String)
[2m2025-08-04 17:20:54.810[0;39m [32mDEBUG[0;39m [35m1312[0;39m [2m---[0;39m [2m[nio-8082-exec-6][0;39m [36mc.s.c.s.m.UserInfoMapper.selectJoinList [0;39m [2m:[0;39m <==      Total: 1
