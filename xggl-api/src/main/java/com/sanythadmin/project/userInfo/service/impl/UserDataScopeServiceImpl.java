package com.sanythadmin.project.userInfo.service.impl;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.toolkit.support.SFunction;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.github.yulichang.wrapper.MPJLambdaWrapper;
import com.sanythadmin.common.core.Constants;
import com.sanythadmin.common.core.config.AsyncConfig;
import com.sanythadmin.common.core.mybatisplus.wrapper.MyMPJLambdaWrapper;
import com.sanythadmin.common.core.utils.CommonUtil;
import com.sanythadmin.common.core.utils.ExcelUtils;
import com.sanythadmin.common.core.utils.SpringContextUtil;
import com.sanythadmin.common.core.utils.SqlUtil;
import com.sanythadmin.common.core.web.ExcelImportError;
import com.sanythadmin.common.core.web.PageParam;
import com.sanythadmin.common.enums.UserType;
import com.sanythadmin.common.system.entity.BaseUserInfo;
import com.sanythadmin.common.system.entity.SysAccountRole;
import com.sanythadmin.common.system.entity.SysRole;
import com.sanythadmin.common.system.entity.UserInfo;
import com.sanythadmin.common.system.mapper.SysRoleMapper;
import com.sanythadmin.common.system.mapper.UserInfoMapper;
import com.sanythadmin.common.system.param.UserInfoParam;
import com.sanythadmin.common.system.service.UserInfoService;
import com.sanythadmin.project.code.entity.CodeBjb;
import com.sanythadmin.project.code.entity.CodeCommon;
import com.sanythadmin.project.code.entity.CodeDwb;
import com.sanythadmin.project.code.entity.CodeZyb;
import com.sanythadmin.project.code.mapper.CodeBjbMapper;
import com.sanythadmin.project.code.mapper.CodeCommonMapper;
import com.sanythadmin.project.code.mapper.CodeDwbMapper;
import com.sanythadmin.project.code.mapper.CodeZybMapper;
import com.sanythadmin.project.code.service.CodeBjbService;
import com.sanythadmin.project.code.service.CodeZybService;
import com.sanythadmin.project.userInfo.dto.DataScopeCache;
import com.sanythadmin.project.userInfo.dto.UserDataScopeDTO;
import com.sanythadmin.project.userInfo.entity.UserDataScope;
import com.sanythadmin.project.userInfo.mapper.UserDataScopeMapper;
import com.sanythadmin.project.userInfo.param.UserDataScopeParam;
import com.sanythadmin.project.userInfo.service.UserDataScopeService;
import com.sanythadmin.project.userInfo.vo.InstructorsVO;
import lombok.AllArgsConstructor;
import lombok.Data;
import org.springframework.data.redis.core.RedisTemplate;
import org.springframework.scheduling.annotation.Async;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.CollectionUtils;
import org.springframework.util.StringUtils;

import java.util.*;
import java.util.concurrent.TimeUnit;
import java.util.function.Function;
import java.util.stream.Collectors;

/**
 * 用户数据权限Service实现
 *
 * <AUTHOR>
 * @since 2024-04-22 13:37:34
 */
@AllArgsConstructor
@Service
public class UserDataScopeServiceImpl extends ServiceImpl<UserDataScopeMapper, UserDataScope> implements UserDataScopeService {
    private final UserDataScopeMapper userDataScopeMapper;
    private final CodeBjbMapper codeBjbMapper;
    private final CodeZybMapper codeZybMapper;
    private final CodeDwbMapper codeDwbMapper;
    private final CodeCommonMapper codeCommonMapper;
    private final UserInfoMapper userInfoMapper;
    private final RedisTemplate<String, Object> redisTemplate;
    private final SysRoleMapper roleMapper;
    private final CodeBjbService codeBjbService;
    private final CodeZybService codeZybService;

    @Async(AsyncConfig.ASYNC_EXECUTOR)
    @Transactional
    @Override
    public void edit(UserDataScope object) {
        List<UserDataScope> scopes = new ArrayList<>();
        if (StringUtils.hasText(object.getXgh())) {
            addDataScopeByUser(scopes, object);
        } else if (StringUtils.hasText(object.getBjid())) {
            addDataScopeByClass(scopes, object);
        } else if (StringUtils.hasText(object.getZyid())) {
            addDataScopeByMajor(scopes, object);
        } else if (StringUtils.hasText(object.getXyid())) {
            addDataScopeByDept(scopes, object);
        } else if (StringUtils.hasText(object.getNjid()) || StringUtils.hasText(object.getPyccid())) {
            addDataScope(scopes, object);
        }

        userDataScopeMapper.delete(new LambdaQueryWrapper<UserDataScope>()
                .eq(UserDataScope::getGlzXgh, object.getGlzXgh())
                .eq(UserDataScope::getRoleId, object.getRoleId()));
        scopes.forEach(userDataScopeMapper::insert);
        String key = String.format(Constants.USER_DATA_SCOPE_KEY, object.getGlzXgh());
        redisTemplate.delete(key);
        String countKey = String.format(Constants.USER_DATA_SCOPE_COUNT_KEY, object.getGlzXgh());
        redisTemplate.delete(countKey);
    }

    @Override
    public List<UserDataScope> queryList(UserDataScopeParam param) {
        PageParam<UserDataScope, UserDataScopeParam> page = new PageParam<>(param);
        //page.setDefaultOrder("create_time desc");
        return userDataScopeMapper.selectList(page.getOrderWrapper());
    }

    private <T extends UserDataScope> Set<String> filterhasText2Set(List<T> list, Function<? super T, ? extends String> mapper) {
        return list.stream().map(mapper).filter(StringUtils::hasText).collect(Collectors.toSet());
    }

    @Override
    public <T extends UserDataScope> UserDataScope list2Single(List<T> list, boolean showAll) {
        if (CollectionUtils.isEmpty(list)) return null;
        Set<String> classIds = filterhasText2Set(list, T::getBjid);
        Set<String> majorIds = filterhasText2Set(list, T::getZyid);
        Set<String> deptIds = filterhasText2Set(list, T::getXyid);
        Set<String> eduLevelIds = filterhasText2Set(list, T::getPyccid);
        Set<String> gradeIds = filterhasText2Set(list, T::getNjid);
        Set<String> username = filterhasText2Set(list, T::getXgh);
        Set<String> roleIds = filterhasText2Set(list, T::getRoleId);
        Set<String> managerUsername = filterhasText2Set(list, T::getGlzXgh);

        UserDataScope dataScope = getUserDataScope(CommonUtil.strJoin(eduLevelIds), CommonUtil.strJoin(classIds),
                CommonUtil.strJoin(majorIds), CommonUtil.strJoin(deptIds), CommonUtil.strJoin(gradeIds),
                CommonUtil.strJoin(roleIds), CommonUtil.strJoin(managerUsername),
                CommonUtil.strJoin(username));

        if (showAll) {
            if (StringUtils.hasText(dataScope.getBjid())) {
                List<CodeBjb> classList = codeBjbMapper.selectList(new LambdaQueryWrapper<CodeBjb>().in(CodeBjb::getId, classIds));
                majorIds = classList.stream().map(CodeBjb::getZyid).collect(Collectors.toSet());
                deptIds = classList.stream().map(CodeBjb::getXyid).collect(Collectors.toSet());
                dataScope.setZyid(CommonUtil.strJoin(majorIds));
                dataScope.setXyid(CommonUtil.strJoin(deptIds));
            } else if (StringUtils.hasText(dataScope.getZyid())) {
                List<CodeZyb> majorList = codeZybMapper.selectList(new LambdaQueryWrapper<CodeZyb>().in(CodeZyb::getId, majorIds));
                deptIds = majorList.stream().map(CodeZyb::getXyid).collect(Collectors.toSet());
                dataScope.setXyid(CommonUtil.strJoin(deptIds));
            }
        }
        return dataScope;
    }

    private void addDataScopeByUser(List<UserDataScope> userDataScopes, UserDataScope dataScope) {
        String[] strings = CommonUtil.split(dataScope.getXgh());
        List<UserInfo> userInfos = userInfoMapper.selectList(new LambdaQueryWrapper<UserInfo>().in(UserInfo::getXgh, strings));
        Map<String, UserInfo> userInfoMap = userInfos.stream().collect((Collectors.toMap(UserInfo::getXgh, Function.identity())));
        for (String xgh : strings) {
            UserInfo userInfo = userInfoMap.get(xgh);
            UserDataScope scope = getUserDataScope(userInfo.getPyccid(), userInfo.getBjid(), userInfo.getZyid(),
                    userInfo.getXyid(), userInfo.getNjid(), dataScope.getRoleId(), dataScope.getGlzXgh(), xgh);
            userDataScopes.add(scope);
        }
    }

    private void addDataScope(List<UserDataScope> userDataScopes, UserDataScope dataScope) {
        Result result = addDataScopeEduLevelOrGrade(dataScope, null);
        result.strList.forEach(str -> {
            String[] strings = CommonUtil.split(str);
            String gradeId = null;
            String eduLevelId = null;
            if (result.addEduLevel && result.addGrade) {
                eduLevelId = strings[0];
                gradeId = strings[1];
            } else if (result.addEduLevel) {
                eduLevelId = strings[0];
            } else if (result.addGrade) {
                gradeId = strings[0];
            }
            userDataScopes.add(getUserDataScope(eduLevelId, null, null, null,
                    gradeId, dataScope.getRoleId(), dataScope.getGlzXgh(), dataScope.getXgh()));
        });
    }

    private void addDataScopeByDept(List<UserDataScope> userDataScopes, UserDataScope dataScope) {
        List<String> strList = Arrays.asList(CommonUtil.split(dataScope.getXyid()));
        Result result = addDataScopeEduLevelOrGrade(dataScope, strList);
        result.strList.forEach(str -> {
            TempObj tempObj = new TempObj(result, CommonUtil.split(str));
            userDataScopes.add(getUserDataScope(tempObj.eduLevelId, null, null, tempObj.id, tempObj.gradeId
                    , dataScope.getRoleId(), dataScope.getGlzXgh(), dataScope.getXgh()));
        });
    }

    private void addDataScopeByMajor(List<UserDataScope> userDataScopes, UserDataScope dataScope) {
        List<String> strList = Arrays.asList(CommonUtil.split(dataScope.getZyid()));
        Result result = addDataScopeEduLevelOrGrade(dataScope, strList);
        strList = result.strList;
        for (String str : strList) {
            TempObj tempObj = new TempObj(result, CommonUtil.split(str));
            userDataScopes.add(getUserDataScope(tempObj.eduLevelId, null, tempObj.id, null,
                    tempObj.gradeId, dataScope.getRoleId(), dataScope.getGlzXgh(), dataScope.getXgh()));
        }
    }

    private Result addDataScopeEduLevelOrGrade(UserDataScope dataScope, List<String> strList) {
        if (strList == null) strList = new ArrayList<>();
        boolean addEduLevel = false;
        if (StringUtils.hasText(dataScope.getPyccid())) {
            strList = addDataScope(dataScope.getPyccid(), strList);
            addEduLevel = true;
        }

        boolean addGrade = false;
        if (StringUtils.hasText(dataScope.getNjid())) {
            strList = addDataScope(dataScope.getNjid(), strList);
            addGrade = true;
        }
        return new Result(strList, addEduLevel, addGrade);
    }

    public List<String> addDataScope(String ids, List<String> strList) {
        List<String> list = Arrays.asList(CommonUtil.split(ids));
        if (CollectionUtils.isEmpty(strList)) {
            strList = list;
        } else {
            strList = strList.stream().flatMap(str -> list.stream().map(id ->
                            (str.endsWith(",") ? str.concat(id) : str.concat(",").concat(id))))
                    .collect(Collectors.toList());
        }
        return strList;
    }

    private void addDataScopeByClass(List<UserDataScope> userDataScopes, UserDataScope dataScope) {
        String[] array = CommonUtil.split(dataScope.getBjid());
        List<String> strList = Arrays.asList(array);
        Result result = addDataScopeEduLevelOrGrade(dataScope, strList);
        strList = result.strList;
        for (String str : strList) {
            TempObj tempObj = new TempObj(result, CommonUtil.split(str));
            userDataScopes.add(getUserDataScope(tempObj.eduLevelId, tempObj.id, null, null,
                    tempObj.gradeId, dataScope.getRoleId(), dataScope.getGlzXgh(), dataScope.getXgh()));
        }
    }

    private static UserDataScope getUserDataScope(String eduLevelId, String classId, String majorId, String deptId,
                                                  String gradeId, String roleId, String glzXgh, String xgh) {
        UserDataScope userDataScope = new UserDataScope();
        userDataScope.setBjid(classId);
        userDataScope.setZyid(majorId);
        userDataScope.setXyid(deptId);
        userDataScope.setNjid(gradeId);
        userDataScope.setPyccid(eduLevelId);
        userDataScope.setRoleId(roleId);
        userDataScope.setGlzXgh(glzXgh);
        userDataScope.setXgh(xgh);
        return userDataScope;
    }

    private class Result {
        public List<String> strList;
        public boolean addEduLevel;
        public boolean addGrade;

        public Result(List<String> strList, boolean addEduLevel, boolean addGrade) {
            this.strList = strList;
            this.addEduLevel = addEduLevel;
            this.addGrade = addGrade;
        }
    }

    private static class TempObj {
        private final String id;
        public String gradeId;
        public String eduLevelId;

        public TempObj(Result result, String[] strings) {
            id = strings[0];
            if (strings.length == 3) {
                this.eduLevelId = strings[1];
                this.gradeId = strings[2];
            } else {
                if (result.addEduLevel) this.eduLevelId = strings[1];
                if (result.addGrade) this.gradeId = strings[1];
            }
        }
    }

    @Override
    public MPJLambdaWrapper<UserDataScope> buildUserScopeQuery(BaseUserInfo userInfo, String roleScope) {
        MPJLambdaWrapper<UserDataScope> wrapper = new MyMPJLambdaWrapper<UserDataScope, UserDataScopeParam>()
                .innerJoin(UserInfo.class, UserInfo::getXgh, UserDataScope::getGlzXgh)
                .leftJoin(SysRole.class, SysRole::getId, UserDataScope::getRoleId)
                .eq(SysRole::getRoleScope, roleScope);
        appendQueryScope(wrapper, userInfo);
        return wrapper;
    }

    @Override
    public <T> void appendQueryScope(MPJLambdaWrapper<T> consumer, BaseUserInfo userInfo) {
        consumer.and(wrapper -> wrapper
                .in(UserDataScope::getXgh, (Object[]) CommonUtil.split(userInfo.getXgh()))
                .or(w2 -> w2.isNull(UserDataScope::getXgh).and(w3 -> w3.in(UserDataScope::getBjid, (Object[]) CommonUtil.split(userInfo.getBjid()))
                                .or(w4 -> w4
                                        .isNull(UserDataScope::getBjid)
                                        .in(UserDataScope::getZyid, (Object[]) CommonUtil.split(userInfo.getZyid()))
                                )
                                .or(w5 -> w5
                                        .isNull(UserDataScope::getBjid)
                                        .isNull(UserDataScope::getZyid)
                                        .in(UserDataScope::getXyid, (Object[]) CommonUtil.split(userInfo.getXyid()))
                                )
                                .or(w6 -> w6
                                        .isNull(UserDataScope::getBjid)
                                        .isNull(UserDataScope::getZyid)
                                        .isNull(UserDataScope::getXyid)
                                        .in(UserDataScope::getNjid, (Object[]) CommonUtil.split(userInfo.getNjid()))
                                )
                                .or(w7 -> w7
                                        .isNull(UserDataScope::getBjid)
                                        .isNull(UserDataScope::getZyid)
                                        .isNull(UserDataScope::getXyid)
                                        .isNull(UserDataScope::getNjid)
                                        .in(UserDataScope::getPyccid, (Object[]) CommonUtil.split(userInfo.getPyccid()))
                                )
                        )
                )
        );
    }

    @Override
    public List<InstructorsVO> getStudentInstructors(BaseUserInfo userInfo, String roleScope, Collection<SFunction<UserInfo, ?>> selectFields) {
        MyMPJLambdaWrapper<UserInfo, UserInfoParam> wrapper = new MyMPJLambdaWrapper<>();
        wrapper.leftJoin(SysAccountRole.class, SysAccountRole::getUsername, UserInfo::getXgh)
                .leftJoin(SysRole.class, SysRole::getId, SysAccountRole::getRoleId);
        StringBuilder builder = new StringBuilder("SELECT 1 FROM SYT_USER_DATA_SCOPE DATA_SCOPE WHERE " + wrapper.getAlias() + ".XGH = DATA_SCOPE.GLZ_XGH AND ");
        MyMPJLambdaWrapper<UserDataScope, UserDataScopeParam> wrapper1 = new MyMPJLambdaWrapper<>();
        wrapper1.setAlias("DATA_SCOPE");
        appendQueryScope(wrapper1, userInfo);
        builder.append(SqlUtil.getRealSql(wrapper1));
        wrapper.exists(builder.toString());
        if (!CollectionUtils.isEmpty(selectFields)) {
            wrapper.select(selectFields);
        } else {
            wrapper.selectAll(UserInfo.class);
        }
        wrapper.selectAs(SysRole::getName, InstructorsVO::getRoleName)
                .eq(SysRole::getRoleScope, roleScope);
        return userInfoMapper.selectJoinList(InstructorsVO.class, wrapper);
    }

    @Override
    public List<UserDataScope> dataImportCheck(List<UserDataScopeDTO> infos) {
        List<ExcelImportError> errors = new ArrayList<>();
        HashSet<String> teacherSet = new HashSet<>();
        HashSet<String> studentSet = new HashSet<>();
        HashSet<String> classSet = new HashSet<>();
        HashSet<String> majorSet = new HashSet<>();
        HashSet<String> deptSet = new HashSet<>();
        HashSet<String> gradeSet = new HashSet<>();
        HashSet<String> eduLevelSet = new HashSet<>();
        HashSet<String> roleSet = new HashSet<>();
        for (int i = 0; i < infos.size(); i++) {
            UserDataScopeDTO dto = infos.get(i);
            if (dto.scopeIsEmpty()) {
                errors.add(ExcelImportError.data(i + 2, dto.getGlzXgh(), "教师带班/带生信息不可全部为空"));
                continue;
            }
            teacherSet.add(dto.getGlzXgh());
            roleSet.add(dto.getRoleId());
            if (StringUtils.hasText(dto.getXgh())) studentSet.add(dto.getXgh());
            if (StringUtils.hasText(dto.getBjid())) classSet.add(dto.getBjid());
            if (StringUtils.hasText(dto.getZyid())) majorSet.add(dto.getZyid());
            if (StringUtils.hasText(dto.getXyid())) deptSet.add(dto.getXyid());
            if (StringUtils.hasText(dto.getNjid())) gradeSet.add(dto.getNjid());
            if (StringUtils.hasText(dto.getPyccid())) eduLevelSet.add(dto.getPyccid());
        }

        List<String> objects = userInfoMapper.selectObjs(new LambdaQueryWrapper<UserInfo>()
                .select(UserInfo::getXgh).in(UserInfo::getUserType, UserType.STUDENT, UserType.TEACHER));
        checkImportData(teacherSet, objects, errors, "教师工号或学号不存在");
        if (!CollectionUtils.isEmpty(studentSet))
            checkImportData(studentSet, objects, errors, "教师工号或学号不存在");
        if (!CollectionUtils.isEmpty(roleSet)) {
            List<SysRole> roles = roleMapper.selectList(new LambdaQueryWrapper<SysRole>()
                    .select(SysRole::getName, SysRole::getId));
            Map<String, String> roleMap = roles.stream().collect(Collectors.toMap(SysRole::getName, SysRole::getId));
            boolean error = checkImportData(roleSet, roleMap.keySet(), errors, "角色不存在");
            if (!error)
                infos.forEach(info -> info.setRoleId(roleMap.get(info.getRoleId())));
        }

        if (!CollectionUtils.isEmpty(classSet)) {
            List<CodeBjb> codeList = codeBjbMapper.selectList(new LambdaQueryWrapper<>());
            Map<String, Long> countMap = codeList.stream().collect(Collectors.groupingBy(CodeBjb::getName, Collectors.counting()));
            List<String> codeNameRepeatList = countMap.keySet().stream().filter(name -> countMap.get(name) > 1).toList();
            Map<String, String> codeMap = CollectionUtils.isEmpty(codeNameRepeatList) ? codeList.stream().collect(Collectors.toMap(CodeBjb::getName, CodeBjb::getId))
                    : codeList.stream().filter(code -> !codeNameRepeatList.contains(code.getName())).collect(Collectors.toMap(CodeBjb::getName, CodeBjb::getId));
            codeMap.putAll(codeList.stream().collect(Collectors.toMap(CodeBjb::getCode, CodeBjb::getId)));
            boolean error = checkImportData(classSet, codeMap.keySet(), errors, "班级名称或代码不存在");
            if (!error) {
                HashSet<String> newSet = new HashSet<>(classSet);
                newSet.retainAll(codeNameRepeatList);
                if (!CollectionUtils.isEmpty(newSet)) {
                    infos.stream().filter(info -> newSet.contains(info.getBjid())).toList().forEach(info -> {
                        if (!StringUtils.hasText(info.getZyid()) || !StringUtils.hasText(info.getXyid())) {
                            errors.add(ExcelImportError.data(null, info.getBjid(), "系统内班级名称重复,请填写班级代码或增加专业和学院"));
                        } else {
                            CodeBjb codeBjb = codeBjbService.get(info.getBjid(), info.getZyid(), info.getXyid());
                            if (codeBjb == null) {
                                errors.add(ExcelImportError.data(null, info.getBjid(), "根据班级、专业、学院名称未找到班级信息"));
                            } else {
                                String string = codeBjb.getName() + codeBjb.getZyid() + codeBjb.getXyid();
                                info.setBjid(string);
                                codeMap.put(string, codeBjb.getId());
                            }
                        }
                    });
                }
                infos.forEach(info -> info.setBjid(codeMap.get(info.getBjid())));
            }
        }

        if (!CollectionUtils.isEmpty(majorSet)) {
            List<CodeZyb> codeList = codeZybMapper.selectList(new LambdaQueryWrapper<>());
            Map<String, Long> countMap = codeList.stream().collect(Collectors.groupingBy(CodeZyb::getName, Collectors.counting()));
            List<String> codeNameRepeatList = countMap.keySet().stream().filter(name -> countMap.get(name) > 1).toList();
            Map<String, String> codeMap = CollectionUtils.isEmpty(codeNameRepeatList) ? codeList.stream().collect(Collectors.toMap(CodeZyb::getName, CodeZyb::getId))
                    : codeList.stream().filter(code -> !codeNameRepeatList.contains(code.getName())).collect(Collectors.toMap(CodeZyb::getName, CodeZyb::getId));
            codeMap.putAll(codeList.stream().collect(Collectors.toMap(CodeZyb::getCode, CodeZyb::getId)));
            boolean error = checkImportData(majorSet, codeMap.keySet(), errors, "专业名称或代码不存在");
            if (!error) {
                HashSet<String> newSet = new HashSet<>(classSet);
                newSet.retainAll(codeNameRepeatList);
                if (!CollectionUtils.isEmpty(newSet)) {
                    infos.stream().filter(info -> newSet.contains(info.getZyid())).toList().forEach(info -> {
                        if (!StringUtils.hasText(info.getXyid())) {
                            errors.add(ExcelImportError.data(null, info.getBjid(), "系统内存在名称重复的专业,请填写学院代码或增加学院"));
                        } else {
                            CodeZyb codeZyb = codeZybService.get(info.getZyid(), info.getXyid());
                            if (codeZyb == null) {
                                errors.add(ExcelImportError.data(null, info.getBjid(), "根据专业、学院名称未找到专业信息"));
                            } else {
                                String string = codeZyb.getId() + codeZyb.getXyid();
                                info.setZyid(string);
                                codeMap.put(string, codeZyb.getId());
                            }
                        }
                    });
                }
                infos.forEach(info -> info.setZyid(codeMap.get(info.getZyid())));
            }
        }

        if (!CollectionUtils.isEmpty(deptSet)) {
            List<CodeDwb> codeList = codeDwbMapper.selectList(new LambdaQueryWrapper<>());
            Map<String, String> codeMap = codeList.stream().collect(Collectors.toMap(CodeDwb::getName, CodeDwb::getId));
            codeMap.putAll(codeList.stream().collect(Collectors.toMap(CodeDwb::getCode, CodeDwb::getId)));
            boolean error = checkImportData(deptSet, codeMap.keySet(), errors, "学院名称或代码不存在");
            if (!error)
                infos.forEach(info -> info.setXyid(codeMap.get(info.getXyid())));
        }


        if (!CollectionUtils.isEmpty(gradeSet)) {
            List<CodeCommon> codeList = codeCommonMapper.selectList(new LambdaQueryWrapper<CodeCommon>()
                    .eq(CodeCommon::getCodeType, "nj"));
            Map<String, String> codeMap = codeList.stream().collect(Collectors.toMap(CodeCommon::getName, CodeCommon::getId));
            codeMap.putAll(codeList.stream().collect(Collectors.toMap(CodeCommon::getCode, CodeCommon::getId)));
            boolean error = checkImportData(gradeSet, codeMap.keySet(), errors, "年级名称或代码不存在");
            if (!error)
                infos.forEach(info -> info.setNjid(codeMap.get(info.getNjid())));
        }

        if (!CollectionUtils.isEmpty(eduLevelSet)) {
            List<CodeCommon> codeList = codeCommonMapper.selectList(new LambdaQueryWrapper<CodeCommon>()
                    .eq(CodeCommon::getCodeType, "pycc"));
            Map<String, String> codeMap = codeList.stream().collect(Collectors.toMap(CodeCommon::getName, CodeCommon::getId));
            codeMap.putAll(codeList.stream().collect(Collectors.toMap(CodeCommon::getCode, CodeCommon::getId)));
            boolean error = checkImportData(eduLevelSet, codeMap.keySet(), errors, "培养层次名称或代码不存在");
            if (!error)
                infos.forEach(info -> info.setPyccid(codeMap.get(info.getPyccid())));
        }
        ExcelUtils.checkAndWriteImportDataErrorInfo(errors);
        List<UserDataScope> newData = new ArrayList<>();
        for (String str : teacherSet) {
            Set<String> roleIdSet = infos.stream().filter(info -> info.getGlzXgh().equals(str)).
                    map(UserDataScopeDTO::getRoleId).collect(Collectors.toSet());
            for (String roleId : roleIdSet) {
                List<UserDataScopeDTO> list = infos.stream().filter(info ->
                        info.getGlzXgh().equals(str) && info.getRoleId().equals(roleId)).toList();
                newData.add(list2Single(list, true));
            }
        }
        return newData;
    }

    @Override
    public DataScopeCache get(String username, String roleId) {
        String key = String.format(Constants.USER_DATA_SCOPE_KEY, username);
        DataScopeCache object = (DataScopeCache) redisTemplate.opsForValue().get(key);
        if (object == null) {
            UserInfo userInfo = userInfoMapper.selectById(username);
            UserDataScopeParam param = new UserDataScopeParam();
            param.setGlzXgh(username);
            param.setRoleId(roleId);
            List<UserDataScope> scopes = queryList(param);
            object = new DataScopeCache();
            if (!CollectionUtils.isEmpty(scopes))
                object.setDataScope(list2Single(scopes, false));
            object.setDeptId(userInfo.getXyid());
            object.setClassId(userInfo.getBjid());
            redisTemplate.opsForValue().set(key, object);
            redisTemplate.expire(key, 30, TimeUnit.MINUTES);
        }
        return object;
    }

    private static boolean checkImportData(HashSet<String> importData, Collection<String> dbData
            , List<ExcelImportError> errors, String remark) {
        int oldSize = errors.size();
        importData.forEach(str -> {
            if (!dbData.contains(str))
                errors.add(ExcelImportError.data(null, str, remark));
        });
        return errors.size() > oldSize;
    }
}
