package com.sanythadmin.project.evaluate.controller;

import com.sanythadmin.common.core.annotation.OperationLog;
import com.sanythadmin.common.core.config.AsyncConfig;
import com.sanythadmin.common.core.utils.*;
import com.sanythadmin.common.core.web.BaseController;
import com.sanythadmin.common.core.web.ExcelImportError;
import com.sanythadmin.common.core.web.PageParam;
import com.sanythadmin.common.core.web.PageResult;
import com.sanythadmin.common.enums.UserType;
import com.sanythadmin.common.system.entity.UserInfo;
import com.sanythadmin.project.code.param.CodeXsztParam;
import com.sanythadmin.project.evaluate.entity.EvaluateConfig;
import com.sanythadmin.project.evaluate.entity.EvaluatePeerReviewRecord;
import com.sanythadmin.project.evaluate.entity.EvaluateReviewScore;
import com.sanythadmin.project.evaluate.param.EvaluatePeerReviewRecordParam;
import com.sanythadmin.project.evaluate.param.EvaluateReviewScoreParam;
import com.sanythadmin.project.evaluate.service.EvaluateConfigService;
import com.sanythadmin.project.evaluate.service.EvaluatePeerReviewRecordService;
import com.sanythadmin.project.evaluate.service.EvaluateReviewScoreService;
import com.sanythadmin.project.userInfo.service.PersonInfoFactory;
import jakarta.annotation.Resource;
import jakarta.servlet.http.HttpServletResponse;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.BeanUtils;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.util.CollectionUtils;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.multipart.MultipartFile;

import java.util.ArrayList;
import java.util.List;
import java.util.Map;
import java.util.concurrent.CopyOnWriteArrayList;
import java.util.concurrent.Executor;
import java.util.function.Function;
import java.util.stream.Collectors;

/**
 * 综合测评/学生评议分成绩控制器
 *
 * <AUTHOR>
 * @since 2025-07-14 09:27:59
 */
@Slf4j
@RestController
@RequestMapping("/api/evaluate/evaluate-review-score")
public class EvaluateReviewScoreController extends BaseController {
    @Resource
    private EvaluateReviewScoreService evaluateReviewScoreService;
    @Resource
    private EvaluatePeerReviewRecordService evaluatePeerReviewRecordService;
    @Resource
    private PersonInfoFactory personInfoFactory;
    @Resource
    private EvaluateConfigService evaluateConfigService;


    /**
     * 分页查询学生评议分成绩（权限标识：evaluate:evaluateReviewScore:list）
     */
    @PreAuthorize("hasAuthority('evaluate:evaluateReviewScore:list')")
    @GetMapping("/page")
    public PageResult<EvaluateReviewScore> page(EvaluateReviewScoreParam param) {
        return evaluateReviewScoreService.page(param);
    }

    /**
     * 查询全部学生评议分成绩（权限标识：evaluate:evaluateReviewScore:list）
     */
    @PreAuthorize("hasAuthority('evaluate:evaluateReviewScore:list')")
    @GetMapping()
    public List<EvaluateReviewScore> list(EvaluateReviewScoreParam param) {
        PageParam<EvaluateReviewScore, EvaluateReviewScoreParam> page = new PageParam<>(param);
        //page.setDefaultOrder("create_time desc");
        return evaluateReviewScoreService.list(page.getOrderWrapper());
    }

    /**
     * 根据id查询学生评议分成绩（权限标识：evaluate:evaluateReviewScore:list）
     */
    @PreAuthorize("hasAuthority('evaluate:evaluateReviewScore:list')")
    @GetMapping("/{id}")
    public EvaluateReviewScore get(@PathVariable("id") String id) {
        return evaluateReviewScoreService.getById(id);
    }

    /**
     * 计算学生互评均分（权限标识：evaluate:evaluateReviewScore:operation）
     */
    @PreAuthorize("hasAuthority('evaluate:evaluateReviewScore:operation')")
    @OperationLog(module = "学生评议分成绩", comments = "计算学生互评均分")
    @PostMapping("/operation")
    public void save(@RequestBody EvaluatePeerReviewRecordParam param) {
        List<EvaluatePeerReviewRecord> reviewStudents = evaluatePeerReviewRecordService.peerReviewStudents(param);
        for (EvaluatePeerReviewRecord record : reviewStudents) {
            evaluateReviewScoreService.operation(record);
        }
    }

    /**
     * 修改学院评分（权限标识：evaluate:evaluateReviewScore:operation）
     */
    @PreAuthorize("hasAuthority('evaluate:evaluateReviewScore:operation')")
    @OperationLog(module = "学生评议分成绩", comments = "修改学院评分")
    @PostMapping("/updateDeptScore")
    public void updateDeptScore(@RequestBody List<EvaluateReviewScore> reviewScores) {
        reviewScores.forEach(reviewScore -> evaluateReviewScoreService.updateDeptScore(reviewScore));
    }

    /**
     * 批量删除学生评议分成绩（权限标识：evaluate:evaluateReviewScore:remove）
     */
//    @PreAuthorize("hasAuthority('evaluate:evaluateReviewScore:remove')")
//    @OperationLog(module = "学生评议分成绩", comments = "批量删除学生评议分成绩")
//    @PostMapping("/remove")
    public void remove(@RequestBody List<String> ids) {
        evaluateReviewScoreService.removeByIds(ids);
    }

    private List<ExcelUtils.Head> getHeads() {
        List<ExcelUtils.Head> heads = new ArrayList<>();
        heads.add(new ExcelUtils.Head("学号", "xgh"));
        heads.add(new ExcelUtils.Head("姓名", "xm", false));
        heads.add(new ExcelUtils.Head("学院评分", "xypf", "(-)?\\d+(\\.\\d+)?"));
        return heads;
    }

    /**
     * 学院评分更新导入模板
     */
    @PreAuthorize("hasAuthority('evaluate:evaluateReviewScore:dataImport')")
    @GetMapping("/dataImportTemplate")
    public void dataImportTemplate(HttpServletResponse response) {
        ExcelUtils.dataImportTemplate(getHeads(), response);
    }

    /**
     * 学院评分更新数据导入（权限标识：evaluate:evaluateReviewScore:dataImport）
     */
    @PreAuthorize("hasAuthority('evaluate:evaluateReviewScore:dataImport')")
    @OperationLog(module = "学生评议分成绩", comments = "学院评分更新数据导入")
    @PostMapping("/dataImport")
    public void dataImport(@RequestParam(name = "file") MultipartFile file, String configId) throws Exception {
        List<EvaluateReviewScore> infos = ExcelUtils.readExcelToEntity(EvaluateReviewScore.class, file.getInputStream(), file.getOriginalFilename(), getHeads());
        List<UserInfo> userInfoList = personInfoFactory.apply(UserType.STUDENT, service -> service.queryList(null, CodeXsztParam.currentStudent(), CommonUtil.sFunctionList(UserInfo::getXgh, UserInfo::getXm)));
        if (CollectionUtils.isEmpty(userInfoList))
            AssertUtil.throwMessage("学生信息不存在");
        EvaluateConfig config = evaluateConfigService.getById(configId);
        if (config == null)
            AssertUtil.throwMessage("测评方案不存在");
        List<ExcelImportError> errors = new CopyOnWriteArrayList<>();
        Map<String, UserInfo> userInfoMap = userInfoList.stream().collect(Collectors.toMap(UserInfo::getXgh, Function.identity()));
        for (int i = 0; i < infos.size(); i++) {
            EvaluateReviewScore reviewScore = infos.get(i);
            UserInfo userInfo = userInfoMap.get(reviewScore.getXgh());
            if (userInfo == null) {
                errors.add(new ExcelImportError(i + 2, reviewScore.getXgh(), "未找到学生信息"));
                continue;
            }
            BeanUtils.copyProperties(userInfo, reviewScore);
            reviewScore.setConfigId(configId);
            reviewScore.setCpnf(config.getCpnf());
        }

        if (!CollectionUtils.isEmpty(errors)) {
            String str = CommonUtil.writeErrorInfoExcel(errors);
            AssertUtil.throwImportError(str);
        }
        Executor executor = SpringContextUtil.getBean(AsyncConfig.ASYNC_EXECUTOR, Executor.class);
        infos.forEach(reviewScore -> evaluateReviewScoreService.importUpdateDeptScore(reviewScore, executor));
    }

    /**
     * 学生评议成绩导出
     *
     * @param param
     * @param response
     */
    @PreAuthorize("hasAuthority('evaluate:evaluateReviewScore:list')")
    @GetMapping("/exportData")
    public void exportData(EvaluateReviewScoreParam param, HttpServletResponse response) {
        try {
            param.setLimit((long) Integer.MAX_VALUE - 1);
            param.setInternalCall(true);
            PageResult<EvaluateReviewScore> page = evaluateReviewScoreService.page(param);
            String[] title = {"学号", "姓名", "学院", "专业", "班级", "年级", "测评年份", "学生互评分", "学院评分"};
            String[] property = {"xgh", "xm", "xymc", "zymc", "bjmc", "njmc", "cpnf", "xshpf", "xypf"};
            EasyExcelHelper.exportExcel(page.getList(), title, property, response, null, "学生评议分成绩");
        } catch (Exception e) {
            log.error(e.getMessage(), e);
        }
    }
}
