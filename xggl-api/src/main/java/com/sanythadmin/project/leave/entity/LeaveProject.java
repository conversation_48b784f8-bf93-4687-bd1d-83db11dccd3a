package com.sanythadmin.project.leave.entity;

import com.sanythadmin.project.leave.enums.LeaveProjectDurationMode;
import com.sanythadmin.project.leave.enums.LeaveTriStateBoolean;
import jakarta.persistence.*;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.time.LocalDateTime;
import java.util.List;

/**
 * 项目表
 *
 * @since 2025/4/25 11:52
 */
@EqualsAndHashCode(callSuper = true)
@Data
@Entity
@Table(name = "syt_leave_project")
public class LeaveProject extends LeaveBaseEntity {

    /**
     * 管理名称
     */
    private String adminName;
    /**
     * 显示名称
     */
    private String displayName;
    private LocalDateTime openFrom;
    private LocalDateTime openTo;
    /**
     * 最大请假天数
     */
    private Integer maxLeaveDays;

//    /**
//     * 销假模式
//     */
//    @Enumerated(EnumType.STRING)
//    private LeaveCancellationMode cancellationMode;
//    /**
//     * 是否允许续假
//     */
//    @Enumerated
//    private LeaveTriStateBoolean allowExtension;
    /**
     * 启用消息通知
     */
    @Enumerated(EnumType.STRING)
    private LeaveTriStateBoolean enableNotification;
    /**
     * 请假时长计算模式
     */
    @Enumerated(EnumType.STRING)
    private LeaveProjectDurationMode durationMode;

    /**
     * 假期结束后自动销假(无需申请)
     */
    private boolean cancellationAfterLeaveEnd;
    /**
     * 仅允许在地理范围内申请销假
     */
    private boolean cancellationApplyInArea;
    /**
     * 无需审批销假
     */
    private boolean cancellationNoApproval;
    /**
     * 在地理范围内销假自动通过
     */
    private boolean cancellationAutoApproveInArea;

    /**
     * 生效条件已启用
     */
    private boolean conditionEnabled;
    /**
     * 生效条件
     */
    @OrderBy("sortOrder")
    @OneToMany(mappedBy = "project", cascade = CascadeType.ALL, orphanRemoval = true)
    private List<LeaveProjectCondition> conditions;
    @OneToMany(mappedBy = "project", cascade = CascadeType.REMOVE)
    private List<LeaveProjectArea> areas;

}
