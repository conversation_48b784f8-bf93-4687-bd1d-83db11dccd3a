package com.sanythadmin.project.leave.service.impl;

import com.sanythadmin.common.enums.JudgeMark;
import com.sanythadmin.common.system.entity.SysParam;
import com.sanythadmin.common.system.service.SysParamService;
import com.sanythadmin.project.leave.enums.LeaveDurationMode;
import com.sanythadmin.project.leave.enums.LeaveProjectDurationMode;
import com.sanythadmin.project.leave.enums.LeaveTriStateBoolean;
import lombok.AllArgsConstructor;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Service;

/**
 * @since 2025/7/31 15:56
 */
@AllArgsConstructor
@Service
public class LeaveParamServiceImpl implements com.sanythadmin.project.leave.service.LeaveParamService {

    private final SysParamService sysParamService;

    @Override
    public LeaveDurationMode getDurationMode(LeaveProjectDurationMode mode) {
        if (mode == LeaveProjectDurationMode.DEFAULT) {
            SysParam sysParam = sysParamService.getByParamName("请假模块默认时长计算模式");
            if (sysParam != null && StringUtils.isNotEmpty(sysParam.getParamValue())) {
                LeaveDurationMode[] values = LeaveDurationMode.values();
                for (LeaveDurationMode m : values) {
                    if (m.name().equals(sysParam.getParamValue())) {
                        return m;
                    }
                }
            }
            return LeaveDurationMode.ELAPSED_TIME;
        }

        return mode.getRealMode();
    }

    @Override
    public boolean isNotification(LeaveTriStateBoolean enableNotification) {
        if (enableNotification == LeaveTriStateBoolean.DEFAULT) {
            SysParam sysParam = sysParamService.getByParamName("请假模块默认是否通知");
            if (sysParam != null && StringUtils.isNotEmpty(sysParam.getParamValue())) {
                return JudgeMark.YES.getText().equals(sysParam.getParamValue());
            }
            return true;
        }
        return enableNotification == LeaveTriStateBoolean.TRUE;
    }
}
