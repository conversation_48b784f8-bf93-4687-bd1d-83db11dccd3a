package com.sanythadmin.project.leave.service.impl;

import com.sanythadmin.common.core.web.PageResult;
import com.sanythadmin.project.leave.entity.LeaveProject;
import com.sanythadmin.project.leave.entity.LeaveProjectCondition;
import com.sanythadmin.project.leave.enums.LeaveConditionOperator;
import com.sanythadmin.project.leave.enums.LeavePolicyAction;
import com.sanythadmin.project.leave.enums.LeaveProjectDurationMode;
import com.sanythadmin.project.leave.enums.LeaveTriStateBoolean;
import com.sanythadmin.project.leave.form.LeaveProjectConditionForm;
import com.sanythadmin.project.leave.form.LeaveProjectForm;
import com.sanythadmin.project.leave.query.LeaveProjectQuery;
import com.sanythadmin.project.leave.repository.LeaveBasicFieldRepository;
import com.sanythadmin.project.leave.repository.LeaveProjectRepository;
import com.sanythadmin.project.leave.util.LeaveUtil;
import lombok.AllArgsConstructor;
import org.apache.commons.lang3.BooleanUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.PageRequest;
import org.springframework.data.domain.Sort;
import org.springframework.data.jpa.domain.Specification;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.time.LocalDateTime;
import java.util.ArrayList;
import java.util.List;

/**
 * @since 2025/5/7 15:58
 */
@AllArgsConstructor
@Service
public class LeaveProjectServiceImpl implements com.sanythadmin.project.leave.service.LeaveProjectService {

    private final LeaveProjectRepository projectRepository;
    private final LeaveBasicFieldRepository basicFieldRepository;

    @Transactional(readOnly = true)
    @Override
    public PageResult<LeaveProjectForm> page(LeaveProjectQuery query) {
        PageRequest pageRequest = PageRequest.of(query.getPageNumber(), query.getPageSize(), Sort.by("createdAt").ascending());

        Page<LeaveProject> page = projectRepository.findAll(Specification.allOf(
                (root, query1, criteriaBuilder) -> {
                    if (query.getNameLike() != null)
                        return criteriaBuilder.like(root.get("name"), "%" + query.getNameLike() + "%");
                    return null;
                }
        ), pageRequest);

        return new PageResult<>(page.getContent().stream().map(LeaveProjectForm::new).toList(), page.getTotalElements());
    }

    @Transactional
    @Override
    public void operation(LeaveProjectForm form) {
        LeaveProject entity;
        if (form.getId() != null) {
            entity = projectRepository.findById(form.getId()).orElseThrow();
            entity.setUpdatedAt(LocalDateTime.now());
        } else {
            entity = new LeaveProject();
            entity.setCreatedAt(LocalDateTime.now());
        }

        LeaveUtil.checkTrue(StringUtils.isNotEmpty(form.getAdminName()), "管理名称不能为空");
        LeaveUtil.checkTrue(StringUtils.isNotEmpty(form.getDisplayName()), "显示名称不能为空");
        entity.setAdminName(form.getAdminName());
        entity.setDisplayName(form.getDisplayName());
        entity.setOpenFrom(form.getOpenFrom());
        entity.setOpenTo(form.getOpenTo());
        entity.setMaxLeaveDays(form.getMaxLeaveDays());

//        entity.setCancellationMode(form.getCancellationModeObj());
//        if (entity.getCancellationMode() == null) {
//            entity.setCancellationMode(LeaveCancellationMode.DEFAULT);
//        }
        //        entity.setAllowExtension(form.getAllowExtensionObj());
//        if (entity.getAllowExtension() == null) {
//            entity.setAllowExtension(LeaveTriStateBoolean.DEFAULT);
//        }

        entity.setDurationMode(form.getDurationModeObj());
        if (entity.getDurationMode() == null) {
            entity.setDurationMode(LeaveProjectDurationMode.DEFAULT);
        }
        entity.setEnableNotification(form.getEnableNotificationObj());
        if (entity.getEnableNotification() == null) {
            entity.setEnableNotification(LeaveTriStateBoolean.DEFAULT);
        }

        // 销假策略
        entity.setCancellationAfterLeaveEnd(LeaveUtil.judgeMarkToBoolean(form.getCancellationAfterLeaveEnd()));
        entity.setCancellationApplyInArea(LeaveUtil.judgeMarkToBoolean(form.getCancellationApplyInArea()));
        entity.setCancellationNoApproval(LeaveUtil.judgeMarkToBoolean(form.getCancellationNoApproval()));
        entity.setCancellationAutoApproveInArea(LeaveUtil.judgeMarkToBoolean(form.getCancellationAutoApproveInArea()));

        // 条件处理
        entity.setConditionEnabled(LeaveUtil.judgeMarkToBoolean(form.getConditionEnabled()));
        List<LeaveProjectCondition> conditions = entity.getConditions();
        if (conditions == null) {
            conditions = new ArrayList<>();
            entity.setConditions(conditions);
        } else {
            conditions.clear();
        }
        List<LeaveProjectConditionForm> formConditions = form.getConditions();
        if (formConditions != null) {
            int conditionIndex = 0;
            for (LeaveProjectConditionForm formCondition : formConditions) {
                LeaveUtil.checkTrue(StringUtils.isNotEmpty(formCondition.getFieldId()), "关联字段不能为空");
                LeaveUtil.checkTrue(StringUtils.isNotEmpty(formCondition.getOperatorId()), "匹配规则不能为空");

                LeaveProjectCondition condition = new LeaveProjectCondition();
                condition.setSortOrder(conditionIndex++);
                condition.setProject(entity);
                condition.setField(basicFieldRepository.findById(formCondition.getFieldId()).orElseThrow());
                condition.setOperator(LeaveConditionOperator.valueOf(formCondition.getOperatorId()));
                condition.setExpectedValue(formCondition.getExpectedValue());

                // 策略
                if (StringUtils.isNotEmpty(formCondition.getActionId())) {
                    condition.setAction(LeavePolicyAction.valueOf(formCondition.getActionId()));
                } else {
                    condition.setAction(LeavePolicyAction.DENY);
                }

                // 是否匹配所有
                condition.setMatchAll(formCondition.isMatchAll());
                if (BooleanUtils.isFalse(condition.getMatchAll())) {
                    LeaveUtil.checkTrue(StringUtils.isNotEmpty(formCondition.getExpectedValue()), "未选择匹配所有，匹配值不能为空");
                }

                conditions.add(condition);
            }
        }

        projectRepository.save(entity);
    }
}
