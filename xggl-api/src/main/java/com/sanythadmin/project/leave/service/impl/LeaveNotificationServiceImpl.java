package com.sanythadmin.project.leave.service.impl;

import com.sanythadmin.common.messsage.service.CommonMessageService;
import com.sanythadmin.project.leave.repository.LeaveApplicationProcessNodeRepository;
import com.sanythadmin.project.leave.service.LeaveNotificationService;
import com.sanythadmin.project.leave.service.LeaveParamService;
import com.sanythadmin.project.userInfo.service.PersonInfoFactory;
import com.sanythadmin.project.userInfo.service.UserDataScopeService;
import lombok.AllArgsConstructor;
import org.springframework.stereotype.Service;

/**
 * @since 2025/8/4 14:20
 */
@AllArgsConstructor
@Service
public class LeaveNotificationServiceImpl implements LeaveNotificationService {

    private final LeaveParamService paramService;
    private final LeaveApplicationProcessNodeRepository processNodeRepository;
    private final UserDataScopeService userDataScopeService;
    private final PersonInfoFactory personInfoFactory;
    private final CommonMessageService commonMessageService;

    @Override
    public void notifyToApprover(String nodeId) {
//        LeaveApplicationProcessNode processNode = processNodeRepository.findById(nodeId).orElseThrow();
//        LeaveApplication application = processNode.getApplication();
//        boolean notification = paramService.isNotification(application.getProject().getEnableNotification());
//        if (!notification) {
//            return;
//        }
//
//        LeaveImmutableUser user = application.getUser();
//        processNode.getAssignees().forEach(nodeAssignee -> {
//            SysRole role = nodeAssignee.getRole();
//            String roleScope = role.getRoleScope();
//            switch (roleScope) {
//                case Constants.ROLE_SCOPE_BJ:
//                    UserInfo userInfo = personInfoFactory.apply(UserType.STUDENT, service -> service.get(user.getXgh()));
//                    List<InstructorsVO> studentInstructors = userDataScopeService.getStudentInstructors(userInfo, Constants.ROLE_SCOPE_BJ,
//                            UserInfo::getXm, UserInfo::getXgh, UserInfo::getSjh);
//                    break;
//                case Constants.ROLE_SCOPE_BY:
//                    break;
//            }
//
//        });
    }

}
