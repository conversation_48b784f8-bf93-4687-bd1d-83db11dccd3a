package com.sanythadmin.project.leave.service.impl;

import com.sanythadmin.common.core.exception.BusinessException;
import com.sanythadmin.common.core.utils.SecurityUtil;
import com.sanythadmin.common.core.web.PageResult;
import com.sanythadmin.common.system.entity.UserInfo;
import com.sanythadmin.project.code.entity.CodeBjb;
import com.sanythadmin.project.code.entity.CodeCommon;
import com.sanythadmin.project.code.entity.CodeDwb;
import com.sanythadmin.project.code.entity.CodeZyb;
import com.sanythadmin.project.code.param.CodeCommonParam;
import com.sanythadmin.project.code.service.CodeBjbService;
import com.sanythadmin.project.code.service.CodeCommonService;
import com.sanythadmin.project.code.service.CodeDwbService;
import com.sanythadmin.project.code.service.CodeZybService;
import com.sanythadmin.project.dictionary.entity.DictionaryField;
import com.sanythadmin.project.leave.entity.*;
import com.sanythadmin.project.leave.enums.*;
import com.sanythadmin.project.leave.form.LeaveApplicationFieldForm;
import com.sanythadmin.project.leave.form.LeaveApplicationForm;
import com.sanythadmin.project.leave.form.LeaveIdTextPairForm;
import com.sanythadmin.project.leave.form.LeaveWithdrawForm;
import com.sanythadmin.project.leave.immutable.LeaveImmutableUser;
import com.sanythadmin.project.leave.query.LeaveQuerySuper;
import com.sanythadmin.project.leave.repository.*;
import com.sanythadmin.project.leave.service.*;
import com.sanythadmin.project.leave.util.LeaveGeoUtils;
import com.sanythadmin.project.leave.util.LeaveUtil;
import com.sanythadmin.project.leave.vo.LeaveStudentActionCancellationVo;
import com.sanythadmin.project.leave.vo.LeaveStudentActionDetailVo;
import com.sanythadmin.project.leave.vo.LeaveStudentActionLeavePageVo;
import com.sanythadmin.project.leave.vo.LeaveStudentActionProjectPageVo;
import lombok.AllArgsConstructor;
import org.apache.commons.beanutils.PropertyUtils;
import org.apache.commons.codec.digest.DigestUtils;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.ArrayUtils;
import org.apache.commons.lang3.BooleanUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.PageRequest;
import org.springframework.data.domain.Sort;
import org.springframework.data.jpa.domain.Specification;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.web.multipart.MultipartFile;

import java.io.IOException;
import java.lang.reflect.InvocationTargetException;
import java.time.Duration;
import java.time.LocalDate;
import java.time.LocalDateTime;
import java.time.LocalTime;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.concurrent.atomic.AtomicInteger;
import java.util.concurrent.atomic.AtomicReference;

/**
 * @since 2025/5/9 11:12
 */
@AllArgsConstructor
@Service
public class LeaveStudentActionServiceImpl implements LeaveStudentActionService {

    private final LeaveApplicationRepository applicationRepository;
    private final LeaveProjectRepository projectRepository;
    private final CodeCommonService codeCommonService;
    private final CodeDwbService codeDwbService;
    private final CodeZybService codeZybService;
    private final CodeBjbService codeBjbService;
    private final LeaveCommonService commonService;
    private final LeaveProjectFieldRepository projectFormFieldRepository;
    private final LeaveProjectApprovalRuleNodeRepository projectApprovalRuleNodeRepository;
    private final LeaveApplicationProcessNodeRepository applicationProcessNodeRepository;
    private final LeaveApplicationProcessRecordRepository processRecordRepository;
    private final LeaveApplicationService applicationService;
    private final LeaveProjectAreaRepository projectAreaRepository;
    private final LeaveParamService paramService;
    private final LeaveNotificationService notificationService;

    @Transactional(readOnly = true)
    @Override
    public LeaveStudentActionProjectPageVo optionalProjectPage(String lastId, Integer limit) {
        UserInfo userInfo = SecurityUtil.getUserInfo();

        List<LeaveIdTextPairForm> list = new ArrayList<>();
        boolean hasMore = true;
        do {
            Page<LeaveProject> page = fetchMoreProjects(lastId, limit);
            if (page.isEmpty()) {
                break;
            }
            list.addAll(dealProjects(page, userInfo));
            List<LeaveProject> content = page.getContent();
            lastId = content.get(content.size() - 1).getId();
            if (!page.hasNext()) {
                lastId = null;
                hasMore = false;
                break;
            }
        } while (list.size() < limit);

        return new LeaveStudentActionProjectPageVo(list, lastId, hasMore);
    }

    @Transactional(readOnly = true)
    @Override
    public List<LeaveIdTextPairForm> optionalProjectAll() {
        UserInfo userInfo = SecurityUtil.getUserInfo();

        return projectRepository.findAll(Specification.allOf(
                (root, query1, criteriaBuilder) -> {
                    LocalDateTime now = LocalDateTime.now();
                    return criteriaBuilder.and(
//                            criteriaBuilder.isNotNull(root.get("openFrom")),
//                            criteriaBuilder.isNotNull(root.get("openTo")),
                            criteriaBuilder.lessThanOrEqualTo(root.get("openFrom"), now),
                            criteriaBuilder.greaterThanOrEqualTo(root.get("openTo"), now)
                    );
                }
        ), Sort.by("createdAt").descending()).stream().filter(p -> checkUserCanApply(p, userInfo)).map(p -> new LeaveIdTextPairForm(p.getId(), p.getDisplayName())).toList();
    }

    private Page<LeaveProject> fetchMoreProjects(String lastId, int pageLimit) {
        PageRequest pageRequest = PageRequest.of(0, Math.max(pageLimit, 20), Sort.by("createdAt").descending());

        return projectRepository.findAll(Specification.allOf(
                (root, query1, criteriaBuilder) -> {
                    LocalDateTime now = LocalDateTime.now();
                    return criteriaBuilder.and(
//                            criteriaBuilder.isNotNull(root.get("openFrom")),
//                            criteriaBuilder.isNotNull(root.get("openTo")),
                            criteriaBuilder.lessThanOrEqualTo(root.get("openFrom"), now),
                            criteriaBuilder.greaterThanOrEqualTo(root.get("openTo"), now)
                    );
                },
                (root, query1, criteriaBuilder) -> {
                    if (lastId == null) {
                        return null;
                    }
                    LeaveProject lastProject = projectRepository.findById(lastId).orElseThrow();
                    return criteriaBuilder.lessThan(root.get("createdAt"), lastProject.getCreatedAt());
                }
        ), pageRequest);
    }

    private List<LeaveIdTextPairForm> dealProjects(Page<LeaveProject> page, UserInfo userInfo) {
        return page.filter(p -> checkUserCanApply(p, userInfo)).map(p -> LeaveIdTextPairForm.of(p.getId(), p.getDisplayName())).toList();
    }

    private boolean checkUserCanApply(LeaveProject p, UserInfo userInfo) {
        if (!p.isConditionEnabled()) {
            return true;
        }
        List<LeaveProjectCondition> conditions = p.getConditions();
        if (conditions != null) {
            for (LeaveProjectCondition c : conditions) {
                // 策略
                boolean policy = LeavePolicyAction.ALLOW == c.getAction();

                // 是否匹配所有
                if (BooleanUtils.isTrue(c.getMatchAll())) {
                    return policy;
                }

                // 部分匹配
                DictionaryField dicField = c.getField().getDicField();
                String userFieldValue;
                try {
                    userFieldValue = (String) PropertyUtils.getProperty(userInfo, dicField.getFieldEn());
                } catch (IllegalAccessException | InvocationTargetException | NoSuchMethodException e) {
//                            throw new RuntimeException(e);
                    throw new BusinessException("获取用户信息失败:" + e.getMessage());
                }

                String value = null;
                if ("other".equals(dicField.getLoadDataType())) {
                    // 特殊关联
                    switch (dicField.getFieldEn()) {
                        case "xyid" -> {
                            CodeDwb codeDwb = codeDwbService.getById(userFieldValue);
                            value = codeDwb.getName();
                        }
                        case "zyid" -> {
                            CodeZyb codeZyb = codeZybService.getById(userFieldValue);
                            value = codeZyb.getName();
                        }
                        case "bjid" -> {
                            CodeBjb codeBjb = codeBjbService.getById(userFieldValue);
                            value = codeBjb.getName();
                        }
                    }
                } else if (StringUtils.isNotEmpty(dicField.getLoadDataType())) {
                    // 通用码表
                    if (userFieldValue != null) {
                        CodeCommon codeCommon = codeCommonService.getById(userFieldValue);
                        value = codeCommon.getName();
                    }
                } else {
                    // 普通字段
                    value = userFieldValue;
                }

                switch (c.getOperator()) {
                    case EQUAL -> {
                        if (StringUtils.equals(value, c.getExpectedValue())) {
                            return policy;
                        }
                    }
                    case NOT_EQUAL -> {
                        if (!StringUtils.equals(value, c.getExpectedValue())) {
                            return policy;
                        }
                    }
                }
            }
        }

        return false;
    }

    @Transactional(readOnly = true)
    @Override
    public List<LeaveApplicationFieldForm> formFieldsForAdd(String projectId, LeaveRequestType requestType) {
        List<LeaveApplicationFieldForm> formFields = new ArrayList<>();

        LeaveProject project = projectRepository.findById(projectId).orElseThrow();
        projectFormFieldRepository.findByProjectAndRequestTypeOrderBySortOrder(project, requestType).forEach(p -> {
            LeaveApplicationFieldForm formField = new LeaveApplicationFieldForm();
            formField.setFieldId(p.getId());
            formField.setFieldName(p.getName());
            formField.setFieldType(p.getType());
            formField.setRequired(p.getRequired());
            formField.setOptions(p.getOptions().stream().map(LeaveProjectFieldOption::getText).toList());
            formFields.add(formField);
        });
        return formFields;
    }

    @Transactional
    @Override
    public void applicationSubmit(LeaveApplicationForm form) {
        LeaveUtil.checkTrue(StringUtils.isNotEmpty(form.getProjectId()), "类型ID不能为空");
        LeaveRequestType requestType = form.getRequestType();
        LeaveUtil.checkTrue(requestType != null, "申请类型不能为空");

        LeaveApplication entity = new LeaveApplication();
//        entity.setProject(project);
        entity.setRequestType(requestType);
        entity.setUser(new LeaveImmutableUser(SecurityUtil.getUsername()));
        List<LeaveApplicationField> fields = new ArrayList<>();
        entity.setFields(fields);
        entity.setAddress(form.getAddress());
        entity.setLongitude(form.getLongitude());
        entity.setLatitude(form.getLatitude());

        LocalDateTime now = LocalDateTime.now();
        boolean needProcess = true;
        if (requestType == LeaveRequestType.LEAVE) {
            LeaveProject project = projectRepository.findById(form.getProjectId()).orElseThrow(() -> new BusinessException("类型不存在"));
            entity.setProject(project);

            // 申请类型为请假
            LeaveUtil.checkTrue(StringUtils.isNotEmpty(form.getStartTime()), "开始时间不能为空");
            LeaveUtil.checkTrue(StringUtils.isNotEmpty(form.getEndTime()), "结束时间不能为空");
            LocalDateTime formStartTime = LocalDateTime.parse(form.getStartTime(), LeaveUtil.DATE_TIME_FORMATTER);
            LocalDateTime formEndTime = LocalDateTime.parse(form.getEndTime(), LeaveUtil.DATE_TIME_FORMATTER);
            // YYYY-MM-DD HH:mm
            entity.setStartTime(LocalDateTime.of(formStartTime.toLocalDate(), LocalTime.of(formStartTime.getHour(), formStartTime.getMinute())));
            entity.setEndTime(LocalDateTime.of(formEndTime.toLocalDate(), LocalTime.of(formEndTime.getHour(), formEndTime.getMinute())));

            entity.setDurationMode(paramService.getDurationMode(project.getDurationMode()));
            switch (entity.getDurationMode()) {
                case NATURAL_DAY -> {
                    LocalDate ld1 = entity.getStartTime().toLocalDate();
                    LocalDate ld2 = entity.getEndTime().toLocalDate();
                    entity.setDurationDays(ld2.compareTo(ld1) + 1);
                }
                case ELAPSED_TIME -> {
                    Duration duration = Duration.between(entity.getStartTime(), entity.getEndTime());
                    entity.setDurationDays((int) (duration.toDays() + 1));
                }
            }

            // 销假策略
            entity.setCancellationAfterLeaveEnd(project.isCancellationAfterLeaveEnd());
            entity.setCancellationApplyInArea(project.isCancellationApplyInArea());
            entity.setCancellationNoApproval(project.isCancellationNoApproval());
            entity.setCancellationAutoApproveInArea(project.isCancellationAutoApproveInArea());
        } else {
            LeaveUtil.checkTrue(StringUtils.isNotEmpty(form.getOriginalId()), "原始申请ID不能为空");
            // 申请类型为销假
            entity.setOriginalApplication(applicationRepository.findById(form.getOriginalId()).orElseThrow(() -> new BusinessException("原始申请记录不存在")));
            entity.setProject(entity.getOriginalApplication().getProject());

            LeaveUtil.checkTrue(BooleanUtils.isNotTrue(entity.getOriginalApplication().getCancellationAfterLeaveEnd()), "无需申请销假");

            if (BooleanUtils.toBoolean(entity.getOriginalApplication().getCancellationNoApproval())) {
                // 无需审批直接通过
                entity.setApproved(true);
                entity.setApprovalStartTime(now);
                entity.setApprovalEndTime(now);
                needProcess = false;
            }

            if (BooleanUtils.toBoolean(entity.getOriginalApplication().getCancellationAutoApproveInArea()) && entity.getLatitude() != null && entity.getLongitude() != null) {
                // 在地理范围内无需审批直接通过

                boolean inArea = false;
                for (LeaveProjectArea area : projectAreaRepository.findAllByProject(entity.getProject())) {
                    if (LeaveGeoUtils.isWithinRange(area.getLatitude(), area.getLongitude(), entity.getLatitude(), entity.getLongitude(), area.getRadius())) {
                        inArea = true;
                        break;
                    }
                }

                if (inArea) {
                    entity.setApproved(true);
                    entity.setApprovalStartTime(now);
                    entity.setApprovalEndTime(now);
                    needProcess = false;
                }
            }

            if (BooleanUtils.toBoolean(entity.getOriginalApplication().getCancellationApplyInArea())) {
                // 仅支持在范围内销假，不允许缺失定位信息
                LeaveUtil.checkTrue(entity.getLatitude() == null && entity.getLongitude() == null, "缺少定位信息");
            }
        }

        // 用户提交的字段
        Map<String, LeaveApplicationFieldForm> fieldFormMap = new HashMap<>();
        form.getFields().forEach(f -> fieldFormMap.put(f.getFieldId(), f));

        // 以项目配置的字段顺序处理
        Map<LeaveProjectField, LeaveApplicationField> projectToAplMap = new HashMap<>();
        AtomicInteger fieldIndex = new AtomicInteger();
        List<LeaveProjectField> projectFormFields = projectFormFieldRepository.findByProjectAndRequestTypeOrderBySortOrder(entity.getProject(), requestType);
        projectFormFields.forEach(p -> {
            LeaveApplicationField field = new LeaveApplicationField();
            field.setApplication(entity);
            fields.add(field);
            projectToAplMap.put(p, field);

//            field.setCreatedAt(now);
            field.setName(p.getName());
            field.setType(p.getType());
            field.setRequired(p.getRequired());
            field.setType(p.getType());
            field.setSortOrder(fieldIndex.getAndIncrement());
            field.setPlaceholder(p.getPlaceholder());
            field.setCodeType(p.getCodeType());

            // 获取用户输入的表单
            LeaveApplicationFieldForm fieldForm = fieldFormMap.get(p.getId());
            if (p.getType() == LeaveFormFieldType.FILE) {
                // 上传文件
                List<MultipartFile> uploadFiles = fieldForm.getUploadFiles();

                // 检查必填
                if (field.getRequired()) {
                    LeaveUtil.checkTrue(CollectionUtils.isNotEmpty(uploadFiles), "字段[" + field.getName() + "]必填");
                }

                if (CollectionUtils.isNotEmpty(uploadFiles)) {
                    List<LeaveApplicationFieldFile> files = new ArrayList<>();
                    field.setFiles(files);
                    uploadFiles.forEach(f -> {
                        LeaveApplicationFieldFile file = new LeaveApplicationFieldFile();
                        file.setField(field);
                        files.add(file);
                        file.setFilename(f.getOriginalFilename());
                        file.setMime(f.getContentType());
                        file.setLength(f.getSize());
                        try {
                            file.setContent(f.getBytes());
                        } catch (IOException e) {
                            throw new RuntimeException(e);
                        }
                        file.setSha1(DigestUtils.sha1Hex(file.getContent()));
                        file.setCreatedAt(now);
                    });
                }
            } else {
                // 获取表单中的值
                String value = null;
                if (fieldForm != null && fieldForm.getValue() != null) {
                    value = fieldForm.getValue();
                }

                // 检查必填
                if (field.getRequired()) {
                    LeaveUtil.checkTrue(StringUtils.isNotEmpty(value), "字段[" + field.getName() + "]必填");
                }

                // 拷贝选项
                if (field.getType().getCategory() == LeaveFieldCategory.OPTION) {
                    field.setOptions(p.getOptions().stream().map(o -> {
                        LeaveApplicationFieldOption option = new LeaveApplicationFieldOption();
                        option.setField(field);
                        option.setText(o.getText());
                        return option;
                    }).toList());
                }

                if (StringUtils.isNotEmpty(value)) {
                    // 设置值
                    switch (field.getType()) {
                        case NUMBER -> field.setValueInteger(Integer.parseInt(value));
                        case DECIMAL -> field.setValueDouble(Double.parseDouble(value));
                        case BOOLEAN -> field.setValueInteger("是".equals(value) ? 1 : 0);
                        case RADIO -> {
                            int count = 0;
                            List<LeaveApplicationFieldSelectedOption> selectedOptions = new ArrayList<>();
                            field.setSelectedOptions(selectedOptions);

                            if (field.getCodeType() != null) {
                                // 外部通用码表
                                List<CodeCommon> list = codeCommonService.list(new CodeCommonParam(field.getCodeType().getCode()));
                                for (CodeCommon c : list) {
                                    if (c.getName().equals(value)) {
                                        count++;
                                        LeaveApplicationFieldSelectedOption selectedOption = new LeaveApplicationFieldSelectedOption();
                                        selectedOptions.add(selectedOption);
                                        selectedOption.setField(field);
                                        selectedOption.setCode(c.getCode());
                                        selectedOption.setText(c.getName());
                                    }
                                }
                            } else {
                                // 本地配置
                                for (LeaveApplicationFieldOption o : field.getOptions()) {
                                    if (o.getText().equals(value)) {
                                        count++;
                                        LeaveApplicationFieldSelectedOption selectedOption = new LeaveApplicationFieldSelectedOption();
                                        selectedOptions.add(selectedOption);
                                        selectedOption.setField(field);
//                                selectedOption.setCode(o.getText());
                                        selectedOption.setText(o.getText());
                                    }
                                }
                            }

                            LeaveUtil.checkTrue(count > 0, "[" + field.getName() + "]选择的选项不存在");
                            LeaveUtil.checkTrue(count == 1, "[" + field.getName() + "]只能选择一个");
                        }
                        case CHECKBOX -> {
                            int count = 0;
                            String[] split = value.split(",");
                            List<LeaveApplicationFieldSelectedOption> selectedOptions = new ArrayList<>();
                            field.setSelectedOptions(selectedOptions);

                            if (field.getCodeType() != null) {
                                // 外部通用码表
                                List<CodeCommon> list = codeCommonService.list(new CodeCommonParam(field.getCodeType().getCode()));
                                for (CodeCommon c : list) {
                                    if (ArrayUtils.contains(split, c.getName())) {
                                        count++;
                                        LeaveApplicationFieldSelectedOption selectedOption = new LeaveApplicationFieldSelectedOption();
                                        selectedOptions.add(selectedOption);
                                        selectedOption.setField(field);
                                        selectedOption.setCode(c.getCode());
                                        selectedOption.setText(c.getName());
                                    }
                                }
                            } else {
                                // 本地配置
                                for (LeaveApplicationFieldOption o : field.getOptions()) {
                                    if (ArrayUtils.contains(split, o.getText())) {
                                        count++;
                                        LeaveApplicationFieldSelectedOption selectedOption = new LeaveApplicationFieldSelectedOption();
                                        selectedOptions.add(selectedOption);
                                        selectedOption.setField(field);
                                        selectedOption.setText(o.getText());
                                    }
                                }
                            }

                            LeaveUtil.checkTrue(count > 0, "[" + field.getName() + "]选择的选项不存在");
                        }
                        case DATE -> field.setValueDate(LocalDate.parse(value, LeaveUtil.DATE_FORMATTER));
                        case DATETIME ->
                                field.setValueDatetime(LocalDateTime.parse(value, LeaveUtil.DATE_TIME_FORMATTER));
                        case LONGTEXT -> field.setValueLongText(value);
                        default -> field.setValueText(value);
                    }
                }
            }
        });

        entity.setSubmitTime(now);
        entity.setCreatedAt(now);
        applicationRepository.save(entity);

        if (!needProcess) {
            return;
        }
        AtomicReference<LeaveApplicationProcessNodeAssignee> noticeNodeAssign = new AtomicReference<>();
        LeaveApplicationProcessNode noticeNode = null;

        // 获取流程规则
        Map<Integer, List<LeaveProjectApprovalRuleNode>> ruleNodeMap = new HashMap<>();
        if (entity.getRequestType() == LeaveRequestType.LEAVE) {
            projectApprovalRuleNodeRepository.findByProjectAndRequestTypeAndMinDaysLessThanEqualOrderBySortOrderAscParallelOrderAsc(entity.getProject(), entity.getRequestType(), entity.getDurationDays()).forEach(node -> {
                List<LeaveProjectApprovalRuleNode> nodes = ruleNodeMap.computeIfAbsent(node.getSortOrder(), k -> new ArrayList<>());
                nodes.add(node);
            });
        } else if (entity.getRequestType() == LeaveRequestType.CANCELLATION) {
            projectApprovalRuleNodeRepository.findByProjectAndRequestTypeOrderBySortOrderAscParallelOrderAsc(entity.getProject(), entity.getRequestType()).forEach(node -> {
                List<LeaveProjectApprovalRuleNode> nodes = ruleNodeMap.computeIfAbsent(node.getSortOrder(), k -> new ArrayList<>());
                nodes.add(node);
            });
        }

        // 生成流程
        AtomicInteger processNodeIndex = new AtomicInteger();
        List<LeaveApplicationProcessNode> processNodes = new ArrayList<>();
        for (List<LeaveProjectApprovalRuleNode> nodes : ruleNodeMap.values()) {
            for (LeaveProjectApprovalRuleNode node : nodes) {
                if (node.getConditionEnabled()) {
                    Boolean b = null;
                    for (LeaveProjectApprovalRuleNodeCondition condition : node.getConditions()) {
                        boolean actionBool = condition.getAction() == LeavePolicyAction.ALLOW;
                        if (condition.getMatchAll()) {
                            b = actionBool;
                            break;
                        }

                        LeaveProjectField field = condition.getField();
                        LeaveApplicationField applicationField = projectToAplMap.get(field);
                        LeaveFormFieldType type = applicationField.getType();
                        switch (type) {
                            case RADIO, CHECKBOX -> {
                                String expectedValue = condition.getExpectedValue();
                                List<String> selected = applicationField.getSelectedOptions().stream().map(LeaveApplicationFieldSelectedOption::getText).toList();
                                switch (condition.getOperator()) {
                                    case EQUAL -> {
                                        if (selected.contains(expectedValue)) {
                                            b = actionBool;
                                        }
                                    }
                                    case NOT_EQUAL -> {
                                        if (!selected.contains(expectedValue)) {
                                            b = actionBool;
                                        }
                                    }
                                }

                            }
                            case BOOLEAN -> {
                                String expectedValue = condition.getExpectedValue();
                                boolean fieldValue = applicationField.getValueInteger() == 1;
                                switch (condition.getOperator()) {
                                    case EQUAL -> {
                                        if ("是".equals(expectedValue) == fieldValue) {
                                            b = actionBool;
                                        }
                                    }
                                    case NOT_EQUAL -> {
                                        if ("是".equals(expectedValue) != fieldValue) {
                                            b = actionBool;
                                        }
                                    }
                                }
                            }
                            default -> {
                                String expectedValue = condition.getExpectedValue();
                                String fieldValue = applicationField.getValueText();
                                if (type == LeaveFormFieldType.LONGTEXT) {
                                    fieldValue = applicationField.getValueLongText();
                                }
                                switch (condition.getOperator()) {
                                    case EQUAL -> {
                                        if (StringUtils.equals(expectedValue, fieldValue)) {
                                            b = actionBool;
                                        }
                                    }
                                    case NOT_EQUAL -> {
                                        if (!StringUtils.equals(expectedValue, fieldValue)) {
                                            b = actionBool;
                                        }
                                    }
                                }
                            }
                        }
                        if (b != null) {
                            break;
                        }
                    }

                    if (BooleanUtils.isNotTrue(b)) {
                        continue;
                    }
                }

                LeaveApplicationProcessNode processNode = new LeaveApplicationProcessNode();
                processNode.setApplication(entity);
                processNode.setName(node.getName());
                processNode.setSequence(processNodeIndex.getAndIncrement());
                processNode.setApprovalMode(LeaveApprovalMode.FIRST);

                if (processNode.getSequence() == 0) {
                    processNode.setStatus(LeaveProcessNodeStatus.PENDING);
                    processNode.setActivatedAt(now);
                    noticeNode = processNode;
                } else {
                    processNode.setStatus(LeaveProcessNodeStatus.NOT_START);
                }

                processNode.setAssignees(node.getRoles().stream().map(r -> {
                    LeaveApplicationProcessNodeAssignee assignee = new LeaveApplicationProcessNodeAssignee();
                    assignee.setNode(processNode);
                    assignee.setRole(r.getRole());
                    if (processNode.getSequence() == 0) {
                        assignee.setStatus(LeaveProcessNodeAssigneeStatus.PENDING);
                    }
                    return assignee;
                }).toList());

                processNodes.add(processNode);
                break;
            }
        }

        LeaveUtil.checkTrue(!processNodes.isEmpty(), "流程为空，申请失败");
        applicationProcessNodeRepository.saveAll(processNodes);
        if (noticeNode != null) {
            notificationService.notifyToApprover(noticeNode.getId());
        }
    }

    @Transactional(readOnly = true)
    @Override
    public PageResult<LeaveStudentActionLeavePageVo> leavePage(LeaveQuerySuper query) {
        PageRequest pageRequest = PageRequest.of(query.getPageNumber(), query.getPageSize(), Sort.by("createdAt").descending());

        Page<LeaveApplication> page = applicationRepository.findAll(Specification.allOf(
                (root, query1, criteriaBuilder) -> {
                    String username = SecurityUtil.getUsername();
                    return criteriaBuilder.equal(root.get("user").get("xgh"), username);
                },
                (root, query1, criteriaBuilder) -> {
                    return criteriaBuilder.equal(root.get("requestType"), LeaveRequestType.LEAVE);
                }
        ), pageRequest);

        Map<LeaveApplication, LeaveApplication> latestChildMap = applicationService.getLatestChildMap(page);
        List<LeaveStudentActionLeavePageVo> results = page.getContent().stream()
                .map(parent -> {
                    LeaveStudentActionLeavePageVo vo = new LeaveStudentActionLeavePageVo(parent);
                    vo.setLatestCancelledApplication(latestChildMap.get(parent));
                    return vo;
                })
                .toList();

        return new PageResult<>(results, page.getTotalElements());
    }

    @Override
    public void deleteById(String id) {
        applicationRepository.findById(id).ifPresent(a -> {
            LeaveUtil.checkTrue(LeaveUtil.applicationCanDelete(a), "无法删除");
            applicationRepository.delete(a);
        });
    }

    @Transactional
    @Override
    public void onWithdraw(LeaveWithdrawForm form) {
        LeaveUtil.checkTrue(StringUtils.isNotEmpty(form.getApplicationId()), "申请ID不能为空");
        LeaveApplication application = applicationRepository.findById(form.getApplicationId()).orElseThrow(() -> new BusinessException("申请不存在"));
        LeaveUtil.checkTrue(application.getApprovalStartTime() != null, "未开始审批，可直接删除");
        LeaveUtil.checkTrue(application.getApprovalEndTime() == null, "已审批结束，无法撤回");
        LeaveUtil.checkTrue(application.getWithdrawAt() == null, "不可重复撤回");
        LeaveUtil.checkTrue(StringUtils.isNotEmpty(form.getReason()), "撤回原因不能为空");

        LocalDateTime now = LocalDateTime.now();
        application.setWithdrawAt(now);
        application.setWithdrawReason(form.getReason());
        applicationRepository.save(application);

        // 清除流程节点的待审批状态
        application.getProcessNodes().forEach(p -> {
            if (p.getStatus() == LeaveProcessNodeStatus.PENDING) {
                p.setStatus(null);
                p.getAssignees().forEach(a -> {
                    if (a.getStatus() == LeaveProcessNodeAssigneeStatus.PENDING) {
                        a.setStatus(null);
                    }
                });
                applicationProcessNodeRepository.save(p);
            }
        });


        // 审批记录
        LeaveApplicationProcessRecord record = new LeaveApplicationProcessRecord();
        record.setApplication(application);
//        record.setUsername(SecurityUtil.getUsername());
        record.setUser(new LeaveImmutableUser(SecurityUtil.getUsername()));
        record.setActionType(LeaveProcessActionType.WITHDRAWN);
        record.setContent(form.getReason());
        record.setCreatedAt(now);
        processRecordRepository.save(record);
    }

    @Transactional(readOnly = true)
    @Override
    public LeaveStudentActionDetailVo leaveDetail(String leaveId) {
        LeaveUtil.checkTrue(StringUtils.isNotEmpty(leaveId), "leaveId is required");
        String username = SecurityUtil.getUsername();

        LeaveApplication application = applicationRepository.findByIdAndUserXgh(leaveId, username).orElseThrow(() -> new BusinessException("未找到数据"));
        LeaveStudentActionDetailVo detailVo = new LeaveStudentActionDetailVo(application);
        detailVo.setBasicInfo(commonService.basicFieldWithStudentInfo(application.getUser().getXgh()));
        detailVo.setApplicationFields(LeaveUtil.viewApplicationFields(application));
        detailVo.setFlows(applicationService.flows(application));

        // 销假策略
        detailVo.setCancellationApplyInArea(BooleanUtils.toBoolean(application.getCancellationApplyInArea()));

        // 定位信息
        detailVo.setAddress(application.getAddress());
        detailVo.setLongitude(application.getLongitude());
        detailVo.setLatitude(application.getLatitude());

        // 销假记录
        List<LeaveApplication> cancellationList = applicationRepository.findAll(Specification.allOf(
                (root, query1, criteriaBuilder) -> criteriaBuilder.equal(root.get("originalApplication").get("id"), leaveId),
                (root, query1, criteriaBuilder) -> criteriaBuilder.equal(root.get("requestType"), LeaveRequestType.CANCELLATION)
        ), Sort.by("createdAt").descending());
        detailVo.setLatestCancelledApplication(cancellationList.stream().findFirst().orElse(null));
        detailVo.setCancellations(cancellationList.stream().map(item -> {
            LeaveStudentActionCancellationVo vo = new LeaveStudentActionCancellationVo(item);
            vo.setApplicationFields(LeaveUtil.viewApplicationFields(item));
            vo.setFlows(applicationService.flows(item));

            // 定位信息
            vo.setAddress(item.getAddress());
            vo.setLongitude(item.getLongitude());
            vo.setLatitude(item.getLatitude());

            return vo;
        }).toList());

        return detailVo;
    }

}
