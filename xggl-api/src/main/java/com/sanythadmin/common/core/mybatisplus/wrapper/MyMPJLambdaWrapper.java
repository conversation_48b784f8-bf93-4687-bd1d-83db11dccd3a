package com.sanythadmin.common.core.mybatisplus.wrapper;

import cn.hutool.core.bean.BeanUtil;
import cn.hutool.core.util.ReflectUtil;
import cn.hutool.core.util.StrUtil;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableLogic;
import com.baomidou.mybatisplus.core.metadata.OrderItem;
import com.baomidou.mybatisplus.core.toolkit.support.SFunction;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.github.yulichang.toolkit.LambdaUtils;
import com.github.yulichang.toolkit.TableList;
import com.github.yulichang.wrapper.MPJLambdaWrapper;
import com.sanythadmin.common.core.annotation.EncryptedColumn;
import com.sanythadmin.common.core.annotation.EncryptedTable;
import com.sanythadmin.common.core.annotation.QueryField;
import com.sanythadmin.common.core.annotation.QueryType;
import com.sanythadmin.common.core.mybatisplus.MyBatisPlusUtil;
import com.sanythadmin.common.core.mybatisplus.base.MyPage;
import com.sanythadmin.common.core.utils.AESCryptoUtil;
import com.sanythadmin.common.core.utils.CommonUtil;
import com.sanythadmin.common.core.web.BaseParam;
import com.sanythadmin.common.system.entity.UserInfo;
import lombok.Getter;
import lombok.extern.slf4j.Slf4j;
import org.springframework.core.annotation.AnnotationUtils;
import org.springframework.util.CollectionUtils;
import org.springframework.util.StringUtils;

import java.lang.reflect.Field;
import java.util.*;
import java.util.regex.Matcher;
import java.util.regex.Pattern;

@Slf4j
public class MyMPJLambdaWrapper<T, U extends BaseParam> extends MPJLambdaWrapper<T> {
    private static final String delimiter = ".";
    /**
     * 是否把字段名称驼峰转下划线
     */
    private final boolean isToUnderlineCase;
    @Getter
    private final MyPage<T> page;
    @Getter
    private final U where;
    private final Map<Class<?>, String> tableAliasMap = new HashMap<>();

    public MyMPJLambdaWrapper() {
        this(null, true, false);
    }

    public MyMPJLambdaWrapper(U where) {
        this(where, true, true);
    }

    public MyMPJLambdaWrapper(U where, boolean buildQueryCondition) {
        this(where, true, buildQueryCondition);
    }

    public MyMPJLambdaWrapper(U where, boolean isToUnderlineCase, boolean buildQueryCondition) {
        this.where = where;
        //注：在构造时,有个写死的别名t,ConfigProperties类public static String tableAlias = "t";如有关联查询或走权限注解,请留意使用t别名
        this.isToUnderlineCase = isToUnderlineCase;
        if (buildQueryCondition)
            buildQueryCondition(null, where, true);
        page = pageParam();
    }

    public <R> MyPage<R> pageParam() {
        long page = (where == null || where.getPage() == null) ? 1L : where.getPage();
        long limit = (where == null || where.getLimit() == null) ? 20L : where.getLimit();
        return new MyPage<>(page, limit);
    }

    /**
     * 生成查询条件
     *
     * @param queryClass 查询表对应 Class
     * @param tableAlias 查询表别名
     * @param where      BaseParam
     */
    public void buildQueryCondition(Class<?> queryClass, String tableAlias, BaseParam where) {
        if (queryClass != null && !StringUtils.hasText(tableAlias))
            tableAlias = getTableAliasMap().get(queryClass);
        buildQueryCondition(tableAlias, where, false);
    }

    /**
     * 生成查询条件
     *
     * @param tableAlias 表别名
     * @param where      BaseParam
     */
    public void buildQueryCondition(String tableAlias, BaseParam where) {
        buildQueryCondition(tableAlias, where, false);
    }


    /**
     * 生成查询条件
     *
     * @param tableAlias 表别名
     * @param where      BaseParam
     * @param orderBy    排序
     */
    public void buildQueryCondition(String tableAlias, BaseParam where, boolean orderBy) {
        if (where == null) return;
        boolean encryptedTable = isEncryptedTable(where.getClass());
        Map<String, Object> map = BeanUtil.beanToMap(where, false, true);
        String alias = StringUtils.hasText(tableAlias) ? tableAlias : this.getAlias();
        alias = StringUtils.hasText(alias) ? alias + delimiter : "";
        for (String fieldName : map.keySet()) {
            Object fieldValue = map.get(fieldName);
            Field field = ReflectUtil.getField(where.getClass(), fieldName);
            // 过滤逻辑删除字段
            if (field.getAnnotation(TableLogic.class) != null) {
                continue;
            }

            // 获取注解指定的查询字段及查询方式
            QueryType queryType = QueryType.LIKE;
            QueryField queryField = field.getAnnotation(QueryField.class);
            if (queryField != null) {
                if (queryField.ignore()) continue;
                if (StrUtil.isNotEmpty(queryField.value())) {
                    fieldName = queryField.value();
                }
                if (queryField.type() != null) {
                    queryType = queryField.type();
                }
            } else {
                // 过滤非本表的字段
                TableField tableField = field.getAnnotation(TableField.class);
                if (tableField != null && !tableField.exist()) {
                    continue;
                }
            }

            // 字段名驼峰转下划线
            if (this.isToUnderlineCase) {
                fieldName = StrUtil.toUnderlineCase(fieldName);
            }

            fieldValue = getEncryptedValue(field, fieldValue, encryptedTable);

            buildQueryCondition(this, queryType, alias + fieldName, fieldValue);
        }

        if (orderBy && StringUtils.hasText(where.getSort())) {
            List<OrderItem> orderItems = MyBatisPlusUtil.orderItems(where.getSort(), null, true);
            for (OrderItem orderItem : orderItems) {
                String orderColumn = buildOrderColumn(alias, orderItem.getColumn());
                if (orderItem.isAsc()) {
                    this.orderByAsc(orderColumn);
                } else {
                    this.orderByDesc(orderColumn);
                }
            }
        }
    }

    private static Object getEncryptedValue(Field field, Object fieldValue, boolean encryptedTable) {
        if (field != null) {
            EncryptedColumn sensitiveField = field.getAnnotation(EncryptedColumn.class);
            if (fieldValue instanceof String && sensitiveField != null && encryptedTable) {
                try {
                    fieldValue = AESCryptoUtil.encrypt(String.valueOf(fieldValue));
                } catch (Exception e) {
                    log.error(e.getMessage(), e);
                }
            }
        }
        return fieldValue;
    }

    private static boolean isEncryptedTable(Class<?> queryClass) {
        EncryptedTable sensitiveData = AnnotationUtils.findAnnotation(queryClass, EncryptedTable.class);
        return sensitiveData != null;
    }

    private String buildOrderColumn(String alias, String column) {
        if (column.contains(delimiter)) return column;
        String regex = "\\(([^)]+)\\)";
        Pattern pattern = Pattern.compile(regex);
        Matcher matcher = pattern.matcher(column);
        if (matcher.find()) {
            String str = matcher.group(1);
            return column.replaceAll(str, alias + str);
        }
        return alias + column;
    }

    public void buildQueryCondition(MPJLambdaWrapper<T> wrapper, QueryType queryType, String fieldName, Object fieldValue, Class<?> queryClass) {
        boolean encryptedTable = isEncryptedTable(queryClass);
        if (encryptedTable) {
            Field field = CommonUtil.getField(queryClass, fieldName.contains(delimiter) ? fieldName.split("\\.")[1] : fieldName);
            fieldValue = getEncryptedValue(field, fieldValue, true);
        }
        buildQueryCondition(wrapper, queryType, fieldName, fieldValue);
    }

    private void buildQueryCondition(MPJLambdaWrapper<T> wrapper, QueryType queryType, String fieldName, Object fieldValue) {
        switch (queryType) {
            case EQ:
                wrapper.eq(fieldName, fieldValue);
                break;
            case NE:
                wrapper.ne(fieldName, fieldValue);
                break;
            case GT:
                wrapper.gt(fieldName, fieldValue);
                break;
            case GE:
                wrapper.ge(fieldName, fieldValue);
                break;
            case LT:
                wrapper.lt(fieldName, fieldValue);
                break;
            case LE:
                wrapper.le(fieldName, fieldValue);
                break;
            case LIKE:
                wrapper.like(fieldName, fieldValue);
                break;
            case NOT_LIKE:
                wrapper.notLike(fieldName, fieldValue);
                break;
            case LIKE_LEFT:
                wrapper.likeLeft(fieldName, fieldValue);
                break;
            case LIKE_RIGHT:
                wrapper.likeRight(fieldName, fieldValue);
                break;
            case IS_NULL:
                if (!Objects.isNull(fieldValue) && (Boolean) fieldValue)
                    wrapper.isNull(fieldName);
                break;
            case IS_NOT_NULL:
                if (!Objects.isNull(fieldValue) && (Boolean) fieldValue)
                    wrapper.isNotNull(fieldName);
                break;
            case IN:
                wrapper.in(fieldName, fieldValue);
                break;
            case NOT_IN:
                wrapper.notIn(fieldName, fieldValue);
                break;
            case IN_STR:
                if (fieldValue instanceof String)
                    wrapper.in(fieldName, Arrays.asList(((String) fieldValue).split(",")));
                break;
            case NOT_IN_STR:
                if (fieldValue instanceof String)
                    wrapper.notIn(fieldName, Arrays.asList(((String) fieldValue).split(",")));
                break;
        }
    }

    public Map<Class<?>, String> getTableAliasMap() {
        List<TableList.Node> nodeList = getTableList().getAll();
        if (CollectionUtils.isEmpty(tableAliasMap) || tableAliasMap.size() != nodeList.size()) {
            for (TableList.Node node : nodeList) {
                tableAliasMap.put(node.clazz, node.getAlias() + node.getIndex());
            }
        }
        return tableAliasMap;
    }

    public void setTableAlias(Class<?> clazz, String alias) {
        tableAliasMap.put(clazz, alias);
    }

    public MyMPJLambdaWrapper<T, U> selectColumn(String alias, String... columns) {
        if (!StringUtils.hasText(alias)) alias = this.getAlias();
        List<String> list = new ArrayList<>();
        for (String column : columns) {
            if (column.contains(delimiter)) {
                list.add(column);
                continue;
            }
            list.add(alias + delimiter + column);
        }
        super.select(list.toArray(new String[]{}));
        return this;
    }

    public <E> MyMPJLambdaWrapper<T, U> select(Collection<SFunction<E, ?>> collection) {
        if (CollectionUtils.isEmpty(collection)) return this;
        collection.forEach(sFunction -> super.select(sFunction));
        return this;
    }

    public <R> String getColumnWithAlias(SFunction<R, ?> sFunction) {
        Class<R> entityClass = LambdaUtils.getEntityClass(sFunction);
        String alias = getTableAliasMap().get(entityClass);
        String column = LambdaUtils.getName(sFunction);
        return StringUtils.hasText(alias) ? alias + delimiter + column : this.getAlias() + delimiter + column;
    }
}
