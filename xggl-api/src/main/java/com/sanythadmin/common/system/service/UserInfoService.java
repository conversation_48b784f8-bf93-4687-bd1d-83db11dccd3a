package com.sanythadmin.common.system.service;

import com.baomidou.mybatisplus.core.toolkit.support.SFunction;
import com.baomidou.mybatisplus.extension.service.IService;
import com.sanythadmin.common.core.mybatisplus.wrapper.MyMPJLambdaWrapper;
import com.sanythadmin.common.system.entity.UserInfo;
import com.sanythadmin.common.system.param.UserInfoParam;
import com.sanythadmin.project.code.param.CodeXsztParam;

import java.util.Collection;
import java.util.LinkedHashMap;
import java.util.List;

/**
 * 用户信息表Service
 *
 * <AUTHOR>
 * @since 2024-03-05 17:16:31
 */
public interface UserInfoService extends IService<UserInfo> {

    void saveUserInfo(UserInfo userInfo);

    void removeUserInfo(String xgh);

    void updateStatus(String username, String statusId);

    /**
     * 根据id查询
     *
     * @param xgh 学号/工号，对应账户表username
     * @return UserInfo
     */
    UserInfo get(String xgh);

    List<UserInfo> list(UserInfoParam param);

    List<UserInfo> list(UserInfoParam param, Collection<SFunction<UserInfo, ?>> sFunction);

    List<UserInfo> list(UserInfoParam param, CodeXsztParam codeXsztParam, Collection<SFunction<UserInfo, ?>> sFunction);

    List<UserInfo> listWithPermission(UserInfoParam param, Collection<SFunction<UserInfo, ?>> sFunction);

    /**
     * list查询
     *
     * @param param         用户参数
     * @param codeXsztParam 状态参数
     * @param sFunction     查询字段
     * @return
     */
    List<UserInfo> listWithPermission(UserInfoParam param, CodeXsztParam codeXsztParam, Collection<SFunction<UserInfo, ?>> sFunction);

    Long countWithPermission(UserInfoParam param, CodeXsztParam codeXsztParam);

    /**
     * 构建查询 wrapper
     *
     * @param param   用户参数
     * @param columns 查询字段
     * @return
     */
    MyMPJLambdaWrapper<UserInfo, UserInfoParam> getQueryWrapper(UserInfoParam param, String... columns);

    /**
     * 构建查询 wrapper
     *
     * @param param         用户参数
     * @param codeXsztParam 状态参数
     * @param columns       查询字段
     * @return
     */
    MyMPJLambdaWrapper<UserInfo, UserInfoParam> getQueryWrapper(UserInfoParam param, CodeXsztParam codeXsztParam, String... columns);

    /**
     * 构建查询 wrapper
     *
     * @param param         用户参数
     * @param codeXsztParam 状态参数
     * @param sFunction     查询字段
     * @return
     */
    MyMPJLambdaWrapper<UserInfo, UserInfoParam> getQueryWrapper(UserInfoParam param, CodeXsztParam codeXsztParam, Collection<SFunction<UserInfo, ?>> sFunction);

    public void createUserAndAccountInfo(UserInfo userInfo, boolean checkAccountInfo, String passwordPattern);

    public void createUserAndAccountInfo(UserInfo userInfo, boolean checkAccountInfo, String passwordPattern
            , List<LinkedHashMap> classInfos, List<LinkedHashMap> deptInfos);

    public void updateUserOrgMap(UserInfo userInfo);

    public void updateUserOrgMap(UserInfo userInfo, List<LinkedHashMap> classInfos, List<LinkedHashMap> deptInfos);
}
